using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;

namespace voice_processing_service.Tests.Simulators.WavProcessorSimulator
{
    class WavProcessorSimulator
    {
        static async Task Main(string[] args)
        {
            if (args.Length < 1)
            {
                Console.WriteLine("Usage: WavProcessorSimulator <output_wav_file>");
                return;
            }

            string outputWavFile = args[0];

            // Vytvoření loggeru
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Trace);
            });
            var bufferLogger = loggerFactory.CreateLogger<BlockingCollectionAudioBuffer>();
            var processorLogger = loggerFactory.CreateLogger<WavAudioProcessor>();

            // Vytvoření bufferu a procesoru
            using var buffer = new BlockingCollectionAudioBuffer(bufferLogger);
            using var processor = new WavAudioProcessor("test_call", outputWavFile, processorLogger);

            // Vytvoření CancellationTokenSource pro ukončení simulace
            using var cts = new CancellationTokenSource();
            cts.CancelAfter(TimeSpan.FromSeconds(10)); // Simulace bude běžet 10 sekund

            // Spuštění producenta a procesoru
            var producerTask = ProducerAsync(buffer, cts.Token);
            var processorTask = processor.StartProcessingAsync(buffer, cts.Token);

            // Čekání na ukončení simulace
            try
            {
                await Task.WhenAll(producerTask, processorTask);
                Console.WriteLine("Simulace úspěšně dokončena.");
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Simulace byla zrušena.");
            }
        }

        static async Task ProducerAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            try
            {
                var random = new Random();
                for (int i = 0; i < 500; i++) // Simulace 500 paketů (10 sekund při 20ms)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    // Vytvoření náhodných audio dat (G.711 µ-law, 20ms při 8kHz = 160 bytů)
                    var audioData = new byte[160];
                    random.NextBytes(audioData);

                    // Přidání dat do bufferu
                    buffer.Add(audioData);
                    Console.WriteLine($"Producent: Přidáno {audioData.Length} bytů do bufferu (paket {i+1}/500).");

                    // Simulace prodlevy mezi pakety (20ms)
                    await Task.Delay(20, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Producent: Zrušen.");
            }
            finally
            {
                buffer.CompleteAdding();
                Console.WriteLine("Producent: Ukončeno přidávání do bufferu.");
            }
        }
    }
}