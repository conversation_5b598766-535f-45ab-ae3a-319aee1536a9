using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using voice_processing_service.Services;
using voice_processing_service.Interfaces;
using voice_processing_service.Configuration;
using Xunit;

namespace voice_processing_service.Tests.Integration.Services
{
    public class WavAudioProcessorIntegrationTests
    {
        [Fact]
        public void BlockingCollection_AddingAndTakingData()
        {
            var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var data1 = new byte[] { 1, 2, 3 };
            var data2 = new byte[] { 4, 5 };

            buffer.Add(data1);
            buffer.Add(data2);

            Assert.True(buffer.TryTake(out var result1, 1000, CancellationToken.None));
            Assert.Equal(data1, result1);

            Assert.True(buffer.TryTake(out var result2, 1000, CancellationToken.None));
            Assert.Equal(data2, result2);

            buffer.Dispose();
        }

        [Fact]
        public void BlockingCollection_CompleteAddingAndIsCompleted()
        {
            var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());

            buffer.CompleteAdding();

            Assert.True(buffer.IsAddingCompleted);
            Assert.True(buffer.IsCompleted);
            Assert.False(buffer.TryTake(out _, 100, CancellationToken.None));

            buffer.Dispose();
        }

        [Fact]
        public async Task ProcessingAudioDataFromBuffer_WritesDataToWavFile()
        {
            var tempFile = Path.Combine(Path.GetTempPath(), $"int_{Guid.NewGuid()}.wav");
            using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var processor = new WavAudioProcessor("call_int1", tempFile, new NullLogger<WavAudioProcessor>());

            // Přidání vzorkových dat a dokončení bufferu
            var sample = new byte[] { 10, 20, 30, 40, 50 };
            buffer.Add(sample);
            buffer.CompleteAdding();

            // Zpracování bufferu
            await processor.StartProcessingAsync(buffer, CancellationToken.None);
            processor.Dispose();

            // Ověření zápisu dat po hlavičce WAV souboru
            Assert.True(File.Exists(tempFile));
            var fileData = File.ReadAllBytes(tempFile);
            var payload = fileData[44..(44 + sample.Length)];
            Assert.Equal(sample, payload);

            File.Delete(tempFile);
        }

        [Fact]
        public async Task StopProcessingAfterBufferEmpty_NoAdditionalDataWritten()
        {
            var tempFile = Path.Combine(Path.GetTempPath(), $"int_{Guid.NewGuid()}.wav");
            using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var processor = new WavAudioProcessor("call_int2", tempFile, new NullLogger<WavAudioProcessor>());

            // Dokončíme buffer bez dat
            buffer.CompleteAdding();

            await processor.StartProcessingAsync(buffer, CancellationToken.None);
            processor.Dispose();

            Assert.True(File.Exists(tempFile));
            var info = new FileInfo(tempFile);
            Assert.Equal(44, info.Length);

            File.Delete(tempFile);
        }

        [Fact]
        public async Task RotatesFileOnMaxDuration()
        {
            // Arrange
            var tempRoot = Path.Combine(Path.GetTempPath(), $"wavrot_{Guid.NewGuid():N}");
            Directory.CreateDirectory(tempRoot);

            var opts = Options.Create(new WavRecordingOptions
            {
                Enabled = true,
                Directory = tempRoot,
                FilenameTemplate = "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav",
                MaxFileDurationSeconds = 1
            });

            var factory = new WavAudioProcessorFactory(new NullLoggerFactory(), opts);
            using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var processor = factory.CreateProcessor("testcall");
            var wav = Assert.IsType<WavAudioProcessor>(processor);

            // Act: start processor without external cancellation; test drives completion via CompleteAdding
            var procTask = Task.Run(() => processor.StartProcessingAsync(buffer, CancellationToken.None));

            var frame = Enumerable.Repeat((byte)0x55, 160).ToArray(); // small RTP-sized frame
            buffer.Add(frame);

            // Wait until the internal writer is opened (avoids relying on filesystem timing)
            var waitSw = System.Diagnostics.Stopwatch.StartNew();
            var writerField = typeof(WavAudioProcessor).GetField("_wavWriter",
                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            Assert.NotNull(writerField);
            while (waitSw.Elapsed < TimeSpan.FromSeconds(2))
            {
                var writer = writerField!.GetValue(wav) as System.IO.BinaryWriter;
                if (writer != null) break;
                await Task.Delay(10);
            }

            // Force time-based rotation by moving _segmentOpenUtc into the past
            var segOpenField = typeof(WavAudioProcessor).GetField("_segmentOpenUtc",
                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            Assert.NotNull(segOpenField);
            segOpenField!.SetValue(wav, DateTime.UtcNow - TimeSpan.FromSeconds(2));

            // Sanity check: first segment file exists once writer is open
            var writerObj = writerField!.GetValue(wav) as System.IO.BinaryWriter;
            Assert.NotNull(writerObj);
            var fs = Assert.IsType<FileStream>(writerObj!.BaseStream);
            Assert.True(File.Exists(fs.Name), $"Expected first segment file to exist: {fs.Name}");

            // Next frame should trigger RotateIfNeeded() and create the next segment immediately
            buffer.Add(frame);

            buffer.CompleteAdding();
            await procTask;
            wav.Dispose();

            // Assert: two segments created and finalized (tolerate short FS lag)
            var swAssert = System.Diagnostics.Stopwatch.StartNew();
            string[] files = Array.Empty<string>();
            while (swAssert.Elapsed < TimeSpan.FromSeconds(2))
            {
                files = Directory.GetFiles(tempRoot, "*.wav", SearchOption.AllDirectories)
                    .OrderBy(f => f)
                    .ToArray();
                if (files.Length >= 2) break;
                await Task.Delay(20);
            }

            Assert.True(files.Length >= 2, $"Expected at least 2 wav files, found {files.Length}");
            Assert.Contains(files, f => Path.GetFileName(f).Contains("_seg-001"));
            Assert.Contains(files, f => Path.GetFileName(f).Contains("_seg-002"));

            // Validate WAV headers finalize correctly
            ValidateWav(files.First(f => Path.GetFileName(f).Contains("_seg-001")));
            ValidateWav(files.First(f => Path.GetFileName(f).Contains("_seg-002")));

            // Cleanup
            Directory.Delete(tempRoot, true);
        }

        [Fact]
        public async Task SplitsOnFormatChangeWhenEnabled()
        {
            // Arrange
            var tempRoot = Path.Combine(Path.GetTempPath(), $"wavfmt_{Guid.NewGuid():N}");
            Directory.CreateDirectory(tempRoot);

            var opts = Options.Create(new WavRecordingOptions
            {
                Enabled = true,
                Directory = tempRoot,
                FilenameTemplate = "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav",
                MaxFileDurationSeconds = 3600, // avoid time-based rotation
                SplitOnReinvite = true
            });

            var factory = new WavAudioProcessorFactory(new NullLoggerFactory(), opts);
            using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var processor = factory.CreateProcessor("testcall");
            var wav = Assert.IsType<WavAudioProcessor>(processor);

            // Act
            var procTask = Task.Run(() => processor.StartProcessingAsync(buffer, CancellationToken.None));

            var frame = Enumerable.Repeat((byte)0x33, 160).ToArray();
            buffer.Add(frame);

            // Trigger format change (e.g., switch μ-law -> A-law)
            wav.NotifyFormatChange(formatTag: 6, channels: 1, sampleRate: 8000, bitsPerSample: 8);

            buffer.Add(frame);
            buffer.CompleteAdding();
            await procTask;
            wav.Dispose();

            // Assert: segment index incremented
            var files = Directory.GetFiles(tempRoot, "*.wav", SearchOption.AllDirectories)
                .OrderBy(f => f)
                .ToArray();

            Assert.True(files.Length >= 2, $"Expected at least 2 wav files, found {files.Length}");
            Assert.Contains(files, f => Path.GetFileName(f).Contains("_seg-001"));
            Assert.Contains(files, f => Path.GetFileName(f).Contains("_seg-002"));

            // Validate headers
            ValidateWav(files.First(f => Path.GetFileName(f).Contains("_seg-001")));
            ValidateWav(files.First(f => Path.GetFileName(f).Contains("_seg-002")));

            // Cleanup
            Directory.Delete(tempRoot, true);
        }

[Fact]
        public async Task WritesToConfiguredDirectoryAndTemplate()
        {
            // Arrange
            var tempRoot = Path.Combine(Path.GetTempPath(), $"wavtpl_{Guid.NewGuid():N}");
            Directory.CreateDirectory(tempRoot);

            try
            {
                var opts = Options.Create(new WavRecordingOptions
                {
                    Enabled = true,
                    Directory = tempRoot,
                    FilenameTemplate = "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav",
                    MaxFileDurationSeconds = 3600 // avoid time-based rotation
                });

                var factory = new WavAudioProcessorFactory(new NullLoggerFactory(), opts);
                using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
                var processor = factory.CreateProcessor("testcall");
                var wav = Assert.IsType<WavAudioProcessor>(processor);

                // Act
                var procTask = Task.Run(() => processor.StartProcessingAsync(buffer, CancellationToken.None));
                var frame = Enumerable.Repeat((byte)0x22, 160).ToArray();
                buffer.Add(frame);
                buffer.CompleteAdding();
                await procTask;
                wav.Dispose();

                // Assert
                Assert.True(Directory.Exists(tempRoot));
                var utcToday = DateTime.UtcNow.ToString("yyyyMMdd");
                var expectedDayDir = Path.Combine(tempRoot, utcToday);
                Assert.True(Directory.Exists(expectedDayDir), $"Expected date subfolder to exist: {expectedDayDir}");

                // Exactly one file under tempRoot/utcToday
                var files = Directory.GetFiles(expectedDayDir, "*.wav", SearchOption.TopDirectoryOnly);
                Assert.Single(files);
                var path = files[0];

                // File name matches templating (callId + first segment)
                var fileName = Path.GetFileName(path);
                Assert.Contains("_call-testcall_", fileName);
                Assert.Contains("_seg-001", fileName);

                // Basic WAV validation and ensure payload was written (> header)
                ValidateWav(path);
                var len = new FileInfo(path).Length;
                Assert.True(len > 44, $"Expected some payload written. Length={len}");
            }
            finally
            {
                if (Directory.Exists(tempRoot))
                {
                    try { Directory.Delete(tempRoot, true); } catch { /* ignore */ }
                }
            }
        }

        [Fact]
        public async Task DoesNotRotateWhenDurationNotExceeded()
        {
            // Arrange
            var tempRoot = Path.Combine(Path.GetTempPath(), $"wavnorot_{Guid.NewGuid():N}");
            Directory.CreateDirectory(tempRoot);

            try
            {
                var opts = Options.Create(new WavRecordingOptions
                {
                    Enabled = true,
                    Directory = tempRoot,
                    FilenameTemplate = "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav",
                    MaxFileDurationSeconds = 60 // well above our short test duration
                });

                var factory = new WavAudioProcessorFactory(new NullLoggerFactory(), opts);
                using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
                var processor = factory.CreateProcessor("testcall");
                var wav = Assert.IsType<WavAudioProcessor>(processor);

                // Act
                var procTask = Task.Run(() => processor.StartProcessingAsync(buffer, CancellationToken.None));
                var frame = Enumerable.Repeat((byte)0x44, 160).ToArray();
                buffer.Add(frame);
                buffer.Add(frame);
                buffer.Add(frame);
                buffer.CompleteAdding();
                await procTask;
                wav.Dispose();

                // Assert: only one finalized WAV file produced (no rotation)
                var files = Directory.GetFiles(tempRoot, "*.wav", SearchOption.AllDirectories)
                    .OrderBy(f => f)
                    .ToArray();
                Assert.Single(files);
                Assert.Contains("_seg-001", Path.GetFileName(files[0]));
                ValidateWav(files[0]);
            }
            finally
            {
                if (Directory.Exists(tempRoot))
                {
                    try { Directory.Delete(tempRoot, true); } catch { /* ignore */ }
                }
            }
        }
        private static void ValidateWav(string path)
        {
            var bytes = File.ReadAllBytes(path);
            Assert.True(bytes.Length >= 44, "WAV file too small to contain header");

            // "RIFF"
            Assert.Equal(0x52, bytes[0]); // R
            Assert.Equal(0x49, bytes[1]); // I
            Assert.Equal(0x46, bytes[2]); // F
            Assert.Equal(0x46, bytes[3]); // F

            // "WAVE"
            Assert.Equal(0x57, bytes[8]);  // W
            Assert.Equal(0x41, bytes[9]);  // A
            Assert.Equal(0x56, bytes[10]); // V
            Assert.Equal(0x45, bytes[11]); // E

            // ChunkSize at offset 4
            uint chunkSize = BitConverter.ToUInt32(bytes, 4);
            Assert.Equal(bytes.Length - 8, (int)chunkSize);

            // data chunk size at offset 40
            uint dataSize = BitConverter.ToUInt32(bytes, 40);
            Assert.Equal(bytes.Length - 44, (int)dataSize);
        }
    }
}