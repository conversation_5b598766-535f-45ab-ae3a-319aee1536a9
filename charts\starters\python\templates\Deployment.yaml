apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
  labels:
    {{- include "initial.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  revisionHistoryLimit: 1
  {{- end }}
  selector:
    matchLabels:
      {{- include "initial.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
      	 {{- if .Values.podReloader.enabled }}
         checksum/config: {{ include (print $.Template.BasePath "/" $.Values.podReloader.filename) $ | sha256sum }}
         {{- end }}
      labels:
        {{- include "initial.selectorLabels" . | nindent 8 }}
    spec:
      imagePullSecrets:
        - name: {{ .Release.Name }}-gitlab-dockerconfig
      serviceAccountName: {{ include "initial.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Release.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/deployments/{{ .Chart.Name }}/{{ .Values.image.name  }}:{{ .Chart.Version }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: HTTP_PROXY
              value: ""
            - name: HTTPS_PROXY
              value: ""
          envFrom:
              - configMapRef:
                  name: {{ .Release.Name }}-ldap
              - secretRef:
                  name: {{ .Release.Name }}-ldap
          command:
            - "/bin/bash"
            - "-c"
            - "python3 main.py"
          ports:
            - name: {{ .Values.service.port }}
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          readinessProbe:
            tcpSocket:
              port: {{ .Values.service.port }}
            initialDelaySeconds: 15
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: {{ .Values.service.port }}
            initialDelaySeconds: 15
            periodSeconds: 10
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            {{- if .Values.persistentVolumeClaim.enabled }}
            - name: persistent-data
              mountPath: /opt/data
            {{- end }}
            - name: ephemeral-data
              mountPath: /opt/temp
            - name: html
              mountPath: /opt/static/index.html
              subPath: index.html
            - name: ca
              mountPath: /opt/o2czca.crt
              subPath: o2czca.crt
      volumes:
        - name: ephemeral-data
          emptyDir: { }
        {{- if .Values.persistentVolumeClaim.enabled }}
        - name: persistent-data
          persistentVolumeClaim:
            claimName: {{ .Release.Name }}
        {{- end }}
        - name: html
          configMap:
            name: {{ .Release.Name }}-html
            items:
              - key: index.html
                path: index.html
        - name: ca
          configMap:
            name: {{ .Release.Name }}-o2czca
            items:
              - key: o2czca.crt
                path: o2czca.crt
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
