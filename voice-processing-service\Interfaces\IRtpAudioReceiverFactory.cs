using System.Net;
using SIPSorceryMedia.Abstractions;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Factory for creating RTP audio receivers with allocated ports.
    /// </summary>
    public interface IRtpAudioReceiverFactory
    {
        /// <summary>
        /// Creates a new audio input receiver bound to the local address with dynamic port allocation.
        /// </summary>
        /// <param name="callId">The call identifier for logging purposes.</param>
        /// <param name="localAddress">The local IP address to bind to.</param>
        /// <returns>An instance of IAudioInputReceiver.</returns>
        IAudioInputReceiver CreateReceiver(string callId, IPAddress localAddress);

        /// <summary>
        /// Creates a new audio input receiver bound to optional RTP/RTCP ports.
        /// </summary>
        /// <param name="callId">The call identifier for logging purposes.</param>
        /// <param name="rtpPort">The RTP port to bind to, or null to use default port.</param>
        /// <param name="rtcpPort">The RTCP port to bind to, or null to use default port.</param>
        /// <returns>An instance of IAudioInputReceiver.</returns>
        IAudioInputReceiver CreateReceiver(string callId, int? rtpPort = null, int? rtcpPort = null);
    }
}