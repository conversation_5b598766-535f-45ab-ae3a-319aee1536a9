using System;
using System.Threading;
using System.Threading.Tasks;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Net;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro session jednoho hovoru.
    /// </summary>
    public interface ICallSession : IDisposable
    {
        /// <summary>
        /// Identifikátor hovoru (Call-ID).
        /// </summary>
        string CallId { get; }

        /// <summary>
        /// SIP User Agent pro tento hovor.
        /// </summary>
        SIPServerUserAgent UserAgent { get; }

        /// <summary>
        /// Původní INVITE požadavek, který zahájil hovor.
        /// </summary>
        SIPRequest InitialInviteRequest { get; }

        /// <summary>
        /// Přijímač audio dat.
        /// </summary>
        IAudioInputReceiver AudioReceiver { get; }

        /// <summary>
        /// Čas zahájení hovoru.
        /// </summary>
        DateTime StartTime { get; }

        /// <summary>
        /// Spustí session (zahájí příjem a zpracování audio dat).
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task StartAsync(CancellationToken cancellationToken);

        /// <summary>
        /// Zastaví session (ukončí příjem a zpracování audio dat).
        /// </summary>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task StopAsync();

        /// <summary>
        /// Aktualizuje parametry session na základě re-INVITE požadavku.
        /// Umožňuje změnu media parametrů bez vytvoření nové session.
        /// </summary>
        /// <param name="reInviteRequest">Re-INVITE SIP požadavek s novými parametry.</param>
        /// <param name="newSdpOffer">Nová SDP nabídka z re-INVITE.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task UpdateSessionAsync(SIPRequest reInviteRequest, SDP newSdpOffer);

        /// <summary>
        /// Získá aktuální RTP port používaný session.
        /// </summary>
        int CurrentRtpPort { get; }

        /// <summary>
        /// Získá aktuální RTCP port používaný session.
        /// </summary>
        int CurrentRtcpPort { get; }
    }
}
