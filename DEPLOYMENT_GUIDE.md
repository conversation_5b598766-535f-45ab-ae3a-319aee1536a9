# Voice Processing Service - Deployment Guide

## Updated Configuration Summary

### ✅ **New Default Ports:**
- **SIP Signaling**: `5090/udp` (changed from 5060)
- **HTTP API**: `8081/tcp` (changed from 8080)
- **RTP Media**: `10000-19998/udp` (unchanged)

### ✅ **Early Version Numbering:**
- Starting with `0.1` for early testing phase
- Format: `0.x`, `0.x.y`, `0.x-alpha`, `0.x.y-beta`

---

## Windows Development Environment (Docker Desktop + PowerShell)

### **Build Image (Manual - Monitor Progress):**
```powershell
# Build with version tag (you can monitor progress)
docker build -t voice-processing-service:0.1 ./voice-processing-service

# Tag as latest (optional)
docker tag voice-processing-service:0.1 voice-processing-service:latest

# Save as tar file for RHEL transfer
docker save voice-processing-service:0.1 -o voice-processing-service-0.1.tar
```

### **Test Locally:**
```powershell
# Create data directories
mkdir -p data/logs, data/recordings

# Run container with log mapping and Phonexia URL
docker run -d --name voice-processing-service -p 5090:5090/udp -p 10000-10100:10000-10100/udp -p 8081:8081 -v ${PWD}/data/logs:/app/logs -v ${PWD}/data/recordings:/app/recordings -e PHONEXIA_API_URL=http://localhost:8600 voice-processing-service:0.1

# View real-time logs (should show startup messages and SIP activity)
docker logs -f voice-processing-service

# Health check
curl http://localhost:8081/health

# Stop and remove
docker stop voice-processing-service
docker rm voice-processing-service
```

---

## RHEL9 Testing Environment (Podman)

### **Transfer and Load Image:**
```bash
# Transfer tar file to RHEL9 server
scp voice-processing-service-0.1.tar user@rhel9-server:/tmp/

# On RHEL9 server - load image
podman load -i /tmp/voice-processing-service-0.1.tar

# Verify image loaded
podman images | grep voice-processing-service
```

### **Run Container (Fresh Command):**
```bash
# Create data directories
mkdir -p /data/logs /data/recordings

# Run container with log mapping and Phonexia URL (no --restart flag)
podman run -d --name voice-processing-service -p 5090:5090/udp -p 10000-10100:10000-10100/udp -p 8081:8081 -v /data/logs:/app/logs -v /data/recordings:/app/recordings -e PHONEXIA_API_URL=http://your-phonexia-server:8600 voice-processing-service:0.1
```

### **Management Commands:**
```bash
# Check container status
podman ps

# Health check
curl http://localhost:8081/health

# View logs (should show startup messages and SIP activity)
podman logs voice-processing-service

# Follow logs in real-time (recommended for monitoring)
podman logs -f voice-processing-service

# Check log files on host (mapped volume)
tail -f /data/logs/voice-processing-service-*.log

# Stop container
podman stop voice-processing-service

# Remove container
podman rm voice-processing-service

# Remove image (if needed)
podman rmi voice-processing-service:0.1
```

---

## Configuration Without Rebuilding

### **Environment Variables (No Rebuild Required):**
```bash
# Example with custom Phonexia settings
podman run -d --name voice-processing-service -p 5090:5090/udp -p 10000-10100:10000-10100/udp -p 8081:8081 -v /data/logs:/app/logs -v /data/recordings:/app/recordings -e PHONEXIA_API_URL=http://phonexia.company.com:8600 -e PHONEXIA_USERNAME=production_user -e PHONEXIA_PASSWORD=secure_password -e PHONEXIA_LANGUAGE=en-US voice-processing-service:0.1
```

### **Available Environment Variables:**
- `PHONEXIA_API_URL` - Phonexia server URL
- `PHONEXIA_USERNAME` - Phonexia username
- `PHONEXIA_PASSWORD` - Phonexia password
- `PHONEXIA_LANGUAGE` - Language (cs-CZ, en-US, etc.)
- `PHONEXIA_MODEL` - Speech model
- `LOG_PATH` - Log directory path
- `RECORDINGS_PATH` - Recordings directory path

---

## Port Mapping Explanation

### **✅ Correct Port Mapping:**
```bash
# This works because internal and external ports match
-p 5090:5090/udp    # Host port 5090 → Container port 5090
-p 8081:8081        # Host port 8081 → Container port 8081
```

### **❌ Why This Doesn't Work:**
```bash
# This fails because app binds to 5090 inside container, not 5060
-p 5090:5060/udp    # Host port 5090 → Container port 5060 (WRONG!)
```

---

## Health Monitoring

### **Health Check Endpoints:**
```bash
# Basic health status
curl http://localhost:8081/health

# Detailed health with SIP registrations
curl http://localhost:8081/health/detailed
```

### **Expected Response:**
```json
{
  "Status": "Healthy",
  "Timestamp": "2025-01-16T10:30:00Z",
  "Version": "*******",
  "ActiveRegistrations": 1,
  "Environment": "Production"
}
```

---

## Troubleshooting

### **Common Issues:**
1. **Port conflicts**: Check if ports 5090, 8081 are free
2. **Permission issues**: Ensure volume directories exist and are writable
3. **Phonexia connectivity**: Verify API URL and credentials

### **Debug Commands:**
```bash
# Check container logs
podman logs voice-processing-service

# Check port bindings
podman port voice-processing-service

# Execute shell inside container
podman exec -it voice-processing-service /bin/bash

# Check container resource usage
podman stats voice-processing-service
```

---

## Version Management

### **Recommended Versioning for Testing Phase:**
- `0.1` - Initial testing version
- `0.2` - Bug fixes and minor improvements
- `0.3-alpha` - New features in testing
- `0.4.1-beta` - Release candidate

### **Build Commands:**
```powershell
# Windows PowerShell
.\build-versioned.ps1 -Version 0.1
.\build-versioned.ps1 -Version 0.2 -SaveTar
.\build-versioned.ps1 -Version 0.3-alpha -NoLatest
```

This setup ensures no environment variables are needed for basic operation, proper version tracking, and seamless deployment between Windows development and RHEL9 testing environments.
