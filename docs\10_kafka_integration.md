# Integrace s Kafka brokerem

## Popis úkolu

Tento úkol navazuje na krok 9 (implementace online transkripce pomocí Phonexia API) a zahrnuje:

1. Revizi řešení pro zpracování custom klíčů z SIP INVITE hlavičky (GEN_CONNID, GEN_AGENT_ID, GEN_CUSTOMER_NUMBER, GET_CHANNEL_TYPE)
2. Implementaci odesílání transkripce do Kafka brokeru

Výsledky transkripce budou odesílány do Kafka brokeru ve formě JSON struktury, která bude obsahovat:
- Custom klíče z SIP INVITE hlavičky
- <PERSON><PERSON><PERSON><PERSON> razítko
- Text transkripce
- Data ohledně jistoty přepsaného textu

Konfigurace Kafka brokeru bude načítána z proměnných prostředí (URLs brokers, TopicName).

## Technické detaily

### 1. Rozšíření modelu CallSession

Třída `CallSession` bude rozšířena o nové vlastnosti pro ukládání custom klíčů z SIP INVITE hlavičky:

```csharp
/// <summary>
/// Connection ID z SIP INVITE hlavičky.
/// </summary>
public string ConnectionId { get; }

/// <summary>
/// Agent ID z SIP INVITE hlavičky.
/// </summary>
public string AgentId { get; }

/// <summary>
/// Číslo zákazníka z SIP INVITE hlavičky.
/// </summary>
public string CustomerNumber { get; }

/// <summary>
/// Typ kanálu z SIP INVITE hlavičky.
/// </summary>
public string ChannelType { get; }
```

### 2. Rozšíření konfigurace

Bude vytvořena nová konfigurační třída `KafkaOptions` pro konfiguraci Kafka brokeru:

```csharp
/// <summary>
/// Konfigurace pro Kafka broker.
/// </summary>
public class KafkaOptions
{
    /// <summary>
    /// Seznam URL adres Kafka brokerů oddělených čárkou.
    /// </summary>
    public string BootstrapServers { get; set; }

    /// <summary>
    /// Název Kafka topicu pro odesílání transkripce.
    /// </summary>
    public string TopicName { get; set; }

    /// <summary>
    /// Skupina konzumentů.
    /// </summary>
    public string ConsumerGroupId { get; set; } = "voice-processing-service";
}
```

### 3. Implementace Kafka producenta

Bude vytvořena nová třída `KafkaProducer` pro odesílání zpráv do Kafka brokeru:

```csharp
/// <summary>
/// Třída pro odesílání zpráv do Kafka brokeru.
/// </summary>
public class KafkaProducer : IDisposable
{
    private readonly IProducer<string, string> _producer;
    private readonly string _topicName;
    private readonly ILogger _logger;

    /// <summary>
    /// Vytvoří novou instanci KafkaProducer.
    /// </summary>
    /// <param name="options">Konfigurace Kafka brokeru.</param>
    /// <param name="logger">Logger.</param>
    public KafkaProducer(IOptions<KafkaOptions> options, ILogger<KafkaProducer> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        var config = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _topicName = config.TopicName ?? throw new ArgumentException("TopicName must be specified", nameof(options));

        var producerConfig = new ProducerConfig
        {
            BootstrapServers = config.BootstrapServers,
            Acks = Acks.All
        };

        _producer = new ProducerBuilder<string, string>(producerConfig).Build();
        _logger.LogInformation($"KafkaProducer created for topic {_topicName}");
    }

    /// <summary>
    /// Odešle zprávu do Kafka brokeru.
    /// </summary>
    /// <param name="key">Klíč zprávy.</param>
    /// <param name="value">Hodnota zprávy.</param>
    /// <param name="cancellationToken">Token pro zrušení operace.</param>
    /// <returns>Task reprezentující asynchronní operaci.</returns>
    public async Task ProduceAsync(string key, string value, CancellationToken cancellationToken)
    {
        try
        {
            var message = new Message<string, string>
            {
                Key = key,
                Value = value
            };

            var deliveryResult = await _producer.ProduceAsync(_topicName, message, cancellationToken);
            _logger.LogDebug($"Message delivered to {deliveryResult.TopicPartitionOffset}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error producing message to Kafka topic {_topicName}");
            throw;
        }
    }

    /// <summary>
    /// Uvolní prostředky.
    /// </summary>
    public void Dispose()
    {
        _producer?.Dispose();
    }
}
```

### 4. Implementace modelu pro transkripci

Bude vytvořen nový model `TranscriptionMessage` pro odesílání transkripce do Kafka brokeru:

```csharp
/// <summary>
/// Model pro zprávu s transkripcí.
/// </summary>
public class TranscriptionMessage
{
    /// <summary>
    /// Connection ID z SIP INVITE hlavičky.
    /// </summary>
    [JsonPropertyName("connection_id")]
    public string ConnectionId { get; set; }

    /// <summary>
    /// Agent ID z SIP INVITE hlavičky.
    /// </summary>
    [JsonPropertyName("agent_id")]
    public string AgentId { get; set; }

    /// <summary>
    /// Číslo zákazníka z SIP INVITE hlavičky.
    /// </summary>
    [JsonPropertyName("customer_number")]
    public string CustomerNumber { get; set; }

    /// <summary>
    /// Typ kanálu z SIP INVITE hlavičky.
    /// </summary>
    [JsonPropertyName("channel_type")]
    public string ChannelType { get; set; }

    /// <summary>
    /// Časové razítko.
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Text transkripce.
    /// </summary>
    [JsonPropertyName("text")]
    public string Text { get; set; }

    /// <summary>
    /// Jistota přepsaného textu (0-1).
    /// </summary>
    [JsonPropertyName("confidence")]
    public double Confidence { get; set; }

    /// <summary>
    /// Čas začátku transkripce.
    /// </summary>
    [JsonPropertyName("start_time")]
    public double StartTime { get; set; }

    /// <summary>
    /// Čas konce transkripce.
    /// </summary>
    [JsonPropertyName("end_time")]
    public double EndTime { get; set; }
}
```

### 5. Rozšíření PhonexiaSttProcessor

Třída `PhonexiaSttProcessor` bude rozšířena o:

1. Nové vlastnosti pro ukládání custom klíčů z SIP INVITE hlavičky
2. Referenci na `KafkaProducer`
3. Metodu pro odesílání transkripce do Kafka brokeru

```csharp
/// <summary>
/// Connection ID z SIP INVITE hlavičky.
/// </summary>
private readonly string _connectionId;

/// <summary>
/// Agent ID z SIP INVITE hlavičky.
/// </summary>
private readonly string _agentId;

/// <summary>
/// Číslo zákazníka z SIP INVITE hlavičky.
/// </summary>
private readonly string _customerNumber;

/// <summary>
/// Typ kanálu z SIP INVITE hlavičky.
/// </summary>
private readonly string _channelType;

/// <summary>
/// Kafka producent.
/// </summary>
private readonly KafkaProducer _kafkaProducer;
```

Metoda `ProcessTranscriptionResult` bude upravena tak, aby odesílala transkripci do Kafka brokeru:

```csharp
/// <summary>
/// Zpracování výsledků transkripce.
/// </summary>
/// <param name="message">JSON zpráva s výsledky.</param>
private void ProcessTranscriptionResult(string message)
{
    try
    {
        var sttData = JsonSerializer.Deserialize<PhonexiaSttWebSocketResponse>(message);
        
        if (sttData.Result.OneBestResult != null)
        {
            foreach (var segment in sttData.Result.OneBestResult.Segmentation)
            {
                if (!string.IsNullOrEmpty(segment.Word) && !segment.Word.StartsWith("<"))
                {
                    Console.WriteLine($"Transcribed Text: {segment.Word}");
                    
                    // Odeslání transkripce do Kafka brokeru
                    SendTranscriptionToKafka(segment);
                }
            }
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"[{_callId}] Error processing transcription result.");
    }
}

/// <summary>
/// Odeslání transkripce do Kafka brokeru.
/// </summary>
/// <param name="segment">Segment transkripce.</param>
private async void SendTranscriptionToKafka(PhonexiaSegment segment)
{
    try
    {
        var transcriptionMessage = new TranscriptionMessage
        {
            ConnectionId = _connectionId,
            AgentId = _agentId,
            CustomerNumber = _customerNumber,
            ChannelType = _channelType,
            Timestamp = DateTime.UtcNow,
            Text = segment.Word,
            Confidence = segment.Confidence,
            StartTime = segment.Start,
            EndTime = segment.End
        };

        var json = JsonSerializer.Serialize(transcriptionMessage);
        await _kafkaProducer.ProduceAsync(_callId, json, CancellationToken.None);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"[{_callId}] Error sending transcription to Kafka.");
    }
}
```

### 6. Aktualizace CallSessionManager

Třída `CallSessionManager` bude upravena tak, aby extrahovala custom klíče z SIP INVITE hlavičky a předala je do `PhonexiaSttProcessor`:

```csharp
/// <summary>
/// Vytvoří novou session pro hovor.
/// </summary>
/// <param name="userAgent">SIP User Agent pro tento hovor.</param>
/// <param name="inviteRequest">INVITE požadavek, který zahájil hovor.</param>
/// <param name="inputReceiverFactory">Tovární metoda pro vytvoření přijímače audio dat.</param>
/// <param name="audioProcessorFactory">Tovární metoda pro vytvoření procesoru audio dat.</param>
/// <returns>Task reprezentující asynchronní operaci, která vrací vytvořenou session.</returns>
public Task<ICallSession> CreateSessionAsync(
    SIPUserAgent userAgent,
    SIPRequest inviteRequest,
    Func<IAudioInputReceiver> inputReceiverFactory,
    Func<string, string, string, string, IAudioProcessor> audioProcessorFactory)
{
    var callId = inviteRequest.Header.CallId;
    _logger.LogInformation($"Creating session for call {callId}");

    // Extrakce custom klíčů z SIP INVITE hlavičky
    var connectionId = ExtractHeaderValue(inviteRequest, "GEN_CONNID");
    var agentId = ExtractHeaderValue(inviteRequest, "GEN_AGENT_ID");
    var customerNumber = ExtractHeaderValue(inviteRequest, "GEN_CUSTOMER_NUMBER");
    var channelType = ExtractHeaderValue(inviteRequest, "GET_CHANNEL_TYPE");

    _logger.LogInformation($"Call {callId} - ConnectionId: {connectionId}, AgentId: {agentId}, CustomerNumber: {customerNumber}, ChannelType: {channelType}");

    // Vytvoření komponent
    var audioReceiver = inputReceiverFactory();
    var audioBuffer = new BlockingCollectionAudioBuffer(_loggerFactory.CreateLogger<BlockingCollectionAudioBuffer>());
    var audioProcessor = audioProcessorFactory(callId, connectionId, agentId, customerNumber, channelType);

    // Vytvoření session
    var session = new CallSession(
        callId,
        userAgent,
        inviteRequest,
        audioReceiver,
        audioBuffer,
        audioProcessor,
        async () => await TerminateSessionAsync(callId),
        _loggerFactory.CreateLogger<CallSession>());

    // Přidání session do slovníku
    _sessions.TryAdd(callId, session);

    return Task.FromResult<ICallSession>(session);
}

/// <summary>
/// Extrahuje hodnotu z SIP hlavičky.
/// </summary>
/// <param name="request">SIP požadavek.</param>
/// <param name="headerName">Název hlavičky.</param>
/// <returns>Hodnota hlavičky nebo prázdný řetězec, pokud hlavička neexistuje.</returns>
private string ExtractHeaderValue(SIPRequest request, string headerName)
{
    if (request.Header.UnknownHeaders != null && 
        request.Header.UnknownHeaders.TryGetValue(headerName, out var headerValue))
    {
        return headerValue;
    }
    return string.Empty;
}
```

### 7. Aktualizace PhonexiaSttProcessorFactory

Třída `PhonexiaSttProcessorFactory` bude upravena tak, aby přijímala custom klíče z SIP INVITE hlavičky:

```csharp
/// <summary>
/// Vytvoří novou instanci PhonexiaSttProcessor.
/// </summary>
/// <param name="callId">Identifikátor hovoru pro logování.</param>
/// <param name="connectionId">Connection ID z SIP INVITE hlavičky.</param>
/// <param name="agentId">Agent ID z SIP INVITE hlavičky.</param>
/// <param name="customerNumber">Číslo zákazníka z SIP INVITE hlavičky.</param>
/// <param name="channelType">Typ kanálu z SIP INVITE hlavičky.</param>
/// <returns>Instance IAudioProcessor.</returns>
public IAudioProcessor CreateProcessor(
    string callId,
    string connectionId,
    string agentId,
    string customerNumber,
    string channelType)
{
    var httpClient = _httpClientFactory.CreateClient("PhonexiaApi");
    var logger = _loggerFactory.CreateLogger<PhonexiaSttProcessor>();
    var kafkaProducer = _serviceProvider.GetRequiredService<KafkaProducer>();
    
    return new PhonexiaSttProcessor(
        callId,
        connectionId,
        agentId,
        customerNumber,
        channelType,
        _options,
        httpClient,
        kafkaProducer,
        logger);
}
```

### 8. Registrace služeb v DI kontejneru

V souboru `Program.cs` budou registrovány nové služby:

```csharp
// Konfigurace
builder.Services.Configure<KafkaOptions>(
    builder.Configuration.GetSection("Kafka"));

// Registrace Kafka producenta
builder.Services.AddSingleton<KafkaProducer>();

// Registrace továrny pro audio procesor
builder.Services.AddTransient<Func<string, string, string, string, string, IAudioProcessor>>(sp =>
{
    return (callId, connectionId, agentId, customerNumber, channelType) =>
    {
        var factory = sp.GetRequiredService<PhonexiaSttProcessorFactory>();
        return factory.CreateProcessor(callId, connectionId, agentId, customerNumber, channelType);
    };
});
```

### 9. Aktualizace appsettings.json

Soubor `appsettings.json` bude rozšířen o konfiguraci pro Kafka broker:

```json
"Kafka": {
  "BootstrapServers": "localhost:9092",
  "TopicName": "voice-transcription",
  "ConsumerGroupId": "voice-processing-service"
}
```

## Implementační kroky

1. Přidání NuGet balíčku Confluent.Kafka
2. Vytvoření konfigurace pro Kafka broker
3. Implementace KafkaProducer
4. Implementace modelu TranscriptionMessage
5. Rozšíření PhonexiaSttProcessor o odesílání transkripce do Kafka brokeru
6. Aktualizace CallSessionManager pro extrakci custom klíčů z SIP INVITE hlavičky
7. Aktualizace PhonexiaSttProcessorFactory
8. Registrace služeb v DI kontejneru
9. Aktualizace appsettings.json

## Testovací scénáře

### Unit testy pro KafkaProducer

1. **Test konstruktoru**
   - Ověřit, že instance KafkaProducer je vytvořena bez chyb
   - Ověřit, že všechny závislosti jsou správně nastaveny

2. **Test metody ProduceAsync**
   - Ověřit, že metoda ProduceAsync odešle zprávu do Kafka brokeru
   - Ověřit, že metoda ProduceAsync zpracuje chyby

### Unit testy pro CallSessionManager

1. **Test extrakce custom klíčů z SIP INVITE hlavičky**
   - Ověřit, že metoda ExtractHeaderValue správně extrahuje hodnoty z SIP hlavičky
   - Ověřit, že metoda ExtractHeaderValue vrátí prázdný řetězec, pokud hlavička neexistuje

### Integrační testy

1. **Test end-to-end**
   - Vytvořit SIP INVITE požadavek s custom klíči
   - Vytvořit session a spustit zpracování
   - Ověřit, že transkripce je odeslána do Kafka brokeru

## Závěr

Implementace integrace s Kafka brokerem umožní odesílat výsledky transkripce do Kafka brokeru, což umožní další zpracování a analýzu transkripce v reálném čase. Zároveň bude implementována podpora pro zpracování custom klíčů z SIP INVITE hlavičky, které budou součástí odesílaných zpráv.
