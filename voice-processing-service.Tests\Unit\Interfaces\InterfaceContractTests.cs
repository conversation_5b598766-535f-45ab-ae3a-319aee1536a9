using System.Reflection;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Tests.Unit.Interfaces;

/// <summary>
/// Tests to verify that interfaces are properly defined according to Phase 01 specifications.
/// These tests ensure that interface contracts match the documentation requirements.
/// </summary>
public class InterfaceContractTests
{
    [Fact]
    public void IAudioBuffer_ShouldBeProperlyDefined()
    {
        // Arrange
        var interfaceType = typeof(IAudioBuffer);

        // Assert - Interface should exist and inherit from IDisposable
        interfaceType.IsInterface.Should().BeTrue("because IAudioBuffer should be an interface");
        interfaceType.Should().BeAssignableTo<IDisposable>("because IAudioBuffer should inherit from IDisposable");

        // Assert - Required methods should exist
        interfaceType.Should().HaveMethod("Add", new[] { typeof(byte[]) })
            .Which.ReturnType.Should().Be(typeof(void), "because Add method should return void");

        interfaceType.Should().HaveMethod("TryTake", new[] { typeof(byte[]).MakeByRefType(), typeof(int), typeof(CancellationToken) })
            .Which.ReturnType.Should().Be(typeof(bool), "because TryTake method should return bool");

        interfaceType.Should().HaveMethod("CompleteAdding", Array.Empty<Type>())
            .Which.ReturnType.Should().Be(typeof(void), "because CompleteAdding method should return void");

        // Assert - Required properties should exist
        interfaceType.Should().HaveProperty(typeof(bool), "IsAddingCompleted")
            .Which.PropertyType.Should().Be(typeof(bool), "because IsAddingCompleted should be bool");

        interfaceType.Should().HaveProperty(typeof(bool), "IsCompleted")
            .Which.PropertyType.Should().Be(typeof(bool), "because IsCompleted should be bool");
    }

    [Fact]
    public void IAudioInputReceiver_ShouldBeProperlyDefined()
    {
        // Arrange
        var interfaceType = typeof(IAudioInputReceiver);

        // Assert - Interface should exist and inherit from IDisposable
        interfaceType.IsInterface.Should().BeTrue("because IAudioInputReceiver should be an interface");
        interfaceType.Should().BeAssignableTo<IDisposable>("because IAudioInputReceiver should inherit from IDisposable");

        // Assert - Required methods should exist
        interfaceType.Should().HaveMethod("StartListeningAsync", new[] { typeof(IAudioBuffer), typeof(CancellationToken) })
            .Which.ReturnType.Should().Be(typeof(Task), "because StartListeningAsync method should return Task");

        // Assert - Required properties should exist
        var rtpEndPointProperty = interfaceType.GetProperty("RtpLocalEndPoint");
        rtpEndPointProperty.Should().NotBeNull("because RtpLocalEndPoint property should exist");

        var rtcpEndPointProperty = interfaceType.GetProperty("RtcpLocalEndPoint");
        rtcpEndPointProperty.Should().NotBeNull("because RtcpLocalEndPoint property should exist");
    }

    [Fact]
    public void IAudioProcessor_ShouldBeProperlyDefined()
    {
        // Arrange
        var interfaceType = typeof(IAudioProcessor);

        // Assert - Interface should exist and inherit from IDisposable
        interfaceType.IsInterface.Should().BeTrue("because IAudioProcessor should be an interface");
        interfaceType.Should().BeAssignableTo<IDisposable>("because IAudioProcessor should inherit from IDisposable");

        // Assert - Required methods should exist
        interfaceType.Should().HaveMethod("StartProcessingAsync", new[] { typeof(IAudioBuffer), typeof(CancellationToken) })
            .Which.ReturnType.Should().Be(typeof(Task), "because StartProcessingAsync method should return Task");

        // Assert - Required properties should exist
        interfaceType.Should().HaveProperty(typeof(string), "ProcessorId")
            .Which.PropertyType.Should().Be(typeof(string), "because ProcessorId should be string");
    }

    [Fact]
    public void ICallSession_ShouldBeProperlyDefined()
    {
        // Arrange
        var interfaceType = typeof(ICallSession);

        // Assert - Interface should exist and inherit from IDisposable
        interfaceType.IsInterface.Should().BeTrue("because ICallSession should be an interface");
        interfaceType.Should().BeAssignableTo<IDisposable>("because ICallSession should inherit from IDisposable");

        // Assert - Required methods should exist
        interfaceType.Should().HaveMethod("StartAsync", new[] { typeof(CancellationToken) })
            .Which.ReturnType.Should().Be(typeof(Task), "because StartAsync method should return Task");

        interfaceType.Should().HaveMethod("StopAsync", Array.Empty<Type>())
            .Which.ReturnType.Should().Be(typeof(Task), "because StopAsync method should return Task");

        // Assert - Required properties should exist
        interfaceType.Should().HaveProperty(typeof(string), "CallId")
            .Which.PropertyType.Should().Be(typeof(string), "because CallId should be string");

        interfaceType.Should().HaveProperty(typeof(DateTime), "StartTime")
            .Which.PropertyType.Should().Be(typeof(DateTime), "because StartTime should be DateTime");

        // Note: UserAgent and InitialInviteRequest properties require SIPSorcery types
        // We verify they exist but don't test their types to avoid tight coupling in tests
        interfaceType.GetProperty("UserAgent").Should().NotBeNull("because UserAgent property should exist");
        interfaceType.GetProperty("InitialInviteRequest").Should().NotBeNull("because InitialInviteRequest property should exist");
    }

    [Fact]
    public void ICallSessionManager_ShouldBeProperlyDefined()
    {
        // Arrange
        var interfaceType = typeof(ICallSessionManager);

        // Assert - Interface should exist (note: doesn't inherit from IDisposable)
        interfaceType.IsInterface.Should().BeTrue("because ICallSessionManager should be an interface");

        // Assert - Required methods should exist
        var createSessionMethod = interfaceType.GetMethods()
            .FirstOrDefault(m => m.Name == "CreateSessionAsync");
        createSessionMethod.Should().NotBeNull("because CreateSessionAsync method should exist");
        createSessionMethod!.ReturnType.Should().BeAssignableTo<Task>("because CreateSessionAsync should return Task<ICallSession>");

        interfaceType.Should().HaveMethod("GetSession", new[] { typeof(string) });

        interfaceType.Should().HaveMethod("TerminateSessionAsync", new[] { typeof(string) })
            .Which.ReturnType.Should().Be(typeof(Task), "because TerminateSessionAsync method should return Task");

        var getAllSessionsMethod = interfaceType.GetMethods()
            .FirstOrDefault(m => m.Name == "GetAllSessions");
        getAllSessionsMethod.Should().NotBeNull("because GetAllSessions method should exist");
        getAllSessionsMethod!.ReturnType.Should().BeAssignableTo(typeof(System.Collections.IEnumerable),
            "because GetAllSessions should return an enumerable collection");
    }

    [Fact]
    public void AllInterfaces_ShouldBeInCorrectNamespace()
    {
        // Arrange
        var expectedNamespace = "voice_processing_service.Interfaces";
        var interfaceTypes = new[]
        {
            typeof(IAudioBuffer),
            typeof(IAudioInputReceiver),
            typeof(IAudioProcessor),
            typeof(ICallSession),
            typeof(ICallSessionManager)
        };

        // Assert
        foreach (var interfaceType in interfaceTypes)
        {
            interfaceType.Namespace.Should().Be(expectedNamespace,
                $"because {interfaceType.Name} should be in the correct namespace");
        }
    }

    [Fact]
    public void AllInterfaces_ShouldHaveProperDocumentation()
    {
        // Arrange
        var interfaceTypes = new[]
        {
            typeof(IAudioBuffer),
            typeof(IAudioInputReceiver),
            typeof(IAudioProcessor),
            typeof(ICallSession),
            typeof(ICallSessionManager)
        };

        // Assert - Verify that interfaces are properly defined and accessible
        foreach (var interfaceType in interfaceTypes)
        {
            interfaceType.Should().NotBeNull($"because {interfaceType.Name} should be properly defined");
            interfaceType.IsInterface.Should().BeTrue($"because {interfaceType.Name} should be an interface");
            interfaceType.IsPublic.Should().BeTrue($"because {interfaceType.Name} should be public");
        }
    }
}