using System;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using Xunit;
using Moq;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;

namespace voice_processing_service.Tests.Unit.Services
{
    public class SipServerServiceTests
    {
        private SipServerService CreateService(
            ICallSessionManager sessionManager = null,
            SipServerOptions options = null)
        {
            var logger = new NullLogger<SipServerService>();
            var manager = sessionManager ?? new Mock<ICallSessionManager>().Object;
            var opts = options ?? new SipServerOptions
            {
                ListenPort = 0,
                WavRecordingDirectory = "wav",
                RtpPortMin = 10000,
                RtpPortMax = 11000
            };
            var optionsWrapper = Options.Create(opts);
            var loggerFactory = new NullLoggerFactory();
            Func<string, int, int, IAudioInputReceiver> rtpFactory = (cid, rtpPort, rtcpPort) => Mock.Of<IAudioInputReceiver>();
            Func<string, IAudioProcessor> wavFactory = cid => Mock.Of<IAudioProcessor>();
            Func<string, int, int, IAudioInputReceiver> audioInputReceiverFactory = (cid, rtpPort, rtcpPort) => Mock.Of<IAudioInputReceiver>();
            Func<string, IAudioProcessor> audioProcessorFactory = cid => Mock.Of<IAudioProcessor>();
            Func<string, string, string, IAudioProcessor> enhancedAudioProcessorFactory = (cid, caller, called) => Mock.Of<IAudioProcessor>();
            var syncService = new Mock<ICallSynchronizationService>().Object;

            var portAllocator = Mock.Of<IPortAllocator>();
            var rtpAudioReceiverFactory = Mock.Of<IRtpAudioReceiverFactory>();
            return new SipServerService(
                logger,
                manager,
                optionsWrapper,
                loggerFactory,
                portAllocator,
                rtpAudioReceiverFactory,
                audioProcessorFactory,
                enhancedAudioProcessorFactory,
                new Mock<ISipRegistrationManager>().Object,
                syncService);
        }

        [Fact]
        public void Constructor_NullLogger_Throws()
        {
            // Assert.Throws<ArgumentNullException>(() =>
            //     new SipServerService(
            //         null,
            //         Mock.Of<ICallSessionManager>(),
            //         Options.Create(new SipServerOptions { ListenPort = 5060, WavRecordingDirectory = "wav", RtpPortMin = 1000, RtpPortMax = 2000 }),
            //         new NullLoggerFactory(),
            //         Mock.Of<IPortAllocator>(),
            //         Mock.Of<IRtpAudioReceiverFactory>(),
            //         cid => Mock.Of<IAudioProcessor>()));
        }

        [Fact]
        public async Task StartAsync_InitializesSipTransportAndAddsChannel()
        {
            var service = CreateService();
            await service.StartAsync(CancellationToken.None);

            var transportField = typeof(SipServerService)
                .GetField("_sipTransport", BindingFlags.Instance | BindingFlags.NonPublic);
            var transport = transportField.GetValue(service) as SIPTransport;
            Assert.NotNull(transport);

            Assert.NotEmpty(transport.GetSIPChannels());
            Assert.Contains(transport.GetSIPChannels(), c => c is SIPUDPChannel);
        }

        [Fact]
        public async Task StartAsync_RegistersRequestHandler()
        {
            var service = CreateService();
            await service.StartAsync(CancellationToken.None);

            var transportField = typeof(SipServerService)
                .GetField("_sipTransport", BindingFlags.Instance | BindingFlags.NonPublic);
            var transport = transportField.GetValue(service) as SIPTransport;

            var eventField = typeof(SIPTransport)
                .GetField("SIPTransportRequestReceived", BindingFlags.Instance | BindingFlags.NonPublic);
            var handler = eventField.GetValue(transport) as Delegate;
            Assert.NotNull(handler);
        }

        [Fact]
        public async Task StopAsync_DisposesSipTransportAndUnregistersHandler()
        {
            var service = CreateService();
            await service.StartAsync(CancellationToken.None);

            await service.StopAsync(CancellationToken.None);

            var transportField = typeof(SipServerService)
                .GetField("_sipTransport", BindingFlags.Instance | BindingFlags.NonPublic);
            var transport = transportField.GetValue(service);
            Assert.Null(transport);
        }
    }
}