# Checklist nutných změn pro umožnění běhu serveru a simulátoru na jedné stanici

## Úpravy v simulátoru (voice-processing-simulator)

- [x] **Úprava Program.cs - zpracování parametrů příkazové řádky**
  - [x] Rozšíření příkazu "simulate" o parametry pro konfiguraci SIP a RTP portů
  - [x] Přidání výchozích hodnot pro nové parametry
  - [x] Aktualizace nápovědy a příkladů použití

- [x] **Úprava Program.cs - metoda SimulateCallAsync**
  - [x] Úprava signatury metody pro přidání nových parametrů
  - [x] Úprava vytváření SIP kanálu pro použití konfigurovatelného portu
  - [x] Úprava vytváření VoIPMediaSession pro použití konfigurovatelného rozsahu portů

- [x] **Úprava Program.cs - metoda ShowUsage**
  - [x] Aktualizace nápovědy pro zobrazení nových parametrů
  - [x] Přidání příkladů použití s novými parametry

- [x] **Úprava Properties/launchSettings.json**
  - [x] Přidání profilu s vlastními porty pomocí parametrů příkazové řádky

## Úpravy v serveru (voice-processing-service)

- [x] **Úprava RtpAudioReceiverFactory.cs - metoda CreateReceiverWithSpecificPorts**
  - [x] Odstranění pevně nastavených portů (40002 a 40003)
  - [x] Úprava vytváření UDP klientů pro použití parametrů rtpPort a rtcpPort
  - [x] Zajištění konzistence v nastavení IP adresy (použití parametru localAddress)
  - [x] Přidání lepšího logování pro diagnostiku problémů s porty

- [x] **Úprava SipServerService.cs - zajištění konzistence RTP portů**
  - [x] Extrakce RTP portu z SDP odpovědi
  - [x] Použití stejného portu pro vytvoření RtpAudioReceiver
  - [x] Přidání logování pro ověření, že porty v SDP odpovídají portům v RtpAudioReceiver

- [x] **Úprava Properties/launchSettings.json**
  - [x] Přidání profilu s vlastními porty pomocí proměnných prostředí
  - [x] Nastavení proměnných prostředí pro konfiguraci SIP a RTP portů

## Testování

- [ ] **Testování serveru s výchozími porty**
  - [ ] Spuštění serveru bez parametrů
  - [ ] Ověření, že server naslouchá na správných portech

- [ ] **Testování serveru s vlastními porty**
  - [ ] Spuštění serveru s proměnnými prostředí pro konfiguraci portů
  - [ ] Ověření, že server naslouchá na správných portech

- [ ] **Testování simulátoru s výchozími porty**
  - [ ] Spuštění simulátoru s minimálními parametry
  - [ ] Ověření, že simulátor naslouchá na správných portech

- [ ] **Testování simulátoru s vlastními porty**
  - [ ] Spuštění simulátoru s parametry pro konfiguraci portů
  - [ ] Ověření, že simulátor naslouchá na správných portech

- [ ] **Testování serveru a simulátoru na jedné stanici**
  - [ ] Spuštění serveru a simulátoru na jedné stanici s různými porty
  - [ ] Ověření, že simulátor se úspěšně připojí k serveru
  - [ ] Ověření, že RTP stream je správně přenášen
  - [ ] Ověření, že hovor je správně ukončen

- [ ] **Testování více instancí serveru na jedné stanici**
  - [ ] Spuštění více instancí serveru na jedné stanici s různými porty
  - [ ] Ověření, že všechny instance běží současně bez konfliktů

- [ ] **Testování více instancí simulátoru na jedné stanici**
  - [ ] Spuštění více instancí simulátoru na jedné stanici s různými porty
  - [ ] Ověření, že všechny instance běží současně bez konfliktů

## Dokumentace

- [ ] **Aktualizace dokumentace**
  - [ ] Aktualizace README.md s informacemi o konfiguraci portů
  - [ ] Přidání příkladů použití s různými konfiguracemi portů
  - [ ] Přidání sekce o řešení problémů s porty

- [ ] **Vytvoření dokumentace pro vývojáře**
  - [ ] Popis implementace konfigurace portů
  - [ ] Vysvětlení, jak funguje SIP a RTP komunikace
  - [ ] Popis, jak správně konfigurovat porty pro běh na jedné stanici

## Finální kontrola

- [ ] **Kontrola kompatibility s SIPSorcery 8.0.11**
  - [ ] Ověření, že všechny úpravy jsou kompatibilní s SIPSorcery 8.0.11
  - [ ] Testování s aktuální verzí SIPSorcery

- [ ] **Kontrola výkonu**
  - [ ] Testování s více současnými hovory
  - [ ] Ověření, že nedochází k únikům paměti nebo jiným problémům s výkonem

- [ ] **Kontrola bezpečnosti**
  - [ ] Ověření, že konfigurace portů neotevírá bezpečnostní díry
  - [ ] Ověření, že porty jsou správně zabezpečeny

## Dokumentace implementovaných změn

- [x] **Vytvoření dokumentace implementovaných změn**
  - [x] Vytvoření souboru implemented_changes.md s popisem provedených změn
  - [x] Přidání ukázek kódu pro klíčové změny
  - [x] Aktualizace index.md s odkazem na implementované změny
  - [x] Aktualizace todo.md na základě implementovaných změn
