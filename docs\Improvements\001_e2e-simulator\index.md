# Umožnění běhu serveru a simulátoru na jedné stanici

## Obsah

### Hlavní dokumenty

1. [Kompletní <PERSON>e<PERSON>en<PERSON>](complete_solution.md) - **Hlavní dokument s uceleným řešením**
2. [Checklist nutných změn](todo.md) - **Seznam všech potřebných úprav**
3. [Implementované změny](implemented_changes.md) - **Seznam již implementovaných změn**

### Doplňkové dokumenty a detailní analýzy

4. [Popis problému a řešení](README.md)
5. [Implementační detaily](implementation.md)
6. [Testovací plán](testing.md)
7. [Technické detaily SIPSorcery](technical_details.md)
8. [Analýza kompatibility s SIPSorcery 8.0.11](compatibility_analysis.md)
9. [Úprava RtpAudioReceiverFactory](rtpaudioreceiverfactory_update.md)
10. [Analýza SIP a RTP implementace](sip_rtp_analysis.md)

## Shrnutí

Tento dokument popisuje úpravy potřebné pro umožnění běhu serveru (voice-processing-service) a simulátoru (voice-processing-simulator) na jedné stanici. Hlavním problémem je konfigurace portů a zajištění kompatibility s aktuální verzí SIPSorcery.

**Pro implementaci řešení se řiďte především dokumenty [Kompletní řešení](complete_solution.md) a [Checklist nutných změn](todo.md).** Ostatní dokumenty obsahují detailnější analýzy jednotlivých částí a jsou určeny pro hlubší pochopení problematiky.

### Klíčové body

1. **Konfigurace portů**:
   - Server: Využití nativní podpory ASP.NET Core pro konfiguraci pomocí proměnných prostředí
   - Simulátor: Přidání parametrů příkazové řádky pro konfiguraci SIP a RTP portů

2. **Kompatibilita s SIPSorcery 8.0.11**:
   - Zajištění, že implementace je kompatibilní s aktuální verzí SIPSorcery
   - Správné nastavení RTP portů v SDP

3. **Testování**:
   - Ověření, že server a simulátor mohou běžet na jedné stanici
   - Ověření, že je možné spustit více instancí serveru a simulátoru na jedné stanici

### Příklady použití

```bash
# Spuštění serveru s vlastními porty (Windows)
set SIPSERVER__LISTENPORT=5060
set SIPSERVER__RTPPORTMIN=30000
set SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Spuštění serveru s vlastními porty (Linux/macOS)
export SIPSERVER__LISTENPORT=5060
export SIPSERVER__RTPPORTMIN=30000
export SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Spuštění simulátoru s vlastními porty
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30 5070 25000 25010
```

## Další kroky

Po implementaci těchto úprav bude možné:

1. Spustit server a simulátor na jedné stanici
2. Konfigurovat porty pro obě komponenty
3. Spustit více instancí serveru a simulátoru na jedné stanici

Tyto úpravy usnadní vývoj a testování aplikace, protože nebude potřeba mít k dispozici více stanic.
