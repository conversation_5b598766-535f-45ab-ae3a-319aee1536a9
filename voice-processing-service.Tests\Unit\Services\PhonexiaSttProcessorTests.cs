using System;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using voice_processing_service.Configuration;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class PhonexiaSttProcessorTests
    {
        private readonly PhonexiaOptions _optionsValue = new PhonexiaOptions
        {
            ApiUrl = "http://testapi",
            Username = "user",
            Password = "pass",
            Model = "model1"
        };

        private PhonexiaSttProcessor CreateProcessor(
            HttpClient httpClient,
            KafkaProducer kafkaProducer = null)
        {
            var optionsMock = new Mock<IOptions<PhonexiaOptions>>();
            optionsMock.Setup(o => o.Value).Returns(_optionsValue);
            var loggerMock = new Mock<ILogger<PhonexiaSttProcessor>>();
            return new PhonexiaSttProcessor(
                callId: "CALLID",
                connectionId: "conn",
                agentId: "agent",
                customerNumber: "cust",
                channelType: "chan",
                callerParty: "caller",
                calledParty: "called",
                options: optionsMock.Object,
                httpClient: httpClient,
                kafkaProducer: kafkaProducer,
                logger: loggerMock.Object);
        }

        [Fact]
        public void Constructor_ValidArgs_DoesNotThrow()
        {
            var httpClient = new HttpClient();
            var proc = CreateProcessor(httpClient);
            Assert.NotNull(proc);
            Assert.Equal("PhonexiaSTT_CALLID", proc.ProcessorId);
        }

        [Fact]
        public void Constructor_NullCallId_ThrowsArgumentNullException()
        {
            var optionsMock = new Mock<IOptions<PhonexiaOptions>>();
            optionsMock.Setup(o => o.Value).Returns(_optionsValue);
            var loggerMock = new Mock<ILogger<PhonexiaSttProcessor>>();
            Assert.Throws<ArgumentNullException>(() =>
                new PhonexiaSttProcessor(
                    callId: null,
                    connectionId: "c",
                    agentId: "a",
                    customerNumber: "n",
                    channelType: "t",
                    callerParty: "caller",
                    calledParty: "called",
                    options: optionsMock.Object,
                    httpClient: new HttpClient(),
                    kafkaProducer: null,
                    logger: loggerMock.Object));
        }

        [Fact]
        public async Task LoginAsync_Success_ReturnsSessionId()
        {
            // Arrange
            var handlerMock = new Mock<HttpMessageHandler>();
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post && req.RequestUri.AbsoluteUri == "http://testapi/login"),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent("{\"Result\":{\"Session\":{\"Id\":\"SESSION123\"}}}", Encoding.UTF8, "application/json")
                });

            var httpClient = new HttpClient(handlerMock.Object);
            var proc = CreateProcessor(httpClient);

            // Act
            var method = typeof(PhonexiaSttProcessor)
                .GetMethod("LoginAsync", BindingFlags.NonPublic | BindingFlags.Instance);
            var task = (Task<string>)method.Invoke(proc, new object[] { CancellationToken.None });
            var result = await task;

            // Assert
            Assert.Equal("SESSION123", result);
        }

        [Fact]
        public async Task LoginAsync_HttpError_ThrowsException()
        {
            var handlerMock = new Mock<HttpMessageHandler>();
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.InternalServerError));
            var httpClient = new HttpClient(handlerMock.Object);
            var proc = CreateProcessor(httpClient);

            var method = typeof(PhonexiaSttProcessor)
                .GetMethod("LoginAsync", BindingFlags.NonPublic | BindingFlags.Instance);
            var invoke = Assert.Throws<TargetInvocationException>(() =>
            {
                var task = (Task<string>)method.Invoke(proc, new object[] { CancellationToken.None });
                task.GetAwaiter().GetResult();
            });
            Assert.IsType<HttpRequestException>(invoke.InnerException);
        }



        [Fact]
        public async Task BindSttToStreamAsync_Success_ReturnsTaskId()
        {
            var json = "{\"Result\":{\"StreamTaskInfo\":{\"Id\":\"TASK99\"}}}";
            var handlerMock = new Mock<HttpMessageHandler>();
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post && req.RequestUri.AbsoluteUri.Contains("/technologies/stt/input_stream")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent(json, Encoding.UTF8, "application/json")
                });
            var httpClient = new HttpClient(handlerMock.Object);
            var proc = CreateProcessor(httpClient);
            typeof(PhonexiaSttProcessor)
                .GetField("_sessionId", BindingFlags.NonPublic | BindingFlags.Instance)
                .SetValue(proc, "sessA");

            var method = typeof(PhonexiaSttProcessor)
                .GetMethod("BindSttToStreamAsync", BindingFlags.NonPublic | BindingFlags.Instance);
            dynamic task = method.Invoke(proc, new object[] { "stream1", CancellationToken.None });
            var result = await task;
            Assert.Equal("TASK99", result);
        }

        [Fact]
        public void Dispose_Idempotent_NoException()
        {
            var proc = CreateProcessor(new HttpClient());
            // Call Dispose twice
            proc.Dispose();
            proc.Dispose();
        }

        // BindSttToStreamAsync failure test
        [Fact]
        public void BindSttToStreamAsync_HttpError_ThrowsException()
{
    var handlerMock = new Mock<HttpMessageHandler>();
    handlerMock
        .Protected()
        .Setup<Task<HttpResponseMessage>>(
            "SendAsync",
            ItExpr.IsAny<HttpRequestMessage>(),
            ItExpr.IsAny<CancellationToken>())
        .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.BadRequest));
    var httpClient = new HttpClient(handlerMock.Object);
    var proc = CreateProcessor(httpClient);
    // Set private _sessionId for test
    typeof(PhonexiaSttProcessor)
        .GetField("_sessionId", BindingFlags.NonPublic | BindingFlags.Instance)
        .SetValue(proc, "sessB");
    var method = typeof(PhonexiaSttProcessor)
        .GetMethod("BindSttToStreamAsync", BindingFlags.NonPublic | BindingFlags.Instance);
    Assert.Throws<TargetInvocationException>(() =>
    {
        dynamic task = method.Invoke(proc, new object[] { "streamX", CancellationToken.None });
        task.GetAwaiter().GetResult();
    });
}

        [Fact]
        public async Task StopStreamAsync_NoIds_DoesNotThrow()
        {
            var httpClient = new HttpClient();
            var proc = CreateProcessor(httpClient);
            var method = typeof(PhonexiaSttProcessor)
                .GetMethod("StopStreamAsync", BindingFlags.NonPublic | BindingFlags.Instance);
            dynamic task = method.Invoke(proc, new object[] { CancellationToken.None });
            await task; // should complete without exception
        }

        [Fact]
        public async Task ProcessWebSocketMessagesAsync_NullWebSocket_DoesNotThrow()
        {
            // Arrange
            var httpClient = new HttpClient();
            var proc = CreateProcessor(httpClient);
            
            // Ensure _websocket field is null (simulating failed WebSocket connection)
            var websocketField = typeof(PhonexiaSttProcessor)
                .GetField("_websocket", BindingFlags.NonPublic | BindingFlags.Instance);
            websocketField.SetValue(proc, null);

            // Act & Assert
            var method = typeof(PhonexiaSttProcessor)
                .GetMethod("ProcessWebSocketMessagesAsync", BindingFlags.NonPublic | BindingFlags.Instance);
            
            // This should complete without throwing NullReferenceException
            var task = (Task)method.Invoke(proc, new object[] { CancellationToken.None });
            await task;
            
            // Test passes if no exception is thrown
        }

        [Fact]
        public async Task ProcessWebSocketMessagesAsync_WebSocketBecomesNull_DoesNotThrow()
        {
            // Arrange
            var httpClient = new HttpClient();
            var proc = CreateProcessor(httpClient);
            
            // Set _websocket to null initially
            var websocketField = typeof(PhonexiaSttProcessor)
                .GetField("_websocket", BindingFlags.NonPublic | BindingFlags.Instance);
            websocketField.SetValue(proc, null);

            // Act & Assert
            var method = typeof(PhonexiaSttProcessor)
                .GetMethod("ProcessWebSocketMessagesAsync", BindingFlags.NonPublic | BindingFlags.Instance);
            
            // Use a short timeout to ensure the method completes quickly
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(100));
            var task = (Task)method.Invoke(proc, new object[] { cts.Token });
            
            // This should complete without throwing NullReferenceException
            try
            {
                await task;
            }
            catch (OperationCanceledException)
            {
                // Expected due to timeout, this is fine
            }
            
            // Test passes if no NullReferenceException is thrown
        }
    }
}