**Koncept návrhu:**

1.  **Modulární architektura:** Rozdělíme logiku do samost<PERSON>, do<PERSON><PERSON><PERSON> definovan<PERSON>ch služeb/modulů s jasnými rozhraními.
2.  **Dependency Injection (DI):** Všechny závislosti budeme vkládat přes konstruktory, což umožní snadnou výměnu implementací (např. RTP za WebRTC, WAV za skutečné STT).
3.  **Session Management:** Vytvoříme `CallSessionManager`, který bude zodpovědný za vytváření, sledování a ukončování jednotlivých `CallSession`. Každá `CallSession` bude izolovaná.
4.  **Buffer:** Mezi modul příjmu audia (`IAudioInputReceiver`) a modul zpracování audia (`IAudioProcessor`) vložíme buffer (`IAudioBuffer`), k<PERSON><PERSON> bude vyrovnávat rozdílné rych<PERSON>ti. Použijeme `BlockingCollection<T>` pro thread-safe implementaci.
5.  **Oddělení Zodpovědností:**
    - `SipServerService` (IHostedService): Spravuje SIP transport a deleguje příchozí hovory na `CallSessionManager`.
    - `CallSessionManager`: Vytváří a spravuje instance `ICallSession`.
    - `ICallSession`: Reprezentuje jeden aktivní hovor, drží pohromadě všechny jeho části (SIP UA, přijímač, buffer, procesor) a řídí jeho životní cyklus.
    - `IAudioInputReceiver` (např. `RtpAudioReceiver`): Přijímá síťová data, parsuje audio (RTP) a vkládá `byte[]` do bufferu.
    - `IAudioBuffer`: Thread-safe fronta pro audio data (`byte[]`).
    - `IAudioProcessor` (např. `WavAudioProcessor` nebo `SttAudioProcessor`): Odebírá data z bufferu a zpracovává je (ukládá do WAV, posílá do STT).

**Mermaid Diagram Architektury:**

```mermaid
graph TD
    subgraph External
        SIP_Client[SIP Klient]
    end

    subgraph SipApplication ["ASP.NET Core Aplikace (.NET 9)"]
        A_Kestrel[Kestrel Web Server] --> B_ApiEndpoints(API Endpoints /api/calls)
        A_Kestrel --> C_SipServerService(SipServerService : IHostedService)

        subgraph DependencyInjection ["DI Kontejner"]
            D_SIPTransport(SIPTransport - Singleton)
            E_CallSessionManager(CallSessionManager : ICallSessionManager - Singleton)
            F_LoggerFactory(ILoggerFactory - Singleton)
            G_AudioReceiverFactory(Factory pro IAudioInputReceiver - Transient)
            H_AudioBufferFactory(Factory pro IAudioBuffer - Transient)
            I_AudioProcessorFactory(Factory pro IAudioProcessor - Transient)
            J_CallSessionFactory(Factory pro ICallSession - Transient)
        end

        C_SipServerService -- Příchozí INVITE --> E_CallSessionManager
        B_ApiEndpoints -- Správa hovorů --> E_CallSessionManager

        E_CallSessionManager -- Vytváří / Spravuje --> K_CallSession(ICallSession - Instance per Call)

        subgraph CallSessionInstance [Instance pro 1 hovor]
            direction LR
            K_CallSession --> L_SIPUserAgent(SIPUserAgent)
            K_CallSession --> M_AudioReceiver(IAudioInputReceiver)
            K_CallSession --> N_AudioBuffer(IAudioBuffer)
            K_CallSession --> O_AudioProcessor(IAudioProcessor)

            M_AudioReceiver -- byte[] audio --> N_AudioBuffer
            O_AudioProcessor -- čte byte[] --> N_AudioBuffer
        end

        M_AudioReceiver -- síťová data --> P_UdpListeners(UDP Listeners RTP/RTCP)
        O_AudioProcessor -- výsledek (WAV/Text) --> Q_Output[Uložiště / STT Výsledek]
    end

    SIP_Client -- SIP (UDP/TCP) --> C_SipServerService
    SIP_Client -- RTP/RTCP (UDP) --> P_UdpListeners

    style CallSessionInstance fill:#f9f,stroke:#333,stroke-width:2px
```

**Vysvětlivky k diagramu:**

- **SIP Klient:** Externí zařízení nebo aplikace, která iniciuje hovor.
- **ASP.NET Core Aplikace:** Hostitelský proces.
- **Kestrel:** Webový server, který hostuje API a `IHostedService`.
- **API Endpoints:** REST rozhraní pro správu hovorů.
- **SipServerService:** Služba běžící na pozadí, která naslouchá SIP zprávám.
- **DI Kontejner:** Spravuje životní cyklus a poskytuje instance služeb. Důležité jsou zde _factory_ (továrny) pro komponenty, které mají životnost vázanou na konkrétní hovor (Transient).
- **CallSessionManager:** Singleton služba pro centrální správu všech hovorů.
- **ICallSession:** Rozhraní pro jeden hovor. Instance je vytvářena manažerem pro každý nový hovor.
- **CallSessionInstance (Subgraph):** Znázorňuje komponenty tvořící jeden aktivní hovor.
- **SIPUserAgent:** Komponenta SIPSorcery pro správu SIP signalizace konkrétního hovoru.
- **IAudioInputReceiver:** Modul přijímající audio (např. `RtpAudioReceiver`).
- **IAudioBuffer:** Buffer mezi přijímačem a procesorem.
- **IAudioProcessor:** Modul zpracovávající audio (např. `WavAudioProcessor`, později `SttAudioProcessor`).
- **UDP Listeners:** Síťové sockety pro příjem RTP/RTCP.
- **Output:** Kam směřuje výsledek zpracování (WAV soubor, text z STT).

**Aktualizovaný kód s DI, Bufferem a modularitou:**

Rozdělíme kód do více souborů pro přehlednost.

**1. Rozhraní (např. `Interfaces.cs`)**

```csharp
using System;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;

namespace SimpleSipWithSttBuffer.Interfaces
{
    // Rozhraní pro buffer audio dat
    public interface IAudioBuffer : IDisposable
    {
        void Add(byte[] audioData);
        bool TryTake(out byte[] audioData, int millisecondsTimeout, CancellationToken cancellationToken);
        void CompleteAdding(); // Signalizuje, že už žádná data nebudou přidána
        bool IsAddingCompleted { get; }
        bool IsCompleted { get; } // Zda je buffer prázdný a bylo voláno CompleteAdding
    }

    // Rozhraní pro příjemce audia (např. RTP)
    public interface IAudioInputReceiver : IDisposable
    {
        Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken);
        IPEndPoint RtpLocalEndPoint { get; }
        IPEndPoint RtcpLocalEndPoint { get; }
    }

    // Rozhraní pro zpracování audia (např. WAV, STT)
    public interface IAudioProcessor : IDisposable
    {
        Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken);
        string ProcessorId { get; } // Identifikátor pro logování/rozlišení
    }

    // Rozhraní pro session jednoho hovoru
    public interface ICallSession : IDisposable
    {
        string CallId { get; }
        SIPUserAgent UserAgent { get; }
        SIPRequest InitialInviteRequest { get; }
        DateTime StartTime { get; }
        Task StartAsync(CancellationToken cancellationToken);
        Task StopAsync();
    }

    // Rozhraní pro správce sessions
    public interface ICallSessionManager
    {
        Task<ICallSession> CreateSessionAsync(SIPUserAgent userAgent, SIPRequest inviteRequest, Func<IAudioInputReceiver> inputReceiverFactory, Func<IAudioProcessor> audioProcessorFactory);
        ICallSession GetSession(string callId);
        Task TerminateSessionAsync(string callId);
        IEnumerable<ICallSession> GetAllSessions();
    }
}
```

**2. Implementace Bufferu (např. `BlockingCollectionAudioBuffer.cs`)**

```csharp
using System;
using System.Collections.Concurrent;
using System.Threading;
using SimpleSipWithSttBuffer.Interfaces;
using Microsoft.Extensions.Logging;

namespace SimpleSipWithSttBuffer.Services
{
    public class BlockingCollectionAudioBuffer : IAudioBuffer
    {
        private readonly BlockingCollection<byte[]> _buffer = new BlockingCollection<byte[]>(new ConcurrentQueue<byte[]>());
        private readonly ILogger<BlockingCollectionAudioBuffer> _logger;
        private readonly string _instanceId = Guid.NewGuid().ToString("N").Substring(0, 6); // Pro logování

        public BlockingCollectionAudioBuffer(ILogger<BlockingCollectionAudioBuffer> logger)
        {
            _logger = logger;
            _logger.LogTrace($"[{_instanceId}] Buffer created.");
        }

        public void Add(byte[] audioData)
        {
            if (!_buffer.IsAddingCompleted)
            {
                try
                {
                    _buffer.Add(audioData);
                    // _logger.LogTrace($"[{_instanceId}] Added {audioData.Length} bytes to buffer. Current count: {_buffer.Count}");
                }
                catch (InvalidOperationException)
                {
                    // Může nastat, pokud se zavolá CompleteAdding a pak Add
                    _logger.LogWarning($"[{_instanceId}] Attempted to add data after CompleteAdding was called.");
                }
            }
        }

        public bool TryTake(out byte[] audioData, int millisecondsTimeout, CancellationToken cancellationToken)
        {
            // _logger.LogTrace($"[{_instanceId}] Attempting to take data from buffer. Current count: {_buffer.Count}");
            return _buffer.TryTake(out audioData, millisecondsTimeout, cancellationToken);
        }

        public void CompleteAdding()
        {
            if (!_buffer.IsAddingCompleted)
            {
                _logger.LogInformation($"[{_instanceId}] Completing adding to buffer.");
                _buffer.CompleteAdding();
            }
        }

        public bool IsAddingCompleted => _buffer.IsAddingCompleted;

        public bool IsCompleted => _buffer.IsCompleted;


        public void Dispose()
        {
            _logger.LogTrace($"[{_instanceId}] Disposing buffer.");
            CompleteAdding(); // Zajistí, že konzumenti přestanou čekat
            _buffer.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
```

**3. Implementace RTP Přijímače (např. `RtpAudioReceiver.cs`)**

```csharp
using System;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SimpleSipWithSttBuffer.Interfaces;

namespace SimpleSipWithSttBuffer.Services
{
    public class RtpAudioReceiver : IAudioInputReceiver
    {
        private readonly ILogger<RtpAudioReceiver> _logger;
        private readonly UdpClient _rtpClient;
        private readonly UdpClient _rtcpClient;
        private readonly string _callId; // Pro logování

        public IPEndPoint RtpLocalEndPoint { get; }
        public IPEndPoint RtcpLocalEndPoint { get; }

        // Konstruktor přijímá již vytvořené a navázané UDP klienty
        public RtpAudioReceiver(string callId, UdpClient rtpClient, UdpClient rtcpClient, ILogger<RtpAudioReceiver> logger)
        {
            _callId = callId;
            _rtpClient = rtpClient ?? throw new ArgumentNullException(nameof(rtpClient));
            _rtcpClient = rtcpClient ?? throw new ArgumentNullException(nameof(rtcpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            RtpLocalEndPoint = (IPEndPoint)_rtpClient.Client.LocalEndPoint;
            RtcpLocalEndPoint = (IPEndPoint)_rtcpClient.Client.LocalEndPoint;

             _logger.LogInformation($"[{_callId}] RtpAudioReceiver created for RTP={RtpLocalEndPoint}, RTCP={RtcpLocalEndPoint}");
        }

        public async Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{_callId}] Starting RTP/RTCP listeners...");

            var rtpTask = Task.Run(() => ListenLoopAsync(_rtpClient, "RTP", buffer.Add, cancellationToken), cancellationToken);
            var rtcpTask = Task.Run(() => ListenLoopAsync(_rtcpClient, "RTCP", ProcessRtcpPacket, cancellationToken), cancellationToken); // RTCP data nejdou do bufferu

            try
            {
                 // Počkáme na dokončení obou listenerů nebo na zrušení
                 // Nepoužíváme WhenAll, protože chceme, aby buffer.CompleteAdding bylo zavoláno i když jeden selže
                await Task.WhenAny(rtpTask, rtcpTask); // Počkáme, až jeden skončí (nebo oba budou zrušeny)
                 _logger.LogInformation($"[{_callId}] One of the listeners finished or was cancelled.");

                // Počkáme chvíli, jestli neskončí i druhý
                await Task.WhenAll(rtpTask, rtcpTask).WaitAsync(TimeSpan.FromSeconds(1), CancellationToken.None); // Krátký timeout pro dokončení druhého

            }
            catch (OperationCanceledException)
            {
                 _logger.LogInformation($"[{_callId}] Listening cancelled.");
            }
            catch (TimeoutException) {
                 _logger.LogWarning($"[{_callId}] Timeout waiting for the second listener task to complete.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Exception while waiting for listener tasks.");
            }
            finally
            {
                _logger.LogInformation($"[{_callId}] Signaling buffer completion.");
                buffer.CompleteAdding(); // Důležité: Signalizovat bufferu, že už nepřijdou žádná data
            }
             _logger.LogInformation($"[{_callId}] Listeners stopped.");
        }

        private async Task ListenLoopAsync(UdpClient client, string type, Action<byte[]> dataHandler, CancellationToken cancellationToken)
        {
             IPEndPoint remoteEndPoint = null; // Přesunuto ven z cyklu
             _logger.LogInformation($"[{_callId}] {type} listener started on {client.Client.LocalEndPoint}.");
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var result = await client.ReceiveAsync(cancellationToken);
                    remoteEndPoint = result.RemoteEndPoint; // Uložíme pro případnou chybu
                    if (result.Buffer.Length > 0)
                    {
                         // Voláme specifický handler pro RTP nebo RTCP
                         // Pro RTP parsujeme a voláme buffer.Add, pro RTCP jen logujeme
                        if(type == "RTP")
                        {
                             ParseAndHandleRtp(result.Buffer, remoteEndPoint, dataHandler);
                        }
                        else // RTCP
                        {
                            dataHandler(result.Buffer); // dataHandler je zde ProcessRtcpPacket
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] {type} listener loop cancelled.");
            }
            catch (ObjectDisposedException) {
                 _logger.LogWarning($"[{_callId}] {type} listener UDP client was disposed while listening.");
            }
            catch (SocketException ex) when (ex.SocketErrorCode == SocketError.Interrupted || ex.SocketErrorCode == SocketError.OperationAborted){
                 _logger.LogInformation($"[{_callId}] {type} listener socket operation interrupted/aborted.");
            }
            catch (Exception ex)
            {
                 _logger.LogError(ex, $"[{_callId}] Error in {type} listener loop from {remoteEndPoint?.ToString() ?? "unknown"}");
            }
            finally
            {
                 _logger.LogInformation($"[{_callId}] {type} listener loop finished on {client.Client?.LocalEndPoint?.ToString() ?? "closed socket"}.");
            }
        }

        // Parsování RTP a volání handleru (buffer.Add)
        private void ParseAndHandleRtp(byte[] buffer, IPEndPoint remoteEndPoint, Action<byte[]> bufferAddAction)
        {
             if (buffer.Length < 12) return;

            // Předpoklad: Payload Type 0 = PCMU/G.711u
            var payloadType = buffer[1] & 0x7F;
             if (payloadType != 0 && payloadType != 8) // Povolíme i A-law (PT=8), i když WAV ukládá jen u-law
             {
                // _logger.LogTrace($"[{_callId}] Ignoring RTP packet with unsupported payload type {payloadType} from {remoteEndPoint}.");
                return;
             }

             int headerLength = 12;
             // Zkontrolujeme, zda je nastaven extension bit (X)
             bool hasExtension = (buffer[0] & 0x10) > 0;
             if(hasExtension)
             {
                 if (buffer.Length < headerLength + 4) return;
                 int extensionHeaderLengthInWords = (buffer[headerLength + 2] << 8) | buffer[headerLength + 3];
                 int extensionDataLength = extensionHeaderLengthInWords * 4;
                 headerLength += 4 + extensionDataLength;
                 if (buffer.Length < headerLength) return;
             }
              // Počítáme s případnými CSRC identifikátory (CC field)
             int csrcCount = buffer[0] & 0x0F;
             headerLength += csrcCount * 4;

             if (buffer.Length <= headerLength) return; // Jen hlavička

            var audioDataLength = buffer.Length - headerLength;
            byte[] audioData = new byte[audioDataLength];
            Buffer.BlockCopy(buffer, headerLength, audioData, 0, audioDataLength);

            // Přidání dat do bufferu
            bufferAddAction(audioData);
        }


         // Zpracování RTCP (zatím jen loguje)
        private void ProcessRtcpPacket(byte[] buffer)
        {
             if (buffer.Length >= 2)
            {
                var payloadType = buffer[1];
                // _logger.LogTrace($"[{_callId}] Received RTCP packet (PT={payloadType}, Length={buffer.Length}).");
            }
        }


        public void Dispose()
        {
            _logger.LogInformation($"[{_callId}] Disposing RtpAudioReceiver for {RtpLocalEndPoint}.");
            _rtpClient?.Close();
            _rtpClient?.Dispose();
            _rtcpClient?.Close();
            _rtcpClient?.Dispose();
             GC.SuppressFinalize(this);
        }
    }
}
```

**4. Implementace WAV Procesoru (např. `WavAudioProcessor.cs`)**

```csharp
using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SimpleSipWithSttBuffer.Interfaces;

namespace SimpleSipWithSttBuffer.Services
{
    public class WavAudioProcessor : IAudioProcessor
    {
        private readonly ILogger<WavAudioProcessor> _logger;
        private readonly string _wavFilePath;
        private readonly string _callId;
        private BinaryWriter _wavWriter;
        private long _audioDataSize = 0;

        public string ProcessorId => $"WAV_{Path.GetFileNameWithoutExtension(_wavFilePath)}";

        public WavAudioProcessor(string callId, string wavFilePath, ILogger<WavAudioProcessor> logger)
        {
            _callId = callId;
            _wavFilePath = wavFilePath ?? throw new ArgumentNullException(nameof(wavFilePath));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _logger.LogInformation($"[{_callId}] WavAudioProcessor created for file: {_wavFilePath}");
        }

        public async Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{_callId}] Starting WAV processing from buffer for {ProcessorId}.");

            if (!InitializeWavFile())
            {
                _logger.LogError($"[{_callId}] Failed to initialize WAV file, processing cannot start.");
                return; // Nemůžeme zpracovávat bez souboru
            }

            try
            {
                // Odebíráme data z bufferu, dokud není prázdný A není dokončeno přidávání
                 while (!buffer.IsCompleted || buffer.TryTake(out _, 0, CancellationToken.None)) // Zkontrolujeme i IsCompleted pro jistotu
                 {
                     if (cancellationToken.IsCancellationRequested)
                     {
                         _logger.LogInformation($"[{_callId}] WAV processing cancellation requested for {ProcessorId}.");
                         break;
                     }

                     // Blokující čekání s timeoutem a cancellation tokenem
                     if (buffer.TryTake(out byte[] audioData, 500, cancellationToken)) // Timeout 500ms
                     {
                         // _logger.LogTrace($"[{_callId}] Processing {audioData.Length} audio bytes for {ProcessorId}.");
                         AppendAudioData(audioData);
                     }
                     // Pokud TryTake vrátí false (timeout nebo cancellation), cyklus pokračuje a zkontroluje podmínky znovu
                 }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] WAV processing cancelled for {ProcessorId} while waiting for data.");
            }
            catch (Exception ex)
            {
                 _logger.LogError(ex, $"[{_callId}] Error during WAV processing for {ProcessorId}.");
            }
            finally
            {
                _logger.LogInformation($"[{_callId}] Finishing WAV processing for {ProcessorId}. Finalizing file.");
                FinalizeWavFile(); // Ukončíme soubor bez ohledu na to, jak smyčka skončila
            }
             _logger.LogInformation($"[{_callId}] WAV processing stopped for {ProcessorId}.");
        }

        private bool InitializeWavFile()
        {
            try
            {
                // Zajistit, že adresář existuje
                Directory.CreateDirectory(Path.GetDirectoryName(_wavFilePath));

                var fileStream = new FileStream(_wavFilePath, FileMode.Create, FileAccess.Write, FileShare.Read);
                _wavWriter = new BinaryWriter(fileStream);
                WriteWavHeader(_wavWriter, 0); // Zapíše hlavičku s nulovou délkou dat
                _logger.LogInformation($"[{_callId}] Initialized WAV file: {_wavFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to initialize WAV file {_wavFilePath}");
                _wavWriter?.Dispose();
                _wavWriter = null;
                return false;
            }
        }

        private void AppendAudioData(byte[] audioData)
        {
            if (_wavWriter == null) return;
            try
            {
                _wavWriter.Write(audioData);
                _audioDataSize += audioData.Length;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to write audio data to WAV file {_wavFilePath}");
                FinalizeWavFile(); // Pokud selže zápis, rovnou soubor uzavřeme
            }
        }

        private void FinalizeWavFile()
        {
            if (_wavWriter != null)
            {
                _logger.LogInformation($"[{_callId}] Finalizing WAV file: {_wavFilePath}, Audio Size: {_audioDataSize} bytes");
                try
                {
                    UpdateWavHeader(_wavWriter, _audioDataSize);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{_callId}] Failed to update WAV header for {_wavFilePath}");
                }
                finally
                {
                    _wavWriter.Close(); // Zavře i podkladový FileStream
                    _wavWriter = null; // Označit jako uzavřený
                }
            }
        }

        // Statické metody pro WAV hlavičku (stejné jako předtím)
        private static void WriteWavHeader(BinaryWriter writer, long audioDataSize)
        {
            writer.Write(Encoding.ASCII.GetBytes("RIFF"));
            writer.Write((uint)(36 + audioDataSize));
            writer.Write(Encoding.ASCII.GetBytes("WAVE"));
            writer.Write(Encoding.ASCII.GetBytes("fmt "));
            writer.Write(16);
            writer.Write((ushort)7); // G.711 µ-law
            writer.Write((ushort)1); // Mono
            writer.Write(8000); // Sample rate
            writer.Write(8000); // Byte rate
            writer.Write((ushort)1); // Block align
            writer.Write((ushort)8); // Bits per sample
            writer.Write(Encoding.ASCII.GetBytes("data"));
            writer.Write((uint)audioDataSize);
        }

        private static void UpdateWavHeader(BinaryWriter writer, long audioDataSize)
        {
            if (writer?.BaseStream == null || !writer.BaseStream.CanSeek) return;
            writer.BaseStream.Seek(4, SeekOrigin.Begin);
            writer.Write((uint)(36 + audioDataSize));
            writer.BaseStream.Seek(40, SeekOrigin.Begin);
            writer.Write((uint)audioDataSize);
            writer.Flush(); // Zajistit zápis
        }

        public void Dispose()
        {
            _logger.LogInformation($"[{_callId}] Disposing WavAudioProcessor for {ProcessorId}.");
            FinalizeWavFile(); // Zajistíme uzavření souboru při dispose
            GC.SuppressFinalize(this);
        }
    }
}
```

**5. Implementace Call Session (např. `CallSession.cs`)**

```csharp
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SimpleSipWithSttBuffer.Interfaces;

namespace SimpleSipWithSttBuffer.Services
{
    public class CallSession : ICallSession
    {
        private readonly ILogger<CallSession> _logger;
        private readonly IAudioInputReceiver _audioReceiver;
        private readonly IAudioBuffer _audioBuffer;
        private readonly IAudioProcessor _audioProcessor;
        private readonly Func<Task> _cleanupCallback; // Callback pro CallSessionManager
        private CancellationTokenSource _sessionCts;
        private Task _receiverTask;
        private Task _processorTask;

        public string CallId { get; }
        public SIPUserAgent UserAgent { get; }
        public SIPRequest InitialInviteRequest { get; }
        public DateTime StartTime { get; }

        public CallSession(
            SIPUserAgent userAgent,
            SIPRequest initialInviteRequest,
            IAudioInputReceiver audioReceiver,
            IAudioBuffer audioBuffer,
            IAudioProcessor audioProcessor,
            Func<Task> cleanupCallback, // Přidáno pro signalizaci manažeru
            ILogger<CallSession> logger)
        {
            UserAgent = userAgent ?? throw new ArgumentNullException(nameof(userAgent));
            InitialInviteRequest = initialInviteRequest ?? throw new ArgumentNullException(nameof(initialInviteRequest));
            _audioReceiver = audioReceiver ?? throw new ArgumentNullException(nameof(audioReceiver));
            _audioBuffer = audioBuffer ?? throw new ArgumentNullException(nameof(audioBuffer));
            _audioProcessor = audioProcessor ?? throw new ArgumentNullException(nameof(audioProcessor));
            _cleanupCallback = cleanupCallback ?? throw new ArgumentNullException(nameof(cleanupCallback));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            CallId = InitialInviteRequest.Header.CallId;
            StartTime = DateTime.UtcNow;

            // Handler pro zavěšení hovoru (zavolá StopAsync)
            UserAgent.OnCallHungup += async (dialog) =>
            {
                _logger.LogInformation($"[{CallId}] Call hung up event received (Reason: {dialog?.HangupReason}). Initiating stop.");
                await StopAsync(); // Spustí úklid
            };

             _logger.LogInformation($"[{CallId}] CallSession created. Processor: {_audioProcessor.ProcessorId}, Receiver RTP: {_audioReceiver.RtpLocalEndPoint}");
        }

        public Task StartAsync(CancellationToken externalToken) // Může přijít externí token pro shutdown celé aplikace
        {
             _logger.LogInformation($"[{CallId}] Starting session...");
            _sessionCts = CancellationTokenSource.CreateLinkedTokenSource(externalToken); // Propojení s externím tokenem

            // Spustit příjemce a procesor v samostatných úlohách
            _receiverTask = _audioReceiver.StartListeningAsync(_audioBuffer, _sessionCts.Token);
            _processorTask = _audioProcessor.StartProcessingAsync(_audioBuffer, _sessionCts.Token);

             _logger.LogInformation($"[{CallId}] Session started. Receiver and Processor tasks running.");
             // Nevracíme Tasky, běží na pozadí. Jejich ukončení řeší StopAsync.
             return Task.CompletedTask;
        }

        public async Task StopAsync()
        {
            _logger.LogInformation($"[{CallId}] Stopping session...");

            // 1. Signalizovat zrušení úlohám
            if (_sessionCts != null && !_sessionCts.IsCancellationRequested)
            {
                 _logger.LogDebug($"[{CallId}] Requesting cancellation via CancellationTokenSource.");
                _sessionCts.Cancel();
            }

            // 2. Pokud hovor stále běží v SIP UA, ukončit ho
            if (UserAgent != null && !UserAgent.IsHungup)
            {
                 _logger.LogInformation($"[{CallId}] SIP User Agent is not hung up yet. Sending BYE.");
                try
                {
                    // TODO: Zvážit, zda zde posílat BYE, nebo zda to má udělat volající API/OnCallHungup handler
                    // Pokud StopAsync volá OnCallHungup, vznikne rekurze. Pokud ho volá API, je to OK.
                    // Zatím necháme jen logování, ukončení by měl řešit SIP stack nebo API.
                    // await UserAgent.Hangup(); // Pozor na rekurzi, pokud to volá OnCallHungup
                }
                catch(Exception ex) {
                     _logger.LogWarning(ex, $"[{CallId}] Exception during explicit UserAgent hangup in StopAsync.");
                }
            }

             // 3. Počkat na dokončení úloh (s timeoutem)
            var allTasks = Task.WhenAll(_receiverTask ?? Task.CompletedTask, _processorTask ?? Task.CompletedTask);
            try
            {
                _logger.LogDebug($"[{CallId}] Waiting for receiver and processor tasks to complete...");
                // Timeout pro případ zablokování
                var completed = await Task.WhenAny(allTasks, Task.Delay(TimeSpan.FromSeconds(5), CancellationToken.None));
                 if (completed != allTasks)
                 {
                      _logger.LogWarning($"[{CallId}] Timeout waiting for session tasks to complete. Some resources might leak if tasks are blocked.");
                 } else {
                      _logger.LogInformation($"[{CallId}] Receiver and processor tasks completed.");
                 }

                 // Zkontrolovat výjimky v úlohách
                 if (_receiverTask?.IsFaulted == true) _logger.LogError(_receiverTask.Exception, $"[{CallId}] Receiver task faulted.");
                 if (_processorTask?.IsFaulted == true) _logger.LogError(_processorTask.Exception, $"[{CallId}] Processor task faulted.");

            }
            catch (Exception ex)
            {
                 _logger.LogError(ex, $"[{CallId}] Error waiting for session tasks to complete.");
            }


            // 4. Uvolnit zdroje (volá Dispose na receiveru, bufferu, processoru)
            DisposeInternal();

             // 5. Zavolat callback pro odstranění ze SessionManageru
             _logger.LogDebug($"[{CallId}] Invoking cleanup callback for session manager.");
            await _cleanupCallback();

             _logger.LogInformation($"[{CallId}] Session stopped and cleaned up.");
        }

         private void DisposeInternal() {
             _logger.LogDebug($"[{CallId}] Disposing session internal resources.");
             _sessionCts?.Dispose();
             _sessionCts = null;
             _audioProcessor?.Dispose();
             _audioReceiver?.Dispose(); // Dispose přijímače zavře UDP klienty
             _audioBuffer?.Dispose();
              // UserAgent se nedisposuje zde, spravuje ho SIP Transport nebo volající kód
              _logger.LogDebug($"[{CallId}] Internal resources disposed.");
         }

        public void Dispose()
        {
             _logger.LogInformation($"[{CallId}] Dispose called on CallSession.");
             // Zajistíme, že StopAsync proběhne, pokud ještě neběželo
             // Toto je fallback, primárně by se mělo volat StopAsync
             if (_sessionCts != null && !_sessionCts.IsCancellationRequested)
             {
                  _logger.LogWarning($"[{CallId}] Dispose called directly without StopAsync. Initiating stop now.");
                  // Spustit StopAsync asynchronně a nečekat na něj, abychom nezablokovali Dispose
                  _ = StopAsync();
             } else {
                 // Pokud už StopAsync běží/běželo, jen disposneme co zbylo
                 DisposeInternal();
             }
             GC.SuppressFinalize(this);
        }
    }
}

```

**6. Implementace Call Session Manager (např. `CallSessionManager.cs`)**

```csharp
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SimpleSipWithSttBuffer.Interfaces;

namespace SimpleSipWithSttBuffer.Services
{
    public class CallSessionManager : ICallSessionManager
    {
        private readonly ConcurrentDictionary<string, ICallSession> _sessions = new ConcurrentDictionary<string, ICallSession>();
        private readonly ILoggerFactory _loggerFactory;
        private readonly ILogger<CallSessionManager> _logger;

        public CallSessionManager(ILoggerFactory loggerFactory)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _logger = _loggerFactory.CreateLogger<CallSessionManager>();
            _logger.LogInformation("CallSessionManager created.");
        }

        public Task<ICallSession> CreateSessionAsync(
            SIPUserAgent userAgent,
            SIPRequest inviteRequest,
            Func<IAudioInputReceiver> inputReceiverFactory,
            Func<IAudioProcessor> audioProcessorFactory)
        {
            var callId = inviteRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Attempting to create new session.");

            // Vytvoření závislostí specifických pro session
            var buffer = new BlockingCollectionAudioBuffer(_loggerFactory.CreateLogger<BlockingCollectionAudioBuffer>());
            var receiver = inputReceiverFactory(); // Továrna dodá instanci (např. RtpAudioReceiver)
            var processor = audioProcessorFactory(); // Továrna dodá instanci (např. WavAudioProcessor)

            // Callback pro úklid v manažeru, až session skončí
            Func<Task> cleanupCallback = async () =>
            {
                 _logger.LogInformation($"[{callId}] Cleanup callback invoked. Removing session from manager.");
                 // Odstraní session ze slovníku. Dispose už proběhlo v session.StopAsync().
                 if(_sessions.TryRemove(callId, out _)) {
                      _logger.LogDebug($"[{callId}] Session successfully removed from manager.");
                 } else {
                      _logger.LogWarning($"[{callId}] Session was not found in manager during cleanup callback.");
                 }
                 await Task.CompletedTask; // Jen aby bylo async
            };

            var session = new CallSession(
                userAgent,
                inviteRequest,
                receiver,
                buffer,
                processor,
                cleanupCallback, // Předání callbacku
                _loggerFactory.CreateLogger<CallSession>()
            );

            if (_sessions.TryAdd(callId, session))
            {
                _logger.LogInformation($"[{callId}] Session created and added to manager.");
                return Task.FromResult<ICallSession>(session);
            }
            else
            {
                 _logger.LogWarning($"[{callId}] Failed to add session to manager (duplicate Call-ID?). Disposing created session components.");
                 // Pokud se nepodařilo přidat, musíme uklidit vytvořené komponenty
                 session.Dispose(); // Dispose session se postará o vnitřní komponenty
                 return Task.FromResult<ICallSession>(null); // Nebo vyhodit výjimku
            }
        }

        public ICallSession GetSession(string callId)
        {
            _sessions.TryGetValue(callId, out var session);
            return session;
        }

        public IEnumerable<ICallSession> GetAllSessions()
        {
            return _sessions.Values.ToList(); // Vracíme kopii seznamu hodnot
        }

        public async Task TerminateSessionAsync(string callId)
        {
            _logger.LogInformation($"[{callId}] Request received to terminate session via manager.");
            if (_sessions.TryGetValue(callId, out var session))
            {
                 _logger.LogInformation($"[{callId}] Session found. Calling StopAsync().");
                // StopAsync zavolá cleanupCallback, který session odstraní z _sessions
                 await session.StopAsync();
                  _logger.LogInformation($"[{callId}] StopAsync() completed for session termination request.");
            }
            else
            {
                _logger.LogWarning($"[{callId}] Session not found for termination request.");
            }
        }

        // Metoda pro úklid při shutdownu aplikace
        public async Task ShutdownAllSessionsAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Shutting down all active call sessions...");
            var shutdownTasks = new List<Task>();
            foreach (var sessionId in _sessions.Keys.ToList()) // Iterujeme přes kopii klíčů
            {
                 if (_sessions.TryGetValue(sessionId, out var session))
                 {
                      _logger.LogInformation($"[{sessionId}] Initiating shutdown for session.");
                      shutdownTasks.Add(session.StopAsync()); // Spustíme StopAsync pro každou session
                 }
            }

            try
            {
                await Task.WhenAll(shutdownTasks).WaitAsync(TimeSpan.FromSeconds(10), cancellationToken); // Počkáme na dokončení všech (s timeoutem)
                 _logger.LogInformation("All active sessions stopped.");
            }
            catch (OperationCanceledException) {
                 _logger.LogWarning("Shutdown cancelled while waiting for sessions to stop.");
            }
            catch (TimeoutException) {
                 _logger.LogWarning("Timeout waiting for all sessions to stop during shutdown.");
            }
             catch (Exception ex) {
                  _logger.LogError(ex, "Error during shutdown of sessions.");
             }
             _sessions.Clear(); // Pro jistotu vyčistíme
        }
    }
}
```

**7. SIP Server Service (např. `SipServerService.cs`)**

```csharp
using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options; // Pro konfiguraci
using SIPSorcery.Net;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SimpleSipWithSttBuffer.Interfaces; // Naše rozhraní
using SimpleSipWithSttBuffer.Services;  // Naše implementace

namespace SimpleSipWithSttBuffer
{
    // Konfigurační třída (může být v appsettings.json)
    public class SipServerOptions
    {
        public string ListenIpAddress { get; set; } = "Any"; // "Any" nebo specifická IP
        public int ListenPort { get; set; } = 5060;
        public int RtpPortMin { get; set; } = 10000;
        public int RtpPortMax { get; set; } = 19998;
        public string WavRecordingDirectory { get; set; } = "RecordedCalls";
    }

    public class SipServerService : IHostedService
    {
        private readonly ILogger<SipServerService> _logger;
        private readonly ILoggerFactory _loggerFactory;
        private readonly ICallSessionManager _sessionManager;
        private readonly SipServerOptions _options;
        private readonly SIPTransport _sipTransport;
        private readonly Random _random = new Random();
        private CancellationTokenSource _stoppingCts;

        public SipServerService(
            ILoggerFactory loggerFactory,
            ICallSessionManager sessionManager,
            IOptions<SipServerOptions> options)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _logger = _loggerFactory.CreateLogger<SipServerService>();
            _sessionManager = sessionManager ?? throw new ArgumentNullException(nameof(sessionManager));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

            _sipTransport = new SIPTransport();
            _sipTransport.EnableTraceLogs(); // Zapne logování SIPSorcery
             SIPSorcery.LogFactory.Set(_loggerFactory.CreateLogger("SIPSorcery").ToSIPFactory());

            // Vytvoření adresáře pro nahrávky
             try {
                 Directory.CreateDirectory(_options.WavRecordingDirectory);
             } catch (Exception ex) {
                  _logger.LogError(ex, $"Failed to create WAV recording directory: {_options.WavRecordingDirectory}");
                  // Můžeme zvážit ukončení aplikace, pokud je adresář kritický
             }
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting SIP Server Service...");
            _stoppingCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            try
            {
                 IPAddress listenAddress = _options.ListenIpAddress.Equals("Any", StringComparison.OrdinalIgnoreCase)
                     ? IPAddress.Any
                     : IPAddress.Parse(_options.ListenIpAddress);

                var sipUdpChannel = new SIPUDPChannel(new IPEndPoint(listenAddress, _options.ListenPort));
                _sipTransport.AddSIPChannel(sipUdpChannel);
                _logger.LogInformation($"SIP Server listening on UDP {sipUdpChannel.ListeningEndPoint}");

                // Handler pro příchozí požadavky
                _sipTransport.SIPTransportRequestReceived += HandleSipRequestAsync;

                _logger.LogInformation("SIP Server Service started successfully.");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "Failed to start SIP Server Service.");
                // Signalizovat selhání spuštění
                return Task.FromException(ex);
            }
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping SIP Server Service...");
            _sipTransport.SIPTransportRequestReceived -= HandleSipRequestAsync; // Odregistrovat handler

            // Ukončit všechny aktivní session
             if (_sessionManager is CallSessionManager concreteManager) // Potřebujeme konkrétní typ pro ShutdownAllSessionsAsync
            {
                await concreteManager.ShutdownAllSessionsAsync(cancellationToken);
            }
            else {
                 _logger.LogWarning("Could not cast ICallSessionManager to concrete type CallSessionManager for shutdown.");
            }


            _logger.LogInformation("Shutting down SIP Transport...");
            _sipTransport.Shutdown();
            _logger.LogInformation("SIP Server Service stopped.");

            _stoppingCts?.Cancel(); // Signalizovat internímu kódu, že se zastavuje
        }


        private async Task HandleSipRequestAsync(SIPEndPoint localEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            string callId = sipRequest.Header.CallId ?? "NoCallID";
             _logger.LogInformation($"[{callId}] Received SIP {sipRequest.Method} from {remoteEndPoint} on {localEndPoint}");

            if (sipRequest.Method == SIPMethodsEnum.INVITE)
            {
                 await ProcessInviteAsync(localEndPoint, remoteEndPoint, sipRequest, _stoppingCts.Token);
            }
            else if (sipRequest.Method == SIPMethodsEnum.BYE)
            {
                 // BYE by měl zpracovat SIPUserAgent v rámci CallSession
                 _logger.LogInformation($"[{callId}] Received BYE from {remoteEndPoint}. Should be handled by the corresponding CallSession.");
                 // Poslat OK odpověď na BYE
                 var byeOkResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                 await _sipTransport.SendResponseAsync(remoteEndPoint, byeOkResponse);
            }
             else if (sipRequest.Method == SIPMethodsEnum.CANCEL)
            {
                await ProcessCancelAsync(remoteEndPoint, sipRequest);
            }
            else
            {
                // Poslat Method Not Allowed pro neimplementované metody
                _logger.LogDebug($"[{callId}] Received unhandled method {sipRequest.Method}. Sending 405 Method Not Allowed.");
                var notAllowedResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.MethodNotAllowed, null);
                await _sipTransport.SendResponseAsync(remoteEndPoint, notAllowedResponse);
            }
        }

        private async Task ProcessInviteAsync(SIPEndPoint localEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest, CancellationToken cancellationToken)
        {
             string callId = sipRequest.Header.CallId;
             _logger.LogInformation($"[{callId}] Processing INVITE from {remoteEndPoint}...");

             UdpClient rtpClient = null;
             UdpClient rtcpClient = null;
             ICallSession session = null;

             try
             {
                 // 1. Najít volné RTP/RTCP porty
                 if (!TryGetAvailableRtpPorts(localEndPoint.Address, out int rtpPort, out int rtcpPort))
                 {
                      _logger.LogWarning($"[{callId}] No available RTP/RTCP ports found.");
                      var busyResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.BusyHere, "No RTP ports available");
                      await _sipTransport.SendResponseAsync(remoteEndPoint, busyResponse);
                      return;
                 }
                  _logger.LogDebug($"[{callId}] Allocated ports RTP={rtpPort}, RTCP={rtcpPort} on IP {localEndPoint.Address}");

                 // 2. Vytvořit UDP klienty
                 rtpClient = new UdpClient(new IPEndPoint(localEndPoint.Address, rtpPort));
                 rtcpClient = new UdpClient(new IPEndPoint(localEndPoint.Address, rtcpPort));

                 // 3. Vytvořit SIP User Agenta a SDP odpověď
                 var sipUserAgent = new SIPUserAgent(_sipTransport, null);
                 sipUserAgent.ClientTraceLogsEnabled = true; // Logování pro UA
                 var sdpAnswer = CreateSdpResponse(localEndPoint.Address, rtpPort);

                 // 4. Vytvořit a spustit session přes manažera
                 // Továrny pro vytvoření závislostí specifických pro session
                 Func<IAudioInputReceiver> receiverFactory = () =>
                     new RtpAudioReceiver(callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());

                 Func<IAudioProcessor> processorFactory = () =>
                 {
                     var wavFileName = $"call_{callId}_{DateTime.Now:yyyyMMddHHmmss}.wav";
                     var wavFilePath = Path.Combine(_options.WavRecordingDirectory, wavFileName);
                     return new WavAudioProcessor(callId, wavFilePath, _loggerFactory.CreateLogger<WavAudioProcessor>());
                     // Zde by se v budoucnu mohla vracet instance SttAudioProcessor
                 };

                 session = await _sessionManager.CreateSessionAsync(sipUserAgent, sipRequest, receiverFactory, processorFactory);

                 if (session == null)
                 {
                     // Session se nepodařilo vytvořit nebo přidat do manažeru (např. duplicate Call-ID)
                      _logger.LogError($"[{callId}] Failed to create or register session.");
                      rtpClient?.Dispose(); // Uklidit UDP klienty
                      rtcpClient?.Dispose();
                      // Poslat chybu zpět? (Např. 500 Internal Server Error)
                      var errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.ServerInternalError, "Session creation failed");
                      await _sipTransport.SendResponseAsync(remoteEndPoint, errorResponse);
                      return;
                 }

                 // 5. Přijmout hovor (poslat 200 OK s SDP)
                 var uasTransaction = _sipTransport.CreateUASTransaction(sipRequest, remoteEndPoint, localEndPoint, null);
                 var okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                 okResponse.Header.ContentType = "application/sdp";
                 okResponse.Body = sdpAnswer;
                 await uasTransaction.SendFinalResponse(okResponse);
                  _logger.LogInformation($"[{callId}] Sent 200 OK with SDP to {remoteEndPoint}. Session started.");


                 // 6. Spustit úlohy session na pozadí (asynchronně)
                 // Použijeme token hostované služby pro možnost zrušení při vypínání aplikace
                 _ = session.StartAsync(cancellationToken); // Spustit a nečekat

             }
             catch (Exception ex)
             {
                 _logger.LogError(ex, $"[{callId}] Critical error processing INVITE from {remoteEndPoint}");
                 // Uklidit zdroje, pokud byly alokovány
                 rtpClient?.Dispose();
                 rtcpClient?.Dispose();
                 // Pokud session byla vytvořena, pokusit se ji ukončit a odstranit
                  if (session != null)
                  {
                       _logger.LogWarning($"[{callId}] Attempting to terminate partially created session due to error.");
                       await _sessionManager.TerminateSessionAsync(callId); // Terminate zavolá Dispose
                  }

                 // Pokusit se poslat chybovou odpověď
                 try
                 {
                     var errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.ServerInternalError, $"Invite processing error: {ex.Message}");
                     await _sipTransport.SendResponseAsync(remoteEndPoint, errorResponse);
                 }
                 catch (Exception sendEx) { _logger.LogError(sendEx, $"[{callId}] Failed to send error response after INVITE processing failure."); }
             }
        }

        private async Task ProcessCancelAsync(SIPEndPoint remoteEndPoint, SIPRequest cancelRequest)
        {
            string callId = cancelRequest.Header.CallId;
             _logger.LogInformation($"[{callId}] Processing CANCEL from {remoteEndPoint}.");

            // Najít původní INVITE transakci
            // SIPSorcery toto často zvládne interně, pokud je CANCEL přijat včas.
            // Ale pro jistotu můžeme zkusit najít session a ukončit ji.
             var session = _sessionManager.GetSession(callId);
             if (session != null)
             {
                  _logger.LogInformation($"[{callId}] Found active session for CANCEL. Terminating session.");
                  await _sessionManager.TerminateSessionAsync(callId); // Ukončí session a uklidí zdroje

                 // Poslat OK na CANCEL
                 var cancelOkResponse = SIPResponse.GetResponse(cancelRequest, SIPResponseStatusCodesEnum.Ok, null);
                 await _sipTransport.SendResponseAsync(remoteEndPoint, cancelOkResponse);

                  // Poslat 487 Request Terminated na původní INVITE (toto může udělat i SIPUserAgent automaticky)
                  // Pokud TerminateSessionAsync zavěsí UA, mělo by se to stát. Ověříme v logu UA.
             }
             else
             {
                 _logger.LogWarning($"[{callId}] No active session found for CANCEL request. Sending 481.");
                 // Pokud session nenajdeme (CANCEL přišel pozdě nebo INVITE selhal), pošleme 481
                 var notFoundResponse = SIPResponse.GetResponse(cancelRequest, SIPResponseStatusCodesEnum.CallLegTransactionDoesNotExist, null);
                 await _sipTransport.SendResponseAsync(remoteEndPoint, notFoundResponse);
             }
        }


        // --- Pomocné metody ---

        private bool TryGetAvailableRtpPorts(IPAddress localIp, out int rtpPort, out int rtcpPort)
        {
             rtpPort = -1;
             rtcpPort = -1;
             int attempts = 0;
             const int maxAttempts = 50; // Omezit počet pokusů

             var activePorts = _sessionManager.GetAllSessions()
                                .Select(s => s is CallSession cs ? cs.UserAgent.Dialogue?.LocalRtpEndPoint?.Port : (int?)null) // Získat RTP porty aktivních sessions
                                .Where(p => p.HasValue)
                                .Select(p => p.Value)
                                .ToHashSet(); // Použít HashSet pro rychlé vyhledávání


             while (attempts < maxAttempts)
             {
                 int port = _random.Next(_options.RtpPortMin, _options.RtpPortMax + 1);
                 if (port % 2 != 0) port--; // Zajistit sudý port

                 // Zkontrolovat, zda port nebo port+1 není v aktivních sessions
                 if (!activePorts.Contains(port) && !activePorts.Contains(port + 1))
                 {
                     // Otestovat, zda systém porty již nepoužívá
                     try
                     {
                         using var testRtpSocket = new UdpClient(new IPEndPoint(localIp, port));
                         using var testRtcpSocket = new UdpClient(new IPEndPoint(localIp, port + 1));
                         // Pokud se podařilo otevřít, jsou pravděpodobně volné
                         rtpPort = port;
                         rtcpPort = port + 1;
                         return true;
                     }
                     catch (SocketException ex)
                     {
                          _logger.LogTrace($"Ports {port}/{port + 1} seem to be in use by the system: {ex.SocketErrorCode}");
                     }
                 }
                 attempts++;
             }

             _logger.LogError($"Could not find available RTP/RTCP ports in range {_options.RtpPortMin}-{_options.RtpPortMax} after {maxAttempts} attempts.");
             return false;
        }

        private string CreateSdpResponse(IPAddress localIpAddress, int rtpPort)
        {
            var sdp = new StringBuilder();
            sdp.AppendLine("v=0");
            sdp.AppendLine($"o=- {DateTimeOffset.UtcNow.ToUnixTimeSeconds()} {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()} IN IP4 {localIpAddress}");
            sdp.AppendLine("s=Simple .NET SIP Recorder v2");
            sdp.AppendLine($"c=IN IP4 {localIpAddress}");
            sdp.AppendLine("t=0 0");
            // Nabízíme PCMU (0), PCMA (8) a DTMF (101)
            sdp.AppendLine($"m=audio {rtpPort} RTP/AVP 0 8 101");
            sdp.AppendLine("a=rtpmap:0 PCMU/8000");
            sdp.AppendLine("a=rtpmap:8 PCMA/8000");
            sdp.AppendLine("a=rtpmap:101 telephone-event/8000");
            sdp.AppendLine("a=fmtp:101 0-16");
            sdp.AppendLine("a=sendrecv");
            // sdp.AppendLine("a=ptime:20"); // Volitelné
            return sdp.ToString();
        }

         // Extension metoda pro snadnější nastavení loggeru pro SIPSorcery (může být v samostatném souboru)
         // [Stejná jako v předchozí odpovědi]
    }

     // Extension metoda pro snadnější nastavení loggeru pro SIPSorcery (může být v samostatném souboru)
    public static class SipsorceryLoggingExtensions
    {
        public static Microsoft.Extensions.Logging.ILoggerFactory ToSIPFactory(this Microsoft.Extensions.Logging.ILogger logger)
        {
            return new SipLoggerFactory(logger);
        }

         private class SipLoggerFactory : Microsoft.Extensions.Logging.ILoggerFactory
         {
             private readonly Microsoft.Extensions.Logging.ILogger _logger;
             public SipLoggerFactory(Microsoft.Extensions.Logging.ILogger logger) { _logger = logger; }
             public void AddProvider(ILoggerProvider provider) { } // Ignored
             public Microsoft.Extensions.Logging.ILogger CreateLogger(string categoryName) => _logger; // Vždy vrátí ten samý logger
             public void Dispose() { }
         }
    }
}
```

**8. Konfigurace DI a API (`Program.cs`)**

```csharp
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SimpleSipWithSttBuffer;
using SimpleSipWithSttBuffer.Interfaces;
using SimpleSipWithSttBuffer.Services;
using System.Linq; // Pro LINQ v API

var builder = WebApplication.CreateBuilder(args);

// --- Konfigurace ---
builder.Services.Configure<SipServerOptions>(builder.Configuration.GetSection("SipServer"));

// --- Logging ---
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
// Přidáme logger factory do DI, aby byla dostupná ostatním službám
builder.Services.AddSingleton(sp => sp.GetRequiredService<ILoggerFactory>());


// --- Registrace Služeb pro DI ---
builder.Services.AddSingleton<ICallSessionManager, CallSessionManager>();
// SIP Server běží jako Hosted Service
builder.Services.AddHostedService<SipServerService>();

// Pro API nepotřebujeme controllers, použijeme Minimal APIs
builder.Services.AddRouting();


var app = builder.Build();

// --- Konfigurace Middleware a Endpoints ---
app.UseRouting();

// --- API Endpoints ---
app.MapGet("/api/calls", (ICallSessionManager sessionManager) =>
{
    var calls = sessionManager.GetAllSessions().Select(s => new
    {
        id = s.CallId,
        from = s.InitialInviteRequest?.Header?.From?.ToString(),
        to = s.InitialInviteRequest?.Header?.To?.ToString(),
        startTime = s.StartTime,
        // Získání informací o receiveru a processoru, pokud je to možné a bezpečné
        receiverInfo = s is CallSession cs ? $"{cs.GetType().Name}({cs.UserAgent?.Dialogue?.LocalRtpEndPoint})" : "N/A", // Příklad
        processorInfo = s is CallSession cs2 ? $"{cs2.GetType().Name}" : "N/A", // Příklad
        customHeaders = s.InitialInviteRequest?.Header?.UnknownHeaders
    });
    return Results.Ok(new { calls });
});

app.MapDelete("/api/calls/{callId}", async (string callId, ICallSessionManager sessionManager, ILogger<Program> logger) =>
{
     logger.LogInformation($"API request to delete call {callId}");
     var session = sessionManager.GetSession(callId);
    if (session == null)
    {
        return Results.NotFound(new { error = "Hovor nenalezen" });
    }
    try
    {
        // TerminateSessionAsync zavolá StopAsync na session, což zahrnuje i Hangup na UA, pokud je potřeba
        await sessionManager.TerminateSessionAsync(callId);
        return Results.Ok(new { success = true, message = "Požadavek na ukončení hovoru zpracován" });
    }
    catch (Exception ex)
    {
         logger.LogError(ex, $"Error terminating call {callId} via API");
        return Results.Problem($"Chyba při ukončování hovoru: {ex.Message}");
    }
});


app.Run(); // Spustí aplikaci (včetně IHostedService)
```

**9. Konfigurace (`appsettings.json`)**

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "SIPSorcery": "Information", // Detailnější logování SIPSorcery
      "SimpleSipWithSttBuffer": "Debug" // Detailnější logování naší aplikace
    }
  },
  "AllowedHosts": "*",
  "SipServer": {
    "ListenIpAddress": "Any", // Nebo specifická IP adresa serveru
    "ListenPort": 5060,
    "RtpPortMin": 10000,
    "RtpPortMax": 19998,
    "WavRecordingDirectory": "RecordedCalls"
  }
}
```

**Shrnutí změn a vlastností:**

- **DI:** Všechny klíčové komponenty (`CallSessionManager`, `SipServerService`, jednotlivé části `CallSession`) jsou spravovány DI kontejnerem.
- **Modularita:** Jasná rozhraní (`IAudioInputReceiver`, `IAudioBuffer`, `IAudioProcessor`, `ICallSession`, `ICallSessionManager`) umožňují snadnou výměnu implementací.
- **Session Management:** `CallSessionManager` spravuje izolované instance `CallSession` pro každý hovor v `ConcurrentDictionary`.
- **Buffer:** `BlockingCollectionAudioBuffer` slouží jako thread-safe buffer mezi přijímačem a procesorem.
- **Oddělení Zodpovědností:** Každá třída má jasně danou roli.
- **Životní cyklus:** `SipServerService` jako `IHostedService` správně integruje start a stop SIP serveru s životním cyklem aplikace. `CallSession` spravuje životní cyklus svých komponent (receiver, buffer, processor) a používá `CancellationToken` pro řízení.
- **Továrny (Implicitní):** Místo explicitních továren v DI předáváme do `CallSessionManager.CreateSessionAsync` lambda výrazy (`Func<T>`), které vytvoří instance `IAudioInputReceiver` a `IAudioProcessor` až v momentě potřeby. To zajišťuje, že každá session dostane své vlastní instance. UDP klienti jsou vytvořeni v `SipServerService` a předáni do konstruktoru `RtpAudioReceiver` přes lambda factory.
- **WAV Zápis:** `WavAudioProcessor` nyní čte data z bufferu a zapisuje je do souboru.
- **Konfigurace:** Klíčové parametry jsou načítány z `appsettings.json`.
- **API:** Minimal API se používá pro jednoduché endpointy pro výpis a ukončení hovorů, které interagují s `ICallSessionManager`.

Tento refaktorovaný kód je mnohem flexibilnější a připravenější na budoucí rozšíření, jako je přidání STT procesoru nebo podpora jiného vstupního protokolu (např. WebRTC). Stačilo by implementovat nová rozhraní `IAudioInputReceiver` nebo `IAudioProcessor` a upravit tovární metody v `SipServerService.ProcessInviteAsync`.
