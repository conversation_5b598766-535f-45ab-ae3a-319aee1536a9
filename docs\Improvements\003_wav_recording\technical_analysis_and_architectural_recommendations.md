# WAV Recording Integration Architecture Plan

## Executive Summary
This document outlines the architecture and implementation plan for reintroducing WAV recording capability to the voice processing service while maintaining the existing Speech-to-Text (STT) integration with Phonexia.

## Current State Analysis

### Audio Processing Pipeline
```mermaid
graph LR
    A[RTP Stream] --> B[RtpAudioReceiver]
    B --> C[IAudioBuffer]
    C --> D[PhonexiaSttProcessor]
    D --> E[Phonexia API]
    D --> F[Kafka Events]
```

### Key Components
1. **RtpAudioReceiver**: Receives RTP packets, extracts audio payload
2. **IAudioBuffer**: BlockingCollection-based buffer for audio data
3. **PhonexiaSttProcessor**: Consumes audio, sends to Phonexia API
4. **CallSession**: Orchestrates the audio pipeline lifecycle
5. **SipServerService**: Creates sessions, uses enhanced factory pattern

### Existing But Unwired Components
- `WavAudioProcessor`: Complete implementation for WAV recording
- `WavAudioProcessorFactory`: Factory for creating WAV processors
- Configuration: `WavRecordingDirectory` in appsettings.json
- Docker mount: `./data/recordings:/app/recordings`

## Proposed Solution: Composite Processor Pattern

### Architecture Design
```mermaid
graph TB
    A[RTP Stream] --> B[RtpAudioReceiver]
    B --> C[IAudioBuffer]
    C --> D[BroadcastingAudioBuffer]
    D --> E[Buffer Copy 1]
    D --> F[Buffer Copy 2]
    E --> G[PhonexiaSttProcessor]
    F --> H[WavAudioProcessor]
    G --> I[Phonexia API]
    H --> J[WAV File]
```

### Implementation Approach: CompositeAudioProcessor

Create a composite processor that manages multiple child processors, each consuming from their own buffer copy:

```csharp
public class CompositeAudioProcessor : IAudioProcessor
{
    private readonly List<IAudioProcessor> _processors;
    private readonly List<IAudioBuffer> _buffers;
    private readonly IAudioBuffer _sourceBuffer;
    
    public async Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken ct)
    {
        // Broadcast audio data to all child buffers
        // Start all child processors in parallel
    }
}
```

## Detailed Implementation Plan

### 1. Create BroadcastingAudioBuffer
**Purpose**: Duplicates audio packets to multiple consumer buffers

**Key Features**:
- Implements IAudioBuffer interface
- Takes audio from source buffer
- Copies to multiple target buffers
- Handles backpressure and buffering

**File**: `voice-processing-service/Services/BroadcastingAudioBuffer.cs`

### 2. Create CompositeAudioProcessor
**Purpose**: Manages multiple audio processors in parallel

**Key Features**:
- Implements IAudioProcessor interface
- Creates child buffers for each processor
- Starts processors in parallel tasks
- Coordinates lifecycle (init, start, dispose)
- Aggregates errors and status

**File**: `voice-processing-service/Services/CompositeAudioProcessor.cs`

### 3. Create CompositeAudioProcessorFactory
**Purpose**: Factory for creating composite processors with both WAV and STT

**Configuration Schema**:
```json
{
  "SipServer": {
    "WavRecordingEnabled": true,
    "WavRecordingDirectory": "RecordedCalls"
  }
}
```

**Factory Logic**:
```csharp
public IAudioProcessor CreateProcessor(string callId, string caller, string called)
{
    var processors = new List<IAudioProcessor>();
    
    // Always add STT processor
    processors.Add(_sttFactory.CreateProcessor(callId, caller, called));
    
    // Conditionally add WAV processor
    if (_options.WavRecordingEnabled)
    {
        processors.Add(_wavFactory.CreateProcessor(callId, caller, called));
    }
    
    return new CompositeAudioProcessor(processors);
}
```

**File**: `voice-processing-service/Services/CompositeAudioProcessorFactory.cs`

### 4. Update Dependency Injection (Program.cs)
```csharp
// Register component factories
builder.Services.AddSingleton<WavAudioProcessorFactory>();
builder.Services.AddSingleton<PhonexiaSttProcessorFactory>();
builder.Services.AddSingleton<CompositeAudioProcessorFactory>();

// Update enhanced factory registration
builder.Services.AddTransient<Func<string, string, string, IAudioProcessor>>(sp =>
{
    var factory = sp.GetRequiredService<CompositeAudioProcessorFactory>();
    return (callId, caller, called) => factory.CreateProcessor(callId, caller, called);
});
```

### 5. Update WavAudioProcessorFactory
**Enhancements**:
- Accept caller/called party parameters
- Include metadata in filename: `call_{callId}_{caller}_{called}_{timestamp}.wav`
- Create directory structure: `RecordedCalls/YYYY-MM-DD/`

## Alternative Approach: Buffer-Level Broadcasting

Instead of CompositeAudioProcessor, implement broadcasting at the buffer level:

```mermaid
graph TB
    A[RtpAudioReceiver] --> B[AudioBufferBroadcaster]
    B --> C[STT Buffer]
    B --> D[WAV Buffer]
    C --> E[PhonexiaSttProcessor]
    D --> F[WavAudioProcessor]
```

This approach modifies CallSession to use a broadcasting buffer:

```csharp
public class CallSession
{
    private void InitializeAudioPipeline()
    {
        var primaryBuffer = new AudioBuffer();
        var sttBuffer = new AudioBuffer();
        var wavBuffer = new AudioBuffer();
        
        var broadcaster = new AudioBufferBroadcaster(
            primaryBuffer, 
            new[] { sttBuffer, wavBuffer });
        
        // Start broadcaster task
        _broadcasterTask = Task.Run(() => 
            broadcaster.StartBroadcastingAsync(_cancellationToken));
        
        // Start processors with their dedicated buffers
        _sttTask = _sttProcessor.StartProcessingAsync(sttBuffer, _cancellationToken);
        _wavTask = _wavProcessor.StartProcessingAsync(wavBuffer, _cancellationToken);
    }
}
```

## Configuration Management

### AppSettings Structure
```json
{
  "SipServer": {
    "ListenPort": 5060,
    "WavRecording": {
      "Enabled": true,
      "Directory": "RecordedCalls",
      "MaxFileSizeMB": 500,
      "RetentionDays": 30,
      "IncludeMetadata": true,
      "FileNamePattern": "call_{callId}_{caller}_{called}_{timestamp}"
    }
  }
}
```

### Environment Variables
```yaml
SipServer__WavRecording__Enabled: "true"
SipServer__WavRecording__Directory: "/app/recordings"
SipServer__WavRecording__RetentionDays: "30"
```

## Resource Management & Error Handling

### 1. Graceful Degradation
- If WAV recording fails, STT continues
- Log errors but don't crash the session
- Implement circuit breaker for disk I/O issues

### 2. Resource Cleanup
- Ensure all buffers are properly disposed
- Finalize WAV files even on errors
- Clean up incomplete recordings

### 3. Performance Considerations
- Use async I/O for file operations
- Implement buffering for WAV writes
- Monitor memory usage with multiple buffers
- Consider compression for long recordings

## Testing Strategy

### Unit Tests
1. **CompositeAudioProcessor Tests**
   - Verify parallel processing
   - Test error isolation
   - Validate lifecycle management

2. **BroadcastingAudioBuffer Tests**
   - Test packet duplication
   - Verify no data loss
   - Test backpressure handling

### Integration Tests
1. **End-to-End Recording**
   - Simulate call with audio
   - Verify both WAV and STT outputs
   - Test configuration changes

2. **Failure Scenarios**
   - Disk full conditions
   - Network interruptions
   - Processor crashes

## Deployment Considerations

### Docker Configuration
```dockerfile
# Ensure recordings volume is mounted
VOLUME ["/app/recordings"]

# Set appropriate permissions
RUN mkdir -p /app/recordings && \
    chmod 755 /app/recordings
```

### Docker Compose
```yaml
services:
  voice-processor:
    volumes:
      - ./data/recordings:/app/recordings
    environment:
      - SipServer__WavRecording__Enabled=true
      - SipServer__WavRecording__Directory=/app/recordings
```

### Monitoring & Observability
1. **Metrics**
   - Recording success/failure rate
   - Average file sizes
   - Disk usage trends

2. **Logging**
   - Call start/stop with recording status
   - File paths for correlation
   - Error details for debugging

3. **Health Checks**
   - Disk space availability
   - Write permissions verification
   - Recording process status

## Migration Path

### Phase 1: Implement Core Components
1. Create CompositeAudioProcessor
2. Create BroadcastingAudioBuffer
3. Update factories

### Phase 2: Testing & Validation
1. Unit test coverage
2. Integration testing
3. Performance testing

### Phase 3: Gradual Rollout
1. Deploy with feature flag disabled
2. Enable for specific test calls
3. Monitor and validate
4. Full production enablement

## Risk Mitigation

### Identified Risks
1. **Increased Memory Usage**: Multiple buffers per call
   - Mitigation: Implement buffer size limits
   
2. **Disk Space Exhaustion**: Large WAV files
   - Mitigation: Retention policies, compression
   
3. **Performance Degradation**: Additional I/O operations
   - Mitigation: Async I/O, buffering strategies

4. **Data Loss**: Buffer overflow or crashes
   - Mitigation: Persistent buffering, graceful shutdown

## Success Criteria

1. **Functional Requirements**
   - ✅ WAV files recorded for all calls
   - ✅ STT continues to work unchanged
   - ✅ Configurable enable/disable
   - ✅ Proper file naming with metadata

2. **Non-Functional Requirements**
   - ✅ No degradation in STT latency
   - ✅ < 5% increase in memory usage
   - ✅ < 10ms additional latency
   - ✅ 99.9% recording success rate

## Recommended Approach

Based on the analysis, I recommend the **CompositeAudioProcessor** approach because:

1. **Cleaner Architecture**: Encapsulates complexity in a single component
2. **Easier Testing**: Can test composite behavior in isolation
3. **Better Extensibility**: Easy to add more processors later
4. **Less Invasive**: Minimal changes to existing CallSession
5. **Factory Pattern**: Aligns with existing architecture

The implementation should proceed with creating the CompositeAudioProcessor and CompositeAudioProcessorFactory, then updating the DI configuration to use them.