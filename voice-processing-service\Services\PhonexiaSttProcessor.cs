using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.WebSockets;
using System.Reflection;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Models;
using NAudio.Codecs;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Metadata pro sdílenou Phonexia session.
    /// </summary>
    public class PhonexiaSessionInfo
    {
        public string SessionId { get; set; } = string.Empty;
        public string CallerParty { get; set; } = string.Empty;
        public string CalledParty { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime LastUsedAt { get; set; }
        public int ActiveStreams { get; set; }
        public bool IsValid { get; set; } = true;
    }

    /// <summary>
    /// Implementace procesoru pro zpracování audio dat a jejich odesl<PERSON> do Phonexia STT služby přes RTP.
    /// </summary>
    public class PhonexiaSttProcessor : IAudioProcessor
    {
        // Static session pool for sharing sessions across concurrent calls
        private static readonly ConcurrentDictionary<string, PhonexiaSessionInfo> _sessionPool = new();
        private static readonly SemaphoreSlim _sessionPoolSemaphore = new(1, 1);
        private static readonly object _sessionPoolLock = new object();
        private readonly ILogger<PhonexiaSttProcessor> _logger;
        private readonly string _callId;
        private readonly PhonexiaOptions _options;
        private readonly HttpClient _httpClient;
        private readonly KafkaProducer _kafkaProducer;
        private readonly string _connectionId;
        private readonly string _agentId;
        private readonly string _customerNumber;
        private readonly string _channelType;
        private readonly string _callerParty;
        private readonly string _calledParty;
        private ClientWebSocket _websocket;
        private ClientWebSocket _websocketAudio;
        private CancellationTokenSource _audioCts;
        private CancellationTokenSource _resultsCts;
        private Task _resultsTask;
        private string _sessionId;
        private string _streamId;
        private string _taskId;
        private bool _isDisposed = false;

        /// <summary>
        /// Identifikátor procesoru.
        /// </summary>
        public string ProcessorId => $"PhonexiaSTT_{_callId}";

        /// <summary>
        /// Vytvoří novou instanci PhonexiaSttProcessor.
        /// </summary>
        /// <param name="callId">ID hovoru.</param>
        /// <param name="connectionId">Connection ID z SIP INVITE hlavičky.</param>
        /// <param name="agentId">Agent ID z SIP INVITE hlavičky.</param>
        /// <param name="customerNumber">Číslo zákazníka z SIP INVITE hlavičky.</param>
        /// <param name="channelType">Typ kanálu z SIP INVITE hlavičky.</param>
        /// <param name="callerParty">Volající strana pro session sharing.</param>
        /// <param name="calledParty">Volaná strana pro session sharing.</param>
        /// <param name="options">Konfigurace Phonexia služby.</param>
        /// <param name="httpClient">HTTP klient pro komunikaci s Phonexia API.</param>
        /// <param name="kafkaProducer">Kafka producent.</param>
        /// <param name="logger">Logger.</param>
        public PhonexiaSttProcessor(
            string callId,
            string connectionId,
            string agentId,
            string customerNumber,
            string channelType,
            string callerParty,
            string calledParty,
            IOptions<PhonexiaOptions> options,
            HttpClient httpClient,
            KafkaProducer kafkaProducer,
            ILogger<PhonexiaSttProcessor> logger)
        {
            _callId = callId ?? throw new ArgumentNullException(nameof(callId));
            _connectionId = connectionId ?? string.Empty;
            _agentId = agentId ?? string.Empty;
            _customerNumber = customerNumber ?? string.Empty;
            _channelType = channelType ?? string.Empty;
            _callerParty = callerParty ?? string.Empty;
            _calledParty = calledParty ?? string.Empty;
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _kafkaProducer = kafkaProducer; // Může být null pro testování
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _logger.LogInformation($"[{_callId}] PhonexiaSttProcessor created. ConnectionId: {_connectionId}, AgentId: {_agentId}, CustomerNumber: {_customerNumber}, ChannelType: {_channelType}, CallerParty: {_callerParty}, CalledParty: {_calledParty}");
        }

        private FileStream _wavFileStream;
        private BinaryWriter _wavWriter;
        private long _audioDataLength = 0;
        private readonly string _recordingsDirectory = "recordings";
        private string _transcription = string.Empty;

        /// <summary>
        /// Asynchronously initialize Phonexia STT session and audio input stream.
        /// </summary>
        /// <param name="cancellationToken">Token for cancellation.</param>
        public async Task InitializeAsync(CancellationToken cancellationToken)
        {
            //return;
            _logger.LogInformation($"[{_callId}] Initializing Phonexia STT processor.");
            
            // Step 1: Get or create shared session based on caller/called party
            _sessionId = await GetOrCreateSharedSessionAsync(cancellationToken);
            _logger.LogInformation($"[{_callId}] Phonexia session established: {_sessionId}");

            // Step 2: connect to audio WebSocket and get input stream ID
            _streamId = await ConnectAudioWebSocketAsync(cancellationToken);
            _logger.LogInformation($"[{_callId}] Audio WebSocket connected. Input stream: {_streamId}");

            // Step 3: bind STT technology to input stream
            _taskId = await BindSttToStreamAsync(_streamId, cancellationToken);
            _logger.LogInformation($"[{_callId}] STT bound to stream. Task id: {_taskId}");
        }

        /// <summary>
        /// Zahájí zpracování audio dat delegováním na metodu ExecuteStartProcessingAsync.
        /// </summary>
        /// <param name="buffer">Buffer s audio daty.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            // await ProcessToWavAsync(buffer, cancellationToken);
            // return;
            _audioCts   = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken); // linked to external token for audio cancellation
            _resultsCts = new CancellationTokenSource();                                    // independent CTS for results listener
            
            // Step 4: connect to results WebSocket (non-fatal) with results CTS
            try
            {
                await ConnectToResultsWebSocketAsync(_taskId, _resultsCts.Token);
                _logger.LogInformation($"[{_callId}] Results WebSocket connected for Task: {_taskId}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"[{_callId}] Failed to connect to results WebSocket, continuing audio streaming.");
            }
            
            // Step 5: Start audio loop with audio CTS
            _resultsTask = ProcessWebSocketMessagesAsync(_resultsCts.Token);
            var receiveTask = _resultsTask;
            var audioTask    = StreamWebSocketAudioLoopAsync(buffer, _audioCts.Token);

            try
            {
                // Wait for audio streaming to complete
                await audioTask;
                try
                {
                    await receiveTask;  // ensure all STT messages are received
                }
                catch (WebSocketException wex)
                {
                    _logger.LogWarning(wex, $"[{_callId}] Error in results WebSocket receiveTask after audio complete; ignoring.");
                }
                // CleanupWebSocketsAsync will handle closing the results WebSocket.
                // After audio loop completes, start processing any buffered results
            }
            catch (WebSocketException wsEx)
            {
                _logger.LogWarning(wsEx, $"[{_callId}] WebSocketException in StartProcessingAsync: {wsEx.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during StartProcessingAsync.");
            }
            finally
            {
                // Step 6: stop transcription and cleanup WebSockets without throwing
                try
                {
                    await StopStreamAsync(CancellationToken.None); // orchestrates shutdown
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"[{_callId}] Ignored error stopping stream.");
                }
                await CleanupWebSocketsAsync();
                // Ensure passed buffer is marked complete; disposal is handled by CallSession
                try
                {
                    buffer.CompleteAdding();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"[{_callId}] Error completing buffer.");
                }
            }
        }

        private async Task GetTranscription()
        {
            if (string.IsNullOrEmpty(_transcription))
            {
                _logger.LogWarning($"[{_callId}] No transcription available.");
                return;
            }

            _logger.LogInformation($"[{_callId}] Transcription: {_transcription}");
        }

        /// <summary>
        /// Získá nebo vytvoří sdílenou Phonexia session na základě caller/called party.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>ID session.</returns>
        private async Task<string> GetOrCreateSharedSessionAsync(CancellationToken cancellationToken)
        {
            var sessionKey = $"{_callerParty}|{_calledParty}";

            await _sessionPoolSemaphore.WaitAsync(cancellationToken);
            try
            {
                // Check if we have a valid session for this caller/called party combination
                if (_sessionPool.TryGetValue(sessionKey, out var existingSession) && existingSession.IsValid)
                {
                    existingSession.LastUsedAt = DateTime.UtcNow;
                    existingSession.ActiveStreams++;
                    _logger.LogInformation($"[{_callId}] Reusing shared session: {existingSession.SessionId} for {sessionKey} (Active streams: {existingSession.ActiveStreams})");
                    return existingSession.SessionId;
                }

                // Check if we've reached the session pool limit
                if (_sessionPool.Count >= _options.SessionPoolSize)
                {
                    _logger.LogWarning($"[{_callId}] Session pool limit reached ({_options.SessionPoolSize}). Creating individual session as fallback.");
                    return await LoginAsync(cancellationToken);
                }

                // Create new shared session
                var sessionId = await LoginAsync(cancellationToken);
                var sessionInfo = new PhonexiaSessionInfo
                {
                    SessionId = sessionId,
                    CallerParty = _callerParty,
                    CalledParty = _calledParty,
                    CreatedAt = DateTime.UtcNow,
                    LastUsedAt = DateTime.UtcNow,
                    ActiveStreams = 1,
                    IsValid = true
                };

                _sessionPool[sessionKey] = sessionInfo;
                _logger.LogInformation($"[{_callId}] Created new shared session: {sessionId} for {sessionKey} (Pool size: {_sessionPool.Count}/{_options.SessionPoolSize})");
                return sessionId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to get or create shared session for {sessionKey}. Falling back to individual session.");
                return await LoginAsync(cancellationToken);
            }
            finally
            {
                _sessionPoolSemaphore.Release();
            }
        }

        /// <summary>
        /// Přihlášení k Phonexia API.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>ID session.</returns>
        private async Task<string> LoginAsync(CancellationToken cancellationToken)
        {
            try
            {
                var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_options.Username}:{_options.Password}"));
                var loginRequest = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/login");
                loginRequest.Headers.Authorization = new AuthenticationHeaderValue("Basic", authHeader);

                _logger.LogInformation($"[{_callId}] Sending login request to Phonexia API...");
                var loginResponse = await _httpClient.SendAsync(loginRequest, cancellationToken);
                loginResponse.EnsureSuccessStatusCode();
                var loginJson = await loginResponse.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogInformation($"[{_callId}] Login response: {loginJson}");

                var loginData = System.Text.Json.JsonSerializer.Deserialize<PhonexiaLoginResponse>(
                                    loginJson,
                                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                var sessionId = loginData.Result.Session.Id;
                _logger.LogInformation($"[{_callId}] Extracted session ID: '{sessionId}' (length: {sessionId?.Length})");
                return sessionId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to login to Phonexia API.");
                throw new TargetInvocationException(ex);
            }
        }


        /// <summary>
        /// Připojení STT technologie ke streamu.
        /// </summary>
        /// <param name="streamId">ID streamu.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>ID úlohy.</returns>
        private async Task<string> BindSttToStreamAsync(string inputStreamId, CancellationToken cancellationToken)
        {
            try
            {
                // Bind STT technology to the input stream
                var sttRequest = new HttpRequestMessage(HttpMethod.Post,
                    $"{_options.ApiUrl}/technologies/stt/input_stream?input_stream={inputStreamId}&model={_options.Model}");
                foreach (var kv in GetPhonexiaAuthHeaders())
                {
                    sttRequest.Headers.Add(kv.Key, kv.Value);
                }

                var sttResponse = await _httpClient.SendAsync(sttRequest, cancellationToken);
                sttResponse.EnsureSuccessStatusCode();
                var sttJson = await sttResponse.Content.ReadAsStringAsync(cancellationToken);
                using var doc = JsonDocument.Parse(sttJson);
                var root = doc.RootElement;
                // result may be named "result" or "Result"
                if (!root.TryGetProperty("result", out var resultElement) &&
                    !root.TryGetProperty("Result", out resultElement))
                {
                    throw new KeyNotFoundException("Missing 'result' property in STT bind response.");
                }
                // stream task info may be snake_case or PascalCase
                if (!resultElement.TryGetProperty("stream_task_info", out var taskInfo) &&
                    !resultElement.TryGetProperty("StreamTaskInfo", out taskInfo))
                {
                    throw new KeyNotFoundException("Missing 'stream_task_info' property in STT bind response.");
                }
                // id may be lowercase or uppercase
                if (!taskInfo.TryGetProperty("id", out var idProp) &&
                    !taskInfo.TryGetProperty("Id", out idProp))
                {
                    throw new KeyNotFoundException("Missing 'id' property in STT bind response.");
                }
                return idProp.GetString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to bind STT to stream.");
                throw new TargetInvocationException(ex);
            }
        }

        /// <summary>
        /// Připojení k WebSocket pro příjem výsledků.
        /// </summary>
        /// <param name="taskId">ID úlohy.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task ConnectToResultsWebSocketAsync(string taskId, CancellationToken cancellationToken)
        {
            ClientWebSocket tempWebSocket = null;
            try
            {
                // Create second WebSocket for receiving transcription results
                tempWebSocket = new ClientWebSocket();
                var apiUri = new Uri(_options.ApiUrl);
                var uri = new Uri($"ws://{apiUri.Host}:{apiUri.Port}/technologies/stt/input_stream?task={taskId}&interval=0.33&trigger_events=transcription&session={_sessionId}");

                // Set only WebSocket-compatible headers for results WebSocket
                try
                {
                    // Only set custom headers that are allowed for WebSocket
                    tempWebSocket.Options.SetRequestHeader("X-SessionID", _sessionId);
                    _logger.LogDebug($"[{_callId}] Set Results WebSocket X-SessionID header");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"[{_callId}] Cannot set Results WebSocket X-SessionID header: {ex.Message}");
                }
                try
                {
                    await tempWebSocket.ConnectAsync(uri, CancellationToken.None);
                }
                catch (WebSocketException wsEx)
                {
                    if (wsEx.Message.Contains("401") || wsEx.Message.Contains("Unauthorized"))
                    {
                        _logger.LogError(wsEx, $"[{_callId}] Results WebSocket 401 Unauthorized. Check session/auth headers.");
                    }
                    else
                    {
                        _logger.LogError(wsEx, $"[{_callId}] Results WebSocket connection error: {wsEx.Message}");
                    }
                    throw;
                }
                
                // Only assign to field after successful connection
                _websocket = tempWebSocket;
                tempWebSocket = null; // Prevent disposal in catch block
                                
                _logger.LogInformation($"[{_callId}] STT results WebSocket handshake complete.");

                // Start processing messages with results CTS

                // Spuštění asynchronního zpracování zpráv
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to connect to results WebSocket.");
                
                // Clean up the temporary WebSocket if connection failed
                if (tempWebSocket != null)
                {
                    try
                    {
                        tempWebSocket.Dispose();
                    }
                    catch (Exception disposeEx)
                    {
                        _logger.LogDebug(disposeEx, $"[{_callId}] Error disposing failed WebSocket connection.");
                    }
                }
                
                // Ensure _websocket remains null on failure
                _websocket = null;
                throw;
            }
        }
        /// <summary>
        /// Establishes WebSocket connection for audio input and returns input stream ID.
        /// </summary>
        private async Task<string> ConnectAudioWebSocketAsync(CancellationToken cancellationToken)
        {
            try
            {
                // Create audio WebSocket for sending data
                _websocketAudio = new ClientWebSocket();
                var apiUri = new Uri(_options.ApiUrl);
                var uri = new Uri($"ws://{apiUri.Host}:{apiUri.Port}/input_stream/websocket?frequency=8000&n_channels=1&session={_sessionId}");

                // Set only WebSocket-compatible headers
                try
                {
                    // Only set custom headers that are allowed for WebSocket
                    _websocketAudio.Options.SetRequestHeader("X-SessionID", _sessionId);
                    _logger.LogDebug($"[{_callId}] Set WebSocket X-SessionID header");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"[{_callId}] Cannot set WebSocket X-SessionID header: {ex.Message}");
                }

                _logger.LogInformation($"[{_callId}] Connecting to audio WebSocket with session ID: {_sessionId}");
                try
                {
                    await _websocketAudio.ConnectAsync(uri, CancellationToken.None);
                }
                catch (WebSocketException wsEx)
                {
                    if (wsEx.Message.Contains("401") || wsEx.Message.Contains("Unauthorized"))
                    {
                        _logger.LogError(wsEx, $"[{_callId}] Audio WebSocket 401 Unauthorized. Check session/auth headers.");
                    }
                    else
                    {
                        _logger.LogError(wsEx, $"[{_callId}] Audio WebSocket connection error: {wsEx.Message}");
                    }
                    throw;
                }
                
                // Await first text message for handshake
                var buffer = new byte[8192];
                var result = await _websocketAudio.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);
                var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                _logger.LogInformation($"[{_callId}] Audio WebSocket handshake message: {message}");
                using var doc = JsonDocument.Parse(message);
                var inputStream = doc.RootElement.GetProperty("result").GetProperty("input_stream").GetString();
                return inputStream;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to connect to audio WebSocket with session ID: {_sessionId}");
                throw;
            }
        }

        /// <summary>
        /// Streams G.711 μ-law PCM audio over WebSocket.
        /// </summary>
        private async Task StreamWebSocketAudioLoopAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            // await ProcessToWavAsync(buffer, cancellationToken);
            // return;
            // const int TimeoutMs = Timeout.Infinite;
            // await Task.Yield();
            _logger.LogInformation($"[{_callId}] Starting WebSocket audio loop.");
            _logger.LogDebug($"[{_callId}] Using buffer instance {buffer.GetHashCode()}");
            try
            {
                // Real-time streaming by fixed-size A-law frames decoded to 10ms PCM
                const int MuLawFrameSize = 80;    // 80 bytes μ-law → 160 bytes PCM = 10ms at 8000Hz mono
                const int FrameDelayMs = 10;
                try
                {
                    // Stream until cancellation or buffer completion
                    while (!cancellationToken.IsCancellationRequested)
                    {
                        byte[] audioData;
                        // Take next chunk, will respect cancellationToken
                        try
                        {
                            audioData = buffer.Take(cancellationToken);
                            int rawLen = Math.Min(10, audioData.Length);
                            _logger.LogTrace($"[{_callId}] Raw audioData first {rawLen} bytes: {BitConverter.ToString(audioData, 0, rawLen)}");
                            _logger.LogTrace($"[{_callId}] Retrieved {audioData.Length} bytes from buffer. Total bytes so far: {_audioDataLength + audioData.Length}");
                        }
                        catch (OperationCanceledException)
                        {
                            _logger.LogInformation($"[{_callId}] Audio streaming cancelled.");
                            break;
                        }
                        catch (InvalidOperationException)
                        {
                            _logger.LogInformation($"[{_callId}] Exiting audio loop due to completed buffer.");
                            break;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"[{_callId}] Exception taking data from buffer: {ex.Message}");
                            break;
                        }
                        if (audioData == null || audioData.Length == 0)
                        {
                            continue;
                        }
                        int offset = 0;
                        while (offset < audioData.Length)
                        {
                            int take = Math.Min(MuLawFrameSize, audioData.Length - offset);
                            var muLawSlice = new byte[take];
                            Array.Copy(audioData, offset, muLawSlice, 0, take);
                            var pcmFrame = DecodeMuLawToPcm(muLawSlice);
                            int pcmLen = Math.Min(10, pcmFrame.Length);
                            _logger.LogTrace($"[{_callId}] Decoded PCM frame first {pcmLen} bytes: {BitConverter.ToString(pcmFrame, 0, pcmLen)}");
                            // Diagnostic log: chunk size and timestamp
                            _logger.LogTrace($"[{_callId}] Sending μ-law slice of {take} bytes at {DateTime.UtcNow:HH:mm:ss.fff}");
                            _logger.LogTrace($"[{_callId}] Sending audio WebSocket chunk: {pcmFrame.Length} bytes (PCM), total processed: {_audioDataLength} bytes");
                            await _websocketAudio.SendAsync(
                                new ArraySegment<byte>(pcmFrame),
                                WebSocketMessageType.Binary,
                                true,
                                cancellationToken);
                            _audioDataLength += take;
                            // Pace exactly chunk duration without cancellation
                            await Task.Delay(FrameDelayMs, CancellationToken.None);
                            offset += take;
                        }
                        // If buffer drained, exit
                        if (buffer.IsAddingCompleted)
                        {
                            _logger.LogInformation($"[{_callId}] Exiting audio loop due to completed buffer.");
                            break;
                        }
                    }
                }
                finally
                {
                    // Allow STT to flush partial results
                    _logger.LogInformation($"[{_callId}] Audio buffer drained, awaiting flush of partial results.");
                    await Task.Delay(TimeSpan.FromSeconds(2), CancellationToken.None);

                    // // Send the "finalize" message over the WebSocket so the API returns the final result
                    // if (buffer.IsAddingCompleted)
                    // {
                    //     _logger.LogInformation($"[{_callId}] Sending the “finalize” message over the WebSocket.");
                        
                    //     // Signal end‐of‐stream to Phonexia:
                    //     var finalizeCmd = new { command = "finalize" };
                    //     var finalizeJson = JsonSerializer.Serialize(finalizeCmd);
                    //     var payload = Encoding.UTF8.GetBytes(finalizeJson);
                    //     await _websocketAudio.SendAsync(
                    //         new ArraySegment<byte>(payload),
                    //         WebSocketMessageType.Text,
                    //         true,
                    //         CancellationToken.None);
                    // }
                }

                _logger.LogInformation($"[{_callId}] Recording completed. Total audio data: {_audioDataLength} bytes.");
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] Audio WebSocket streaming cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error streaming audio over WebSocket: {ex.Message}");
            }
            finally
            {
                // Ensure WebSocket is closed properly
                if (_websocketAudio != null)
                {
                    try
                    {
                        if (_websocketAudio.State == WebSocketState.Open)
                        {
                            _logger.LogInformation($"[{_callId}] Attempting to close audio WebSocket. State: {_websocketAudio.State}");
                            try
                            {
                                await _websocketAudio.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing audio stream", CancellationToken.None);
                                _logger.LogInformation($"[{_callId}] Audio WebSocket CloseAsync completed. State: {_websocketAudio.State}");
                            }
                            catch (WebSocketException wsEx)
                            {
                                _logger.LogWarning(wsEx, $"[{_callId}] Audio WebSocket close handshake error: {wsEx.Message}");
                            }
                        }
                        else if (_websocketAudio.State == WebSocketState.Aborted ||
                                 _websocketAudio.State == WebSocketState.Closed ||
                                 _websocketAudio.State == WebSocketState.CloseReceived ||
                                 _websocketAudio.State == WebSocketState.CloseSent)
                        {
                            _logger.LogInformation($"[{_callId}] Audio WebSocket already closed or aborted. State: {_websocketAudio.State}");
                            // Defensive: do not attempt CloseAsync
                        }
                    }
                    catch (TaskCanceledException)
                    {
                        _logger.LogInformation($"[{_callId}] Audio WebSocket close cancelled.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{_callId}] Error closing audio WebSocket: {ex.Message}");
                    }
                    finally
                    {
                        try
                        {
                            _websocketAudio.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogDebug(ex, $"[{_callId}] Error disposing audio WebSocket.");
                        }
                        _websocketAudio = null;
                    }
                }
            }
        }

        /// <summary>
        /// Zpracování zpráv z WebSocket.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task ProcessWebSocketMessagesAsync(CancellationToken cancellationToken)
        {
            //var buffer = new byte[8192];

            try
            {
                // Check if websocket is null before proceeding
                if (_websocket == null)
                {
                    _logger.LogWarning($"[{_callId}] Results WebSocket is null, cannot process messages.");
                    return;
                }

                while (_websocket != null && _websocket.State == WebSocketState.Open && !cancellationToken.IsCancellationRequested)
                {
                    byte[] buffer = new byte[1024 * 10];
                    
                    // Double-check _websocket is still not null before ReceiveAsync
                    if (_websocket == null)
                    {
                        _logger.LogWarning($"[{_callId}] WebSocket became null during message processing.");
                        break;
                    }
                    
                    var result = await _websocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);
                    _logger.LogInformation($"[{_callId}] Processing {result.Count} bytes");

                    if (result.MessageType == WebSocketMessageType.Close)
                    {
                        // Process any final text payload and any buffered messages before closing
                        if (result.Count > 0)
                        {
                            var closePayload = Encoding.UTF8.GetString(buffer, 0, result.Count);
                            _logger.LogInformation($"[{_callId}] Received final STT WebSocket payload on Close: {closePayload}");
                            await ProcessTranscriptionResult(closePayload);
                        }
                        // Drain any remaining text frames sent before close
                        while (_websocket != null && _websocket.State == WebSocketState.Open)
                        {
                            var extraResult = await _websocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                            if (extraResult.MessageType == WebSocketMessageType.Text && extraResult.Count > 0)
                            {
                                var extraMessage = Encoding.UTF8.GetString(buffer, 0, extraResult.Count);
                                _logger.LogInformation($"[{_callId}] Draining buffered STT WebSocket message: {extraMessage}");
                                await ProcessTranscriptionResult(extraMessage);
                            }
                            else
                            {
                                break;
                            }
                        }
                        _logger.LogInformation($"[{_callId}] Response WebSocket closed by server.");
                        break;
                    }

                    if (result.MessageType == WebSocketMessageType.Binary)
                    {
                        _logger.LogInformation($"[{_callId}] Received binary message: {result.Count} bytes");
                    }

                    if (result.MessageType == WebSocketMessageType.Text)
                    {
                        var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                        _logger.LogInformation($"[{_callId}] Received STT WebSocket message: {message}");
                        await ProcessTranscriptionResult(message);
                    }
                }

                if (_websocket != null && _websocket.State == WebSocketState.Open)
                {
                    try
                    {
                        await _websocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing results stream", cancellationToken);
                    }
                    catch (WebSocketException wex)
                    {
                        _logger.LogWarning(wex, $"[{_callId}] Error closing results WebSocket in ProcessWebSocketMessagesAsync.");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] WebSocket processing cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error processing WebSocket messages.");
            }
        }

        /// <summary>
        /// Zpracování výsledků transkripce.
        /// </summary>
        /// <param name="message">JSON zpráva s výsledky.</param>
        private async Task ProcessTranscriptionResult(string message)
        {
            try
            {
                dynamic sttData = JsonConvert.DeserializeObject<dynamic>(message);

                // Process one_best_result (from working sample)
                if (sttData?.result?.one_best_result?.segmentation != null)
                {
                    var text = string.Empty;
                    foreach (var segment in sttData.result.one_best_result.segmentation)
                    {
                        var wordValue = segment?.word?.ToString();
                        if (!string.IsNullOrEmpty(wordValue) && !wordValue.StartsWith("<"))
                        {
                            _logger.LogInformation($"[{_callId}] Transcribed Text (one_best): {wordValue}");
                            text += $"{wordValue} ";
                        }
                    }
                    _logger.LogInformation($"[{_callId}] Transcribed Text (one_best); Composed: {text.Trim()}");
                }

                // Process n_best_result (from working sample)
                if (sttData?.result?.n_best_result?.phrase_variants != null)
                {
                    foreach (var variant in sttData.result.n_best_result.phrase_variants)
                    {
                        if (variant?.variant != null)
                        {
                            var text = string.Empty;
                            foreach (var phrase in variant.variant)
                            {
                                var phraseValue = phrase?.phrase?.ToString();
                                if (!string.IsNullOrEmpty(phraseValue))
                                {
                                    text += $"Transcribed Text (n_best): {phraseValue}\r\n\t";
                                }
                            }
                            _logger.LogInformation($"[{_callId}] {text}");
                        }
                    }
                }

                // Process confusion_network_result (from working sample)
                if (sttData?.result?.confusion_network_result != null)
                {
                    foreach (var item in sttData.result.confusion_network_result)
                    {
                        var wordValue = item?.word?.ToString();
                        if (!string.IsNullOrEmpty(wordValue))
                        {
                            _logger.LogInformation($"[{_callId}] Transcribed Text (confusion_network): {wordValue}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error processing transcription result dynamically. Raw message: {message}");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Odeslání transkripce do Kafka brokeru.
        /// </summary>
        /// <param name="segment">Segment transkripce.</param>
        private async void SendTranscriptionToKafka(PhonexiaSegment segment)
        {
            // Pokud není Kafka producent k dispozici, pouze zalogujeme a skončíme
            if (_kafkaProducer == null)
            {
                _logger.LogDebug($"[{_callId}] Kafka producer not available, skipping: {segment.Word}");
                return;
            }

            try
            {
                var transcriptionMessage = new TranscriptionMessage
                {
                    ConnectionId = _connectionId,
                    AgentId = _agentId,
                    CustomerNumber = _customerNumber,
                    ChannelType = _channelType,
                    Timestamp = DateTime.UtcNow,
                    Text = segment.Word,
                    Confidence = segment.Confidence,
                    StartTime = segment.Start,
                    EndTime = segment.End
                };

                var json = System.Text.Json.JsonSerializer.Serialize(transcriptionMessage);
                await _kafkaProducer.ProduceAsync(_callId, json, CancellationToken.None);
                _logger.LogDebug($"[{_callId}] Transcription sent to Kafka: {segment.Word}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error sending transcription to Kafka.");
            }
        }

        /// <summary>
        /// Provede zpracování audio dat z bufferu: vytvoří WAV soubor, zapisuje data z bufferu, aktualizuje WAV hlavičku a loguje průběh.
        /// </summary>
        /// <param name="buffer">Buffer s audio daty.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task ProcessToWavAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{_callId}] Starting audio recording to WAV file.");

            try
            {
                // Vytvoření adresáře pro nahrávky, pokud neexistuje
                if (!Directory.Exists(_recordingsDirectory))
                {
                    Directory.CreateDirectory(_recordingsDirectory);
                }

                // Vytvoření WAV souboru
                string wavFilePath = Path.Combine(_recordingsDirectory, $"{_callId}_{DateTime.Now:yyyyMMdd_HHmmss}.wav");
                _wavFileStream = new FileStream(wavFilePath, FileMode.Create, FileAccess.Write);
                _wavWriter = new BinaryWriter(_wavFileStream);

                // Zápis prázdné WAV hlavičky (doplníme ji později)
                WriteEmptyWavHeader();

                _logger.LogInformation($"[{_callId}] Recording to file: {wavFilePath}");

                // Počet neúspěšných pokusů o získání dat
                int emptyAttempts = 0;

                // Zpracování audio dat z bufferu - OPRAVENO
                while (!cancellationToken.IsCancellationRequested)
                {
                    // Pokus o získání dat z bufferu s timeoutem
                    if (buffer.TryTake(out byte[] audioData, 100, cancellationToken))
                    {
                        // Reset počtu neúspěšných pokusů
                        emptyAttempts = 0;

                        // Explicitní log - přidáno pro diagnostiku
                        _logger.LogInformation($"[{_callId}] Processing audio chunk: {audioData.Length} bytes, total processed: {_audioDataLength} bytes");

                        // Zápis audio dat do WAV souboru
                        _wavWriter.Write(audioData);
                        _audioDataLength += audioData.Length;
                    }
                    else
                    {
                        // Počítání neúspěšných pokusů
                        emptyAttempts++;

                        // Po 50 neúspěšných pokusech (přibližně 5 sekund) vypíšeme log pro diagnostiku
                        if (emptyAttempts % 50 == 0)
                        {
                            _logger.LogDebug($"[{_callId}] No data received from buffer for ~{emptyAttempts/10} seconds. Buffer completed: {buffer.IsCompleted}");
                        }

                        // Pokud je buffer označen jako dokončený a nemá žádná data, končíme
                        if (buffer.IsCompleted)
                        {
                            _logger.LogInformation($"[{_callId}] Buffer completed, ending recording.");
                            break;
                        }
                    }
                }

                // Aktualizace WAV hlavičky s konečnou velikostí
                UpdateWavHeader();

                _logger.LogInformation($"[{_callId}] Recording completed. Total audio data: {_audioDataLength} bytes.");
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] Recording cancelled.");
                // I při zrušení aktualizujeme WAV hlavičku
                UpdateWavHeader();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during recording: {ex.Message}");
            }
            finally
            {
                // Uzavření souboru
                _wavWriter?.Dispose();
                _wavFileStream?.Dispose();
                _logger.LogInformation($"[{_callId}] Recording stopped.");
            }
        }

        /// <summary>
        /// Zapíše prázdnou WAV hlavičku do souboru.
        /// </summary>
        private void WriteEmptyWavHeader()
        {
            // RIFF hlavička
            _wavWriter.Write(Encoding.ASCII.GetBytes("RIFF")); // ChunkID
            _wavWriter.Write(0); // ChunkSize - doplníme později
            _wavWriter.Write(Encoding.ASCII.GetBytes("WAVE")); // Format

            // fmt podchunk
            _wavWriter.Write(Encoding.ASCII.GetBytes("fmt ")); // Subchunk1ID
            _wavWriter.Write(16); // Subchunk1Size (16 pro PCM)
            _wavWriter.Write((short)7); // AudioFormat (7 pro G.711 μ-law)
            _wavWriter.Write((short)1); // NumChannels (1 pro mono)
            _wavWriter.Write(8000); // SampleRate (8000 Hz)
            _wavWriter.Write(8000 * 1 * 1); // ByteRate (SampleRate * NumChannels * BitsPerSample/8)
            _wavWriter.Write((short)(1 * 1)); // BlockAlign (NumChannels * BitsPerSample/8)
            _wavWriter.Write((short)8); // BitsPerSample (8 bit)

            // data podchunk
            _wavWriter.Write(Encoding.ASCII.GetBytes("data")); // Subchunk2ID
            _wavWriter.Write(0); // Subchunk2Size - doplníme později
        }

        /// <summary>
        /// Aktualizuje WAV hlavičku s konečnou velikostí audio dat.
        /// </summary>
        private void UpdateWavHeader()
        {
            if (_wavFileStream != null && _wavWriter != null)
            {
                // Uložení aktuální pozice
                long currentPosition = _wavFileStream.Position;

                // Aktualizace ChunkSize (4 + (8 + Subchunk1Size) + (8 + Subchunk2Size))
                _wavFileStream.Seek(4, SeekOrigin.Begin);
                _wavWriter.Write((uint)(36 + _audioDataLength));

                // Aktualizace Subchunk2Size
                _wavFileStream.Seek(40, SeekOrigin.Begin);
                _wavWriter.Write((uint)_audioDataLength);

                // Návrat na původní pozici
                _wavFileStream.Seek(currentPosition, SeekOrigin.Begin);
            }
        }

        /// <summary>
        /// Ukončení streamu.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>

        /// <summary>
        /// Sends audio data via UDP in a loop until buffer completes or cancellation requested.
        /// </summary>

        private async Task StopStreamAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                // Ukončení STT úlohy
                if (!string.IsNullOrEmpty(_taskId))
                {
                    var sttRequest = new HttpRequestMessage(HttpMethod.Delete, $"{_options.ApiUrl}/technologies/stt/input_stream?task={_taskId}");
                    foreach (var kv in GetPhonexiaAuthHeaders())
                    {
                        sttRequest.Headers.Add(kv.Key, kv.Value);
                    }
                    try
                    {
                        var response = await _httpClient.SendAsync(sttRequest, CancellationToken.None);
                        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                        {
                            _logger.LogWarning($"[{_callId}] Phonexia API DELETE returned 404 Not Found for task {_taskId}. This may indicate already deleted or invalid task.");
                        }
                        else
                        {
                            response.EnsureSuccessStatusCode();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{_callId}] Error sending DELETE to Phonexia API for task {_taskId}.");
                    }
                }
        
                // Cancel audio loop first, ensuring all queued audio is sent
                if (_audioCts != null)
                {
                    _audioCts.Cancel();
                    _audioCts.Dispose();
                    _audioCts = null;
                }
        
                // Then await results listener so transcripts can be flushed before cancelling
                if (_resultsCts != null)
                {
                    if (_resultsTask != null)
                    {
                        await _resultsTask;
                    }
                    // Cancel results after listener has completed
                    _resultsCts.Cancel();
                    _resultsCts.Dispose();
                    _resultsCts = null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error stopping stream.");
            }
        }
        /// <summary>
        /// Cleans up and disposes the audio and result WebSockets.
        /// </summary>
        // Helper methods to decode G.711 A-law to PCM s16le
        private static byte[] DecodeAlawToPcm(byte[] alawData)
        {
            var pcm = new byte[alawData.Length * 2];
            for (int i = 0; i < alawData.Length; i++)
            {
                short sample = DecodeAlawSample(alawData[i]);
                pcm[2 * i] = (byte)(sample);
                pcm[2 * i + 1] = (byte)(sample >> 8);
            }
            return pcm;
        }
        
        private static short DecodeAlawSample(byte alaw)
        {
            alaw ^= 0x55;
            int t = (alaw & 0x0F) << 4;
            int seg = (alaw & 0x70) >> 4;
            switch (seg)
            {
                case 0:
                    t += 8;
                    break;
                case 1:
                    t += 0x108;
                    break;
                default:
                    t += 0x108;
                    t <<= (seg - 1);
                    break;
            }
            return (short)((alaw & 0x80) != 0 ? t : -t);
        }
        
        // Helper methods to decode G.711 μ-law to PCM s16le
        private static byte[] DecodeMuLawToPcm(byte[] muLawData)
        {
            var pcm = new byte[muLawData.Length * 2];
            for (int i = 0; i < muLawData.Length; i++)
            {
                short sample = MuLawDecoder.MuLawToLinearSample(muLawData[i]);
                pcm[2 * i] = (byte)(sample);
                pcm[2 * i + 1] = (byte)(sample >> 8);
            }
            return pcm;
        }
        private async Task CleanupWebSocketsAsync()
        {
            try
            {
                // Cleanup audio WebSocket with thread-safe approach
                var audioWs = _websocketAudio;
                if (audioWs != null)
                {
                    try
                    {
                        if (audioWs.State == WebSocketState.Open)
                        {
                            await audioWs.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                        }
                    }
                    catch (WebSocketException wex)
                    {
                        _logger.LogDebug(wex, $"[{_callId}] Audio WebSocket closed with exception.");
                    }
                    catch (ObjectDisposedException)
                    {
                        _logger.LogDebug($"[{_callId}] Audio WebSocket already disposed.");
                    }
                    finally
                    {
                        try
                        {
                            audioWs.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogDebug(ex, $"[{_callId}] Error disposing audio WebSocket.");
                        }
                        _websocketAudio = null;
                    }
                }

                // Cleanup results WebSocket with thread-safe approach
                var resultsWs = _websocket;
                if (resultsWs != null)
                {
                    try
                    {
                        if (resultsWs.State == WebSocketState.Open)
                        {
                            await resultsWs.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                        }
                    }
                    catch (WebSocketException wex)
                    {
                        _logger.LogDebug(wex, $"[{_callId}] Results WebSocket closed with exception.");
                    }
                    catch (ObjectDisposedException)
                    {
                        _logger.LogDebug($"[{_callId}] Results WebSocket already disposed.");
                    }
                    finally
                    {
                        try
                        {
                            resultsWs.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogDebug(ex, $"[{_callId}] Error disposing results WebSocket.");
                        }
                        _websocket = null;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error cleaning up WebSockets.");
            }
        }

        /// <summary>
        /// Vyčistí sdílenou session při ukončení procesoru.
        /// </summary>
        private async Task CleanupSharedSessionAsync()
        {
            if (string.IsNullOrEmpty(_callerParty) || string.IsNullOrEmpty(_calledParty))
            {
                return; // No session sharing was used
            }

            var sessionKey = $"{_callerParty}|{_calledParty}";

            await _sessionPoolSemaphore.WaitAsync();
            try
            {
                if (_sessionPool.TryGetValue(sessionKey, out var sessionInfo))
                {
                    sessionInfo.ActiveStreams--;
                    sessionInfo.LastUsedAt = DateTime.UtcNow;

                    _logger.LogInformation($"[{_callId}] Decremented active streams for session {sessionInfo.SessionId}: {sessionInfo.ActiveStreams}");

                    // If no more active streams, mark session for potential cleanup
                    if (sessionInfo.ActiveStreams <= 0)
                    {
                        _logger.LogInformation($"[{_callId}] Session {sessionInfo.SessionId} has no active streams, keeping for potential reuse");
                        // Note: We don't immediately remove the session to allow for quick reuse
                        // Session cleanup can be implemented later with a background timer if needed
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during shared session cleanup for {sessionKey}");
            }
            finally
            {
                _sessionPoolSemaphore.Release();
            }
        }

        /// <summary>
        /// Uvolní prostředky.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
            {
                return;
            }

            _logger.LogInformation($"[{_callId}] Disposing PhonexiaSttProcessor.");

            try
            {
                // Ukončení streamu
                _ = StopStreamAsync().ConfigureAwait(false);

                // Cleanup shared session
                _ = CleanupSharedSessionAsync().ConfigureAwait(false);

                // Uzavření WAV souboru
                _wavWriter?.Dispose();
                _wavFileStream?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during PhonexiaSttProcessor disposal.");
            }

            _isDisposed = true;
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Returns all required headers for Phonexia authentication/session propagation.
        /// </summary>
        private Dictionary<string, string> GetPhonexiaAuthHeaders()
        {
            var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_options.Username}:{_options.Password}"));
            var headers = new Dictionary<string, string>
            {
                { "Accept", "application/json" },
                { "X-SessionID", _sessionId },
                { "Authorization", "Basic " + authHeader },
                { "Cookie", $"session={_sessionId}" }
            };

            // Log headers for debugging
            _logger.LogDebug($"[{_callId}] Auth headers: SessionID='{_sessionId}', Auth='{authHeader?.Substring(0, Math.Min(10, authHeader?.Length ?? 0))}...'");

            return headers;
        }
    }
}
