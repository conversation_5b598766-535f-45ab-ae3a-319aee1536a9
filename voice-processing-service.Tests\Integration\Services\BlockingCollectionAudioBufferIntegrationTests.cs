using System;
using System.Threading;
using Microsoft.Extensions.Logging.Abstractions;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Integration.Services
{
    public class BlockingCollectionAudioBufferIntegrationTests
    {
        [Fact]
        public void Test_AddingAndTakingData()
        {
            var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var data1 = new byte[] { 1, 2, 3 };
            var data2 = new byte[] { 4, 5 };

            buffer.Add(data1);
            buffer.Add(data2);

            Assert.True(buffer.TryTake(out var result1, 1000, CancellationToken.None));
            Assert.Equal(data1, result1);

            Assert.True(buffer.TryTake(out var result2, 1000, CancellationToken.None));
            Assert.Equal(data2, result2);

            buffer.Dispose();
        }

        [Fact]
        public void Test_CompleteAddingAndIsCompleted()
        {
            var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());

            buffer.CompleteAdding();

            Assert.True(buffer.IsAddingCompleted);
            Assert.True(buffer.IsCompleted);
            Assert.False(buffer.TryTake(out _, 100, CancellationToken.None));

            buffer.Dispose();
        }
    }
}