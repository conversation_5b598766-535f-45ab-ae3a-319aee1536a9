﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using NAudio.Wave;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net.WebSockets;
using System.Text;
using System.Threading;

namespace PhonexiaSttConsole
{
    class Program
    {
        private static readonly string PhonexiaApiUrl = "http://localhost:8600";
        private static readonly string Username = "admin";
        private static readonly string Password = "phonexia";
        private static string _sessionId;
        private static readonly HttpClient Client = new HttpClient();
        private static ClientWebSocket _websocket;
        private static ClientWebSocket _websocketResponse;
        private static string _taskId;
        static async Task Main(string[] args)
        {
            await Login();
            await GetTechnologies();

            string taskId = await CreateWebSocketStream();

            using (var waveIn = new WaveInEvent())
            {
                waveIn.DeviceNumber = 0;
                waveIn.WaveFormat = new WaveFormat(8000, 16, 1);
                waveIn.DataAvailable += WaveIn_DataAvailable;

                Console.WriteLine("Press any key to start recording...");
                Console.ReadKey();
                Console.WriteLine("Recording Started. Press any key to stop...");

                waveIn.StartRecording();
                Console.ReadKey();
                waveIn.StopRecording();
                waveIn.Dispose();
                Console.WriteLine("Recording Stopped.");
            }
            await StopTranscription(_taskId);
            if (_websocket.State == WebSocketState.Open)
                await _websocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
            Console.WriteLine("Press any key to exit.");
            Console.ReadKey();
        }
        private static async Task Login()
        {
            Console.WriteLine("Logging in...");
            var authHeader = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes($"{Username}:{Password}"));
            var loginRequest = new HttpRequestMessage(HttpMethod.Post, $"{PhonexiaApiUrl}/login");
            loginRequest.Headers.Authorization = new AuthenticationHeaderValue("Basic", authHeader);

            var loginResponse = await Client.SendAsync(loginRequest);
            loginResponse.EnsureSuccessStatusCode();
            var loginJson = await loginResponse.Content.ReadAsStringAsync();
            dynamic loginData = JObject.Parse(loginJson);
            _sessionId = loginData.result.session.id;

            Console.WriteLine("Login successful!");
        }
        private static async Task GetTechnologies()
        {
            Console.WriteLine("Getting technologies...");
            var technologiesRequest = new HttpRequestMessage(HttpMethod.Get, $"{PhonexiaApiUrl}/technologies");
            technologiesRequest.Headers.Add("X-SessionID", _sessionId);

            var technologiesResponse = await Client.SendAsync(technologiesRequest);
            technologiesResponse.EnsureSuccessStatusCode();
            var technologiesJson = await technologiesResponse.Content.ReadAsStringAsync();
            Console.WriteLine($"Available technologies: {technologiesJson}");
        }
        //working
        private static async Task<string> CreateWebSocketStream()
        {
            Console.WriteLine("Creating Input Stream and connecting to websocket...");
            var authHeader = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes($"{Username}:{Password}"));
            _websocket = new ClientWebSocket();
            var uri = new Uri($"ws://localhost:8600/input_stream/websocket?frequency=8000&n_channels=1");

            // Only set these necessary headers
            //_websocket.Options.SetRequestHeader("Authorization", "Basic " + authHeader);
            _websocket.Options.SetRequestHeader("Accept", "application/json");
            _websocket.Options.SetRequestHeader("X-SessionID", _sessionId);

            try
            {
                await _websocket.ConnectAsync(uri, CancellationToken.None);
                Console.WriteLine("WebSocket Connected.");
                var streamJson = await ReceiveHandshake();
                dynamic streamData = JObject.Parse(streamJson);
                var _inputStreamId = streamData.result.input_stream;
                Console.WriteLine("Input Stream ID received: " + _inputStreamId);

                var sttRequest = new HttpRequestMessage(HttpMethod.Post,
                    $"{PhonexiaApiUrl}/technologies/stt/input_stream?input_stream={_inputStreamId}&model=CS_CZ_O2_6");
                sttRequest.Headers.Add("X-SessionID", _sessionId);
                HttpResponseMessage sttResponse = await Client.SendAsync(sttRequest);
                sttResponse.EnsureSuccessStatusCode();
                var sttJson = await sttResponse.Content.ReadAsStringAsync();
                dynamic sttData = JObject.Parse(sttJson);
                Console.WriteLine(sttData);
                _taskId = sttData.result.stream_task_info.id;
                Console.WriteLine("Transcription started successfully!");

                _websocketResponse = new ClientWebSocket();
                var responseUri = new Uri($"ws://localhost:8600/technologies/stt/input_stream?task={_taskId}&interval=0.33&trigger_events=transcription"); //start_segment,end_segment

                // Only set these necessary headers
                _websocketResponse.Options.SetRequestHeader("Authorization", "Basic " + authHeader);
                _websocketResponse.Options.SetRequestHeader("Accept", "application/json");
                _websocketResponse.Options.SetRequestHeader("X-SessionID", _sessionId);

                await _websocketResponse.ConnectAsync(responseUri, CancellationToken.None);
                Console.WriteLine("WebSocket transcript Connected.");
                //var streamJsonResponse = await ReceiveHandshake();
                //dynamic streamDataResponse = JObject.Parse(streamJsonResponse);
                //Console.WriteLine("Response Stream ID received: " + streamDataResponse);
                StartReceive(_websocketResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"WebSocket connection error: {ex.Message}");
            }
            return _taskId;
        }
        private static async Task StopTranscription(string taskId)
        {
            var sttRequest = new HttpRequestMessage(HttpMethod.Delete, $"{PhonexiaApiUrl}/technologies/stt/input_stream?task={taskId}");
            sttRequest.Headers.Add("X-SessionID", _sessionId);
            var sttResponse = await Client.SendAsync(sttRequest);
            sttResponse.EnsureSuccessStatusCode();
        }
        private static async Task<string> ReceiveHandshake()
        {
            WebSocketReceiveResult result = null;
            byte[] buffer = new byte[1024 * 10];
            try
            {
                result = await _websocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                Console.WriteLine("Handshake received");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"WebSocket receive error: {ex.Message}");
                return "";
            }
            if (result.MessageType == WebSocketMessageType.Close)
            {
                Console.WriteLine("WebSocket closed by server.");
                await _websocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                return "";
            }
            if (result.MessageType == WebSocketMessageType.Text)
            {
                return Encoding.UTF8.GetString(buffer, 0, result.Count);
            }
            return "";
        }
        private static async void StartReceive(ClientWebSocket websocket)
        {

            while (websocket.State == WebSocketState.Open)
            {
                WebSocketReceiveResult result = null;
                byte[] buffer = new byte[1024 * 10];
                try
                {
                    result = await websocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"WebSocket receive error: {ex.Message}");
                    break;
                }
                if (result.MessageType == WebSocketMessageType.Close)
                {
                    Console.WriteLine("WebSocket closed by server.");
                    //await websocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                    break;
                }

                if (result.MessageType == WebSocketMessageType.Binary)
                {

                }


                if (result.MessageType == WebSocketMessageType.Text)
                {
                    string message = Encoding.UTF8.GetString(buffer, 0, result.Count);

                    try
                    {
                        var sttData = JsonConvert.DeserializeObject<dynamic>(message);
                        if (sttData.result.one_best_result != null)
                        {

                            foreach (var segment in sttData.result.one_best_result.segmentation)
                            {
                                if (segment.word != null && !((string)segment.word).StartsWith("<"))
                                    Console.WriteLine($"Transcribed Text (one_best): {segment.word}");
                            }
                        }

                        if (sttData.result.n_best_result != null)
                        {


                            foreach (var variant in sttData.result.n_best_result.phrase_variants)
                            {
                                foreach (var phrase in variant.variant)
                                    Console.WriteLine($"Transcribed Text (n_best): {phrase.phrase}");
                            }



                        }

                        if (sttData.result.confusion_network_result != null)
                        {
                            foreach (var item in sttData.result.confusion_network_result)
                                Console.WriteLine($"Transcribed Text (confusion network): {item.word}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error while parsing JSON: " + ex.Message);
                    }

                }

            }
        }
        private static async void WaveIn_DataAvailable(object sender, WaveInEventArgs e)
        {
            if (e.BytesRecorded <= 0)
                return;
            try
            {
                if (_websocket.State == WebSocketState.Open)
                {
                    await _websocket.SendAsync(new ArraySegment<byte>(e.Buffer, 0, e.BytesRecorded), WebSocketMessageType.Binary, true, CancellationToken.None);
                }
                else
                {
                    Console.WriteLine("WebSocket is not open");

                }

            }
            catch (Exception ex)
            {
                Console.WriteLine("Error while sending to websocket: " + ex.Message);
            }

        }
    }
}