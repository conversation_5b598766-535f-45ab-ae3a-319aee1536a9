using System;
using System.Text.Json.Serialization;

namespace voice_processing_service.Models
{
    /// <summary>
    /// Model pro zprávu s transkripcí.
    /// </summary>
    public class TranscriptionMessage
    {
        /// <summary>
        /// Connection ID z SIP INVITE hlavičky.
        /// </summary>
        [JsonPropertyName("connection_id")]
        public string ConnectionId { get; set; }

        /// <summary>
        /// Agent ID z SIP INVITE hlavičky.
        /// </summary>
        [JsonPropertyName("agent_id")]
        public string AgentId { get; set; }

        /// <summary>
        /// Číslo zákazníka z SIP INVITE hlavičky.
        /// </summary>
        [JsonPropertyName("customer_number")]
        public string CustomerNumber { get; set; }

        /// <summary>
        /// Typ kanálu z SIP INVITE hlavičky.
        /// </summary>
        [JsonPropertyName("channel_type")]
        public string ChannelType { get; set; }

        /// <summary>
        /// <PERSON>as<PERSON><PERSON> razítko.
        /// </summary>
        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Text transkripce.
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; }

        /// <summary>
        /// Jistota přepsaného textu (0-1).
        /// </summary>
        [JsonPropertyName("confidence")]
        public double Confidence { get; set; }

        /// <summary>
        /// Čas začátku transkripce.
        /// </summary>
        [JsonPropertyName("start_time")]
        public double StartTime { get; set; }

        /// <summary>
        /// Čas konce transkripce.
        /// </summary>
        [JsonPropertyName("end_time")]
        public double EndTime { get; set; }
    }
}
