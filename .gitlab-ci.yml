# Copyright © O2 Czech Republic a.s. - All Rights Reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.
# Terms and Conditions of usage are defined in file 'LICENSE.txt', which is part of this source code package.
# ---------------------------------------------------------------
# Source: https://network.git.cz.o2/ntwcl/gitopsntw/-/tree/main/templates/repository/gitops/.gitlab-ci.yml
# Examples of extending this CI/CD configuration: https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/APPLICATION_CICD_MODS.md
# ---------------------------------------------------------------
include:
  - project: "ntwcl/gitopsntw"
    ref: HEAD
    file:
      - "/templates/cicd/gitlab-ci-deployment_form.yml"
      - "/templates/cicd/gitlab-ci-deployment.yml"
# ---------------------------------------------------------------

stages:
  - gitops:deployment
  - build
  - deploy

## BUILD
### Building image
build and publish:
    image: mcr.microsoft.com/dotnet/sdk:9.0
    stage: build
    tags:
        - baremetaldind
    script:
        - mkdir -p appoutput    
        - cd voice-processing-service
        - dotnet restore
        - dotnet build --configuration release            
        - dotnet publish --configuration release --runtime linux-x64 --self-contained true -p:PublishTrimmed=false -p:PublishSingleFile=true --output ../appoutput
    artifacts:
      expire_in: 1 days
      paths:
        - appoutput
    rules:
        - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
          changes: 
            - version

## DOCEKR BUILD & PUSH
### Building docker image and pushing to local gitlab repository
build docker image and push:
    stage: deploy
    tags:
        - shell
    before_script:
        - export CURRENT_VERSION_ID="$(<version)"
        - echo ${CI_REGISTRY_PASSWORD} | docker login -u ${CI_REGISTRY_USER} ${CI_REGISTRY} --password-stdin
        - echo ${CURRENT_VERSION_ID}
    script:
        - docker build --pull -t ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID} -f voice-processing-service/Dockerfile ./voice-processing-service
        - docker push ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID}
        - docker rmi ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID}
    rules:
        - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
          changes: 
            - version