# CTS Wiring Examples

## Audio Loop (StreamWebSocketAudioLoopAsync)

```csharp
while (!cancellationToken.IsCancellationRequested)
{
    await _websocketAudio.SendAsync(
        new ArraySegment<byte>(pcmData),
        WebSocketMessageType.Binary,
        true,
        cancellationToken);
}

if (_websocketAudio.State == WebSocketState.Open)
{
    await _websocketAudio.CloseAsync(
        WebSocketCloseStatus.NormalClosure,
        "Closing audio stream",
        cancellationToken);
}
```

## Results Loop (ProcessWebSocketMessagesAsync)

```csharp
while (_websocket.State == WebSocketState.Open
       && !cancellationToken.IsCancellationRequested)
{
    var result = await _websocket.ReceiveAsync(
        new ArraySegment<byte>(buffer),
        cancellationToken);
    // Process text or close frames...
}

await _websocket.CloseAsync(
    WebSocketCloseStatus.NormalClosure,
    "Closing results stream",
    cancellationToken);
```

## Connect Calls

```csharp
// Use audio CTS for sending
await _websocketAudio.ConnectAsync(uri, audioCts.Token);

// Use results CTS for receiving
await _websocket.ConnectAsync(uri, resultsCts.Token);