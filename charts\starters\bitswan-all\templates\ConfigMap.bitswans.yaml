{{- range $bitswan, $values := .Values.sites }}
{{- range $instance, $data := $values }}
{{- if ne $data.environment "production" }}
{{- $bitsafe := cat $bitswan "-" $instance | replace "_" "" | nospace }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $.Release.Name }}-{{ $bitsafe }}
data:
  BITSWANS_URI: "cem.git.cz.o2/swan-lake/bitswans.git"
  BITSWANS_CHECKOUT_USERNAME: "bitswans-dt"
  BITSWANS_WORKDIR: "/opt/bspumpo2"
  checkout.sh: |
    #!/bin/sh
    echo "Checking-out the bitswans repository of '${BITSWANS_URI}'"
    git -c http.sslVerify=false clone --branch {{ $data.environment }} --depth=1 --verbose --progress "https://${BITSWANS_CHECKOUT_USERNAME}:${BITSWANS_CHECKOUT_PASSWORD}@${BITSWANS_URI}" /opt/temp
    cp -R /opt/temp/bspumpo2/* /opt/bspumpo2/
    echo "Done"
{{- end }}
{{- end }}
{{- end }}