using System;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Integration.Services
{
    public class ReInviteInactivityIntegrationTests
    {
        private static byte[] BuildRtpPacket(byte payloadType = 0, int payloadLength = 160, ushort seq = 1, uint ts = 160)
        {
            var header = new byte[12];
            header[0] = 0x80;
            header[1] = (byte)(payloadType & 0x7F);
            header[2] = (byte)(seq >> 8);
            header[3] = (byte)(seq & 0xFF);
            header[4] = (byte)((ts >> 24) & 0xFF);
            header[5] = (byte)((ts >> 16) & 0xFF);
            header[6] = (byte)((ts >> 8) & 0xFF);
            header[7] = (byte)(ts & 0xFF);
            var payload = new byte[payloadLength];
            for (int i = 0; i < payload.Length; i++) payload[i] = (byte)(i & 0xFF);

            var packet = new byte[header.Length + payload.Length];
            Buffer.BlockCopy(header, 0, packet, 0, header.Length);
            Buffer.BlockCopy(payload, 0, packet, header.Length, payload.Length);
            return packet;
        }

        private static async Task SendRtpAsync(IPEndPoint endpoint, int count, int spacingMs = 0)
        {
            using var sender = new UdpClient();
            ushort seq = 1;
            uint ts = 0;
            for (int i = 0; i < count; i++)
            {
                var packet = BuildRtpPacket(0, 160, seq++, ts);
                ts += 160;
                await sender.SendAsync(packet, packet.Length, endpoint);
                if (spacingMs > 0)
                {
                    await Task.Delay(spacingMs);
                }
            }
        }

        private class ListLogger<T> : ILogger<T>
        {
            private readonly object _lock = new();
            public string AllText { get; private set; } = string.Empty;

            public IDisposable BeginScope<TState>(TState state) => NullLogger.Instance.BeginScope(state);
            public bool IsEnabled(LogLevel logLevel) => true;

            public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
            {
                lock (_lock)
                {
                    AllText += formatter(state, exception) + Environment.NewLine;
                }
            }
        }

        [Fact]
        public async Task ReInvitePauseShorterThanTimeout_KeepsMediaAlive()
        {
            // Arrange: DI with InactivityTimeoutMs = 30000
            var config = new ConfigurationBuilder()
                .AddInMemoryCollection(new[]
                {
                    new KeyValuePair<string, string>("RtpReceiver:InactivityTimeoutMs", "30000")
                }).Build();

            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(config);
            services.Configure<RtpReceiverOptions>(config.GetSection("RtpReceiver"));
            services.AddLogging();
            services.AddSingleton<ILoggerFactory>(_ => NullLoggerFactory.Instance);

            // Use real buffer and receiver
            var rtpOptions = Options.Create(new RtpReceiverOptions { InactivityTimeoutMs = 30000 });
            var rtpClient = new UdpClient(new IPEndPoint(IPAddress.Loopback, 0));
            var rtcpClient = new UdpClient(new IPEndPoint(IPAddress.Loopback, 0));
            var logger = NullLoggerFactory.Instance.CreateLogger<RtpAudioReceiver>();
            using var receiver = new RtpAudioReceiver("IT-CALL-A", rtpClient, rtcpClient, rtpOptions, logger);
            using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            receiver.AudioFrameReceived += buffer.Add;

            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(15));
            var listenTask = receiver.StartListeningAsync(buffer, cts.Token);

            // Simulate RTP flow, then brief re-INVITE pause
            await SendRtpAsync(receiver.RtpLocalEndPoint, 5, 5);
            await Task.Delay(1200, CancellationToken.None); // Pause < timeout

            await SendRtpAsync(receiver.RtpLocalEndPoint, 3, 5);

            // Assert: buffer not completed, receiver still running, media alive
            Assert.False(buffer.IsAddingCompleted);
            Assert.False(listenTask.IsCompleted);

            // Cleanup
            cts.Cancel();
            await listenTask;
        }

        [Fact]
        public async Task ReInvitePauseLongerThanTimeout_TriggersCompletion()
        {
            // Arrange: DI with InactivityTimeoutMs = 1500
            var config = new ConfigurationBuilder()
                .AddInMemoryCollection(new[]
                {
                    new KeyValuePair<string, string>("RtpReceiver:InactivityTimeoutMs", "1500")
                }).Build();

            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(config);
            services.Configure<RtpReceiverOptions>(config.GetSection("RtpReceiver"));
            services.AddLogging();
            services.AddSingleton<ILoggerFactory>(_ => NullLoggerFactory.Instance);

            var captureLogger = new ListLogger<RtpAudioReceiver>();
            var rtpOptions = Options.Create(new RtpReceiverOptions { InactivityTimeoutMs = 1500 });
            var rtpClient = new UdpClient(new IPEndPoint(IPAddress.Loopback, 0));
            var rtcpClient = new UdpClient(new IPEndPoint(IPAddress.Loopback, 0));
            using var receiver = new RtpAudioReceiver("IT-CALL-B", rtpClient, rtcpClient, rtpOptions, captureLogger);
            using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            receiver.AudioFrameReceived += buffer.Add;

            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(15));
            var listenTask = receiver.StartListeningAsync(buffer, cts.Token);

            await SendRtpAsync(receiver.RtpLocalEndPoint, 3, 5);

            await Task.Delay(2000, CancellationToken.None); // Pause > timeout

            await Task.Delay(150, CancellationToken.None);

            Assert.True(buffer.IsAddingCompleted);

            await SendRtpAsync(receiver.RtpLocalEndPoint, 1);
            var gotAny = buffer.TryTake(out var _, 100, CancellationToken.None);
            Assert.False(gotAny);

            Assert.Contains("threshold 1500ms", captureLogger.AllText, StringComparison.OrdinalIgnoreCase);

            cts.Cancel();
            await listenTask;
        }
    }
}