using System;
using System.Threading;
using System.Threading.Tasks;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Interface for per-Call-ID synchronization service to prevent race conditions
    /// in session operations (INVITE, re-INVITE, BYE).
    /// </summary>
    public interface ICallSynchronizationService
    {
        /// <summary>
        /// Acquires a lock for the specified Call-ID and executes the provided function.
        /// </summary>
        /// <typeparam name="T">Return type of the function.</typeparam>
        /// <param name="callId">The Call-ID to acquire the lock for.</param>
        /// <param name="function">The function to execute while holding the lock.</param>
        /// <param name="timeout">Maximum time to wait for lock acquisition (default: 30 seconds).</param>
        /// <param name="cancellationToken">Cancellation token for the operation.</param>
        /// <returns>The result of the executed function.</returns>
        Task<T> ExecuteWithLockAsync<T>(
            string callId,
            Func<Task<T>> function,
            TimeSpan? timeout = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Acquires a lock for the specified Call-ID and executes the provided action.
        /// </summary>
        /// <param name="callId">The Call-ID to acquire the lock for.</param>
        /// <param name="action">The action to execute while holding the lock.</param>
        /// <param name="timeout">Maximum time to wait for lock acquisition (default: 30 seconds).</param>
        /// <param name="cancellationToken">Cancellation token for the operation.</param>
        Task ExecuteWithLockAsync(
            string callId,
            Func<Task> action,
            TimeSpan? timeout = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Releases all locks and cleans up resources for the specified Call-ID.
        /// This should be called when a session is terminated.
        /// </summary>
        /// <param name="callId">The Call-ID to clean up locks for.</param>
        Task CleanupLocksAsync(string callId);

        /// <summary>
        /// Gets the current number of active locks (for monitoring/debugging).
        /// </summary>
        int ActiveLockCount { get; }
    }
}