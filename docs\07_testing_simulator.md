# Implementace simulátoru pro testování

## Popis úkolu

Tento úkol zahrnuje implementaci nástroje pro simulaci SIP klienta, k<PERSON><PERSON> bude schopen vytvořit SIP spojení s SIP serverem, streamovat WAV soubor přes RTP a ukončit hovor. Simulátor bude sloužit pro E2E testování celého řešení.

## Technické detaily

### Implementace SipClientSimulator

Simulátor bude implementován jako konzolov<PERSON> aplikace, kter<PERSON> bude využívat knihovnu SIPSorcery pro SIP komunikaci.

```csharp
using System;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SIPSorcery.Media;
using SIPSorcery.Net;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;

namespace voice_processing_service.Simulator
{
    /// <summary>
    /// Simulátor SIP klienta pro testování.
    /// </summary>
    class Program
    {
        private static ILogger _logger;
        private static SIPTransport _sipTransport;
        private static SIPUserAgent _userAgent;
        private static UdpClient _rtpClient;
        private static CancellationTokenSource _cts;
        private static Task _rtpSendTask;

        static async Task Main(string[] args)
        {
            if (args.Length < 4)
            {
                Console.WriteLine("Usage: SipClientSimulator <server_ip> <server_port> <wav_file> <call_duration_seconds>");
                Console.WriteLine("Example: SipClientSimulator 127.0.0.1 5060 test.wav 30");
                return;
            }

            string serverIp = args[0];
            int serverPort = int.Parse(args[1]);
            string wavFile = args[2];
            int callDurationSeconds = int.Parse(args[3]);

            // Vytvoření loggeru
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddFilter("Microsoft", LogLevel.Warning)
                    .AddFilter("System", LogLevel.Warning)
                    .AddFilter("SIPSorcery", LogLevel.Information)
                    .AddConsole();
            });
            _logger = loggerFactory.CreateLogger<Program>();

            // Nastavení loggeru pro SIPSorcery
            SIPSorcery.LogFactory.Set(loggerFactory);

            _logger.LogInformation($"Starting SIP client simulator. Server: {serverIp}:{serverPort}, WAV file: {wavFile}, Duration: {callDurationSeconds}s");

            try
            {
                // Inicializace SIP transportu
                _sipTransport = new SIPTransport();
                var sipChannel = new SIPUDPChannel(IPAddress.Any, 0);
                _sipTransport.AddSIPChannel(sipChannel);

                // Vytvoření SIP User Agenta
                _userAgent = new SIPUserAgent(_sipTransport, null);

                // Nastavení handleru pro odpovědi na INVITE
                _userAgent.OnInviteAccepted += OnInviteAccepted;
                _userAgent.OnInviteRejected += OnInviteRejected;

                // Vytvoření CancellationTokenSource pro ukončení simulace
                _cts = new CancellationTokenSource();
                _cts.CancelAfter(TimeSpan.FromSeconds(callDurationSeconds + 10)); // Přidáme 10 sekund rezervu

                // Vytvoření INVITE požadavku
                var serverEndPoint = new SIPEndPoint(SIPProtocolsEnum.udp, new IPEndPoint(IPAddress.Parse(serverIp), serverPort));
                var inviteUri = new SIPURI("sip", "simulator", serverEndPoint.ToHost(), serverEndPoint.Port);
                
                // Odeslání INVITE požadavku
                _logger.LogInformation($"Sending INVITE to {inviteUri}");
                var callResult = await _userAgent.InitiateCallAsync(inviteUri, null, null, null, SIPCallDirection.Out);

                if (callResult.Succeeded)
                {
                    _logger.LogInformation("INVITE sent successfully.");

                    // Čekání na ukončení hovoru
                    await Task.Delay(TimeSpan.FromSeconds(callDurationSeconds), _cts.Token);

                    // Ukončení hovoru
                    _logger.LogInformation("Call duration elapsed. Hanging up.");
                    await _userAgent.Hangup();
                }
                else
                {
                    _logger.LogError($"Failed to send INVITE: {callResult.ReasonPhrase}");
                }

                // Čekání na dokončení RTP streamu
                if (_rtpSendTask != null)
                {
                    try
                    {
                        await _rtpSendTask;
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.LogInformation("RTP stream was cancelled.");
                    }
                }

                _logger.LogInformation("Simulation completed.");
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Simulation was cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SIP client simulator.");
            }
            finally
            {
                // Úklid
                _cts?.Dispose();
                _rtpClient?.Close();
                _sipTransport?.Dispose();
            }
        }

        /// <summary>
        /// Handler pro přijetí INVITE požadavku.
        /// </summary>
        /// <param name="ua">SIP User Agent.</param>
        /// <param name="dialogId">ID dialogu.</param>
        /// <param name="remoteUri">Vzdálené URI.</param>
        /// <param name="sdp">SDP zpráva.</param>
        private static void OnInviteAccepted(SIPUserAgent ua, string dialogId, string remoteUri, SIPSorcery.SDP.SDPMessage sdp)
        {
            _logger.LogInformation($"INVITE accepted by {remoteUri}. Dialog ID: {dialogId}");

            try
            {
                // Získání RTP endpointu ze SDP
                if (sdp?.RtpEndPoint != null)
                {
                    var rtpEndPoint = sdp.RtpEndPoint;
                    _logger.LogInformation($"Remote RTP endpoint: {rtpEndPoint}");

                    // Vytvoření UDP klienta pro odesílání RTP
                    _rtpClient = new UdpClient();

                    // Spuštění úlohy pro odesílání RTP
                    _rtpSendTask = SendRtpStreamAsync(rtpEndPoint, args[2], _cts.Token);
                }
                else
                {
                    _logger.LogWarning("No RTP endpoint in SDP. Cannot send RTP stream.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OnInviteAccepted handler.");
            }
        }

        /// <summary>
        /// Handler pro odmítnutí INVITE požadavku.
        /// </summary>
        /// <param name="ua">SIP User Agent.</param>
        /// <param name="statusCode">Stavový kód.</param>
        /// <param name="reasonPhrase">Důvod odmítnutí.</param>
        private static void OnInviteRejected(SIPUserAgent ua, SIPResponseStatusCodesEnum statusCode, string reasonPhrase)
        {
            _logger.LogWarning($"INVITE rejected: {statusCode} {reasonPhrase}");
            _cts.Cancel();
        }

        /// <summary>
        /// Odesílá RTP stream z WAV souboru.
        /// </summary>
        /// <param name="rtpEndPoint">RTP endpoint.</param>
        /// <param name="wavFile">Cesta k WAV souboru.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private static async Task SendRtpStreamAsync(IPEndPoint rtpEndPoint, string wavFile, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"Starting RTP stream from {wavFile} to {rtpEndPoint}");

            try
            {
                // Načtení WAV souboru
                byte[] wavData = await File.ReadAllBytesAsync(wavFile, cancellationToken);
                
                // Přeskočení WAV hlavičky (44 bytů)
                int offset = 44;
                
                // Parametry RTP
                ushort sequenceNumber = 0;
                uint timestamp = 0;
                uint ssrc = (uint)new Random().Next();
                
                // Velikost RTP paketu (20ms G.711 při 8kHz = 160 vzorků = 160 bytů)
                const int payloadSize = 160;
                
                _logger.LogInformation($"WAV file loaded. Size: {wavData.Length} bytes. Starting RTP stream.");

                while (offset < wavData.Length && !cancellationToken.IsCancellationRequested)
                {
                    // Vytvoření RTP hlavičky (12 bytů)
                    byte[] rtpHeader = new byte[12];
                    rtpHeader[0] = 0x80; // Version 2, no padding, no extension, no CSRC
                    rtpHeader[1] = 0x00; // Payload type 0 = PCMU/G.711u
                    rtpHeader[2] = (byte)((sequenceNumber >> 8) & 0xFF); // Sequence number (high byte)
                    rtpHeader[3] = (byte)(sequenceNumber & 0xFF); // Sequence number (low byte)
                    rtpHeader[4] = (byte)((timestamp >> 24) & 0xFF); // Timestamp (highest byte)
                    rtpHeader[5] = (byte)((timestamp >> 16) & 0xFF); // Timestamp
                    rtpHeader[6] = (byte)((timestamp >> 8) & 0xFF); // Timestamp
                    rtpHeader[7] = (byte)(timestamp & 0xFF); // Timestamp (lowest byte)
                    rtpHeader[8] = (byte)((ssrc >> 24) & 0xFF); // SSRC (highest byte)
                    rtpHeader[9] = (byte)((ssrc >> 16) & 0xFF); // SSRC
                    rtpHeader[10] = (byte)((ssrc >> 8) & 0xFF); // SSRC
                    rtpHeader[11] = (byte)(ssrc & 0xFF); // SSRC (lowest byte)
                    
                    // Vytvoření RTP paketu (hlavička + payload)
                    int bytesToSend = Math.Min(payloadSize, wavData.Length - offset);
                    byte[] rtpPacket = new byte[rtpHeader.Length + bytesToSend];
                    Buffer.BlockCopy(rtpHeader, 0, rtpPacket, 0, rtpHeader.Length);
                    Buffer.BlockCopy(wavData, offset, rtpPacket, rtpHeader.Length, bytesToSend);
                    
                    // Odeslání RTP paketu
                    await _rtpClient.SendAsync(rtpPacket, rtpPacket.Length, rtpEndPoint);
                    
                    // Aktualizace parametrů
                    sequenceNumber++;
                    timestamp += (uint)bytesToSend; // Pro G.711 je timestamp = počet vzorků = počet bytů
                    offset += bytesToSend;
                    
                    // Pauza 20ms (simulace real-time streamu)
                    await Task.Delay(20, cancellationToken);
                }

                _logger.LogInformation("RTP stream completed.");
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("RTP stream was cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending RTP stream.");
            }
        }
    }
}
```

### Implementace WavFileGenerator

Pro testování bude také implementován nástroj pro generování testovacích WAV souborů s různými zvuky.

```csharp
using System;
using System.IO;
using System.Threading.Tasks;

namespace voice_processing_service.Simulator
{
    /// <summary>
    /// Nástroj pro generování testovacích WAV souborů.
    /// </summary>
    class WavFileGenerator
    {
        static async Task Main(string[] args)
        {
            if (args.Length < 3)
            {
                Console.WriteLine("Usage: WavFileGenerator <output_file> <duration_seconds> <tone_frequency>");
                Console.WriteLine("Example: WavFileGenerator test.wav 10 440");
                return;
            }

            string outputFile = args[0];
            int durationSeconds = int.Parse(args[1]);
            int toneFrequency = int.Parse(args[2]);

            Console.WriteLine($"Generating WAV file: {outputFile}, Duration: {durationSeconds}s, Tone: {toneFrequency}Hz");

            try
            {
                // Parametry WAV souboru
                const int sampleRate = 8000; // 8kHz
                const int bitsPerSample = 8; // 8 bitů (G.711 µ-law)
                const int channels = 1; // Mono

                // Výpočet velikosti dat
                int dataSize = sampleRate * durationSeconds * channels * bitsPerSample / 8;

                // Vytvoření WAV hlavičky
                using var fileStream = new FileStream(outputFile, FileMode.Create);
                using var writer = new BinaryWriter(fileStream);

                // RIFF hlavička
                writer.Write(new char[] { 'R', 'I', 'F', 'F' });
                writer.Write(36 + dataSize); // Velikost souboru - 8
                writer.Write(new char[] { 'W', 'A', 'V', 'E' });

                // fmt chunk
                writer.Write(new char[] { 'f', 'm', 't', ' ' });
                writer.Write(16); // Velikost fmt chunku
                writer.Write((short)7); // Format tag (7 = µ-law)
                writer.Write((short)channels); // Počet kanálů
                writer.Write(sampleRate); // Vzorkovací frekvence
                writer.Write(sampleRate * channels * bitsPerSample / 8); // Bytes per second
                writer.Write((short)(channels * bitsPerSample / 8)); // Block align
                writer.Write((short)bitsPerSample); // Bits per sample

                // data chunk
                writer.Write(new char[] { 'd', 'a', 't', 'a' });
                writer.Write(dataSize); // Velikost dat

                // Generování dat (sinus s danou frekvencí)
                double amplitude = 32767.0; // Maximální amplituda pro 16-bit PCM
                double angularFrequency = 2.0 * Math.PI * toneFrequency / sampleRate;

                for (int i = 0; i < sampleRate * durationSeconds; i++)
                {
                    // Generování sinusového signálu
                    double sample = amplitude * Math.Sin(angularFrequency * i);
                    
                    // Konverze na 16-bit PCM
                    short pcmSample = (short)sample;
                    
                    // Konverze na µ-law (zjednodušená implementace)
                    byte muLawSample = LinearToMuLaw(pcmSample);
                    
                    // Zápis vzorku
                    writer.Write(muLawSample);
                }

                Console.WriteLine($"WAV file generated successfully: {outputFile}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating WAV file: {ex.Message}");
            }
        }

        /// <summary>
        /// Konvertuje lineární 16-bit PCM vzorek na 8-bit µ-law vzorek.
        /// </summary>
        /// <param name="pcmSample">16-bit PCM vzorek.</param>
        /// <returns>8-bit µ-law vzorek.</returns>
        private static byte LinearToMuLaw(short pcmSample)
        {
            // Zjednodušená implementace konverze z lineárního PCM na µ-law
            // V reálné implementaci by bylo potřeba použít složitější algoritmus
            
            const int MULAW_BIAS = 33;
            const int MULAW_MAX = 0x7FFF;
            
            // Omezení vstupního rozsahu
            int sample = Math.Max(-MULAW_MAX, Math.Min(MULAW_MAX, pcmSample));
            
            // Získání znaménka a absolutní hodnoty
            int sign = (sample < 0) ? 0x80 : 0x00;
            if (sample < 0) sample = -sample;
            
            // Přidání biasu
            sample += MULAW_BIAS;
            
            // Komprese
            byte compressedByte;
            if (sample < 0x100)
                compressedByte = (byte)(7 << 4 | (sample >> 4));
            else if (sample < 0x200)
                compressedByte = (byte)(6 << 4 | (sample >> 5));
            else if (sample < 0x400)
                compressedByte = (byte)(5 << 4 | (sample >> 6));
            else if (sample < 0x800)
                compressedByte = (byte)(4 << 4 | (sample >> 7));
            else if (sample < 0x1000)
                compressedByte = (byte)(3 << 4 | (sample >> 8));
            else if (sample < 0x2000)
                compressedByte = (byte)(2 << 4 | (sample >> 9));
            else if (sample < 0x4000)
                compressedByte = (byte)(1 << 4 | (sample >> 10));
            else
                compressedByte = (byte)(0 << 4 | (sample >> 11));
            
            // Přidání znaménka a inverze (µ-law má invertované bity)
            return (byte)(~(sign | compressedByte));
        }
    }
}
```

## Testovací scénáře

### Unit testy pro SipClientSimulator

1. **Test odesílání INVITE požadavku**
   - Ověřit, že simulátor správně vytvoří a odešle INVITE požadavek
   - Ověřit, že simulátor správně zpracuje odpověď na INVITE požadavek

2. **Test odesílání RTP streamu**
   - Ověřit, že simulátor správně vytvoří a odešle RTP pakety
   - Ověřit, že simulátor správně načte WAV soubor a extrahuje audio data

3. **Test ukončení hovoru**
   - Ověřit, že simulátor správně ukončí hovor po uplynutí zadané doby
   - Ověřit, že simulátor správně ukončí RTP stream po ukončení hovoru

### Unit testy pro WavFileGenerator

1. **Test generování WAV souboru**
   - Ověřit, že generátor správně vytvoří WAV soubor s požadovanými parametry
   - Ověřit, že generátor správně vygeneruje sinusový signál s požadovanou frekvencí
   - Ověřit, že generátor správně konvertuje lineární PCM na µ-law

### E2E testy pro celé řešení

1. **Test příchozího hovoru**
   - Spustit SIP server
   - Spustit simulátor SIP klienta
   - Ověřit, že hovor byl úspěšně vytvořen a přijat
   - Ověřit, že audio data byla úspěšně přenesena a zpracována
   - Ověřit, že hovor byl úspěšně ukončen

2. **Test více souběžných hovorů**
   - Spustit SIP server
   - Spustit několik instancí simulátoru SIP klienta
   - Ověřit, že všechny hovory byly úspěšně vytvořeny a přijaty
   - Ověřit, že audio data byla úspěšně přenesena a zpracována pro všechny hovory
   - Ověřit, že všechny hovory byly úspěšně ukončeny

3. **Test ukončení hovoru ze strany klienta**
   - Spustit SIP server
   - Spustit simulátor SIP klienta s krátkou dobou trvání hovoru
   - Ověřit, že hovor byl úspěšně vytvořen a přijat
   - Ověřit, že hovor byl úspěšně ukončen ze strany klienta
   - Ověřit, že server správně zpracoval ukončení hovoru

4. **Test ukončení hovoru ze strany serveru**
   - Spustit SIP server
   - Spustit simulátor SIP klienta
   - Ověřit, že hovor byl úspěšně vytvořen a přijat
   - Ukončit hovor pomocí API serveru
   - Ověřit, že hovor byl úspěšně ukončen ze strany serveru
   - Ověřit, že klient správně zpracoval ukončení hovoru

## Implementační kroky

1. Implementovat třídu SipClientSimulator
2. Implementovat třídu WavFileGenerator
3. Implementovat unit testy pro SipClientSimulator
4. Implementovat unit testy pro WavFileGenerator
5. Implementovat E2E testy pro celé řešení
6. Vytvořit dokumentaci pro použití simulátoru

## Použití simulátoru

Simulátor lze použít pro testování SIP serveru následujícím způsobem:

1. Vygenerování testovacího WAV souboru:
   ```
   WavFileGenerator test.wav 10 440
   ```

2. Spuštění simulátoru SIP klienta:
   ```
   SipClientSimulator 127.0.0.1 5060 test.wav 30
   ```

3. Sledování logů SIP serveru a simulátoru pro ověření správné funkčnosti.

Simulátor lze také použít pro zátěžové testování SIP serveru spuštěním více instancí simulátoru současně.
