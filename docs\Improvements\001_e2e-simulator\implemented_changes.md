# Implementované změny pro umožnění běhu serveru a simulátoru na jedné stanici

## Úpravy v simulátoru (voice-processing-simulator)

- [x] **Úprava Program.cs - zpracování parametrů příkazové řádky**
  - [x] Rozšíření příkazu "simulate" o parametry pro konfiguraci SIP a RTP portů
  - [x] Přidání výchozích hodnot pro nové parametry
  - [x] Aktualizace nápovědy a příkladů použití

- [x] **Úprava Program.cs - metoda SimulateCallAsync**
  - [x] Úprava signatury metody pro přidání nových parametrů
  - [x] Úprava vytváření SIP kanálu pro použití konfigurovatelného portu
  - [x] Úprava vytváření VoIPMediaSession pro použití konfigurovatelného rozsahu portů

- [x] **Úprava Program.cs - metoda ShowUsage**
  - [x] Aktualizace nápovědy pro zobrazení nových parametrů
  - [x] Přidání příkladů použití s novými parametry

- [x] **Úprava Properties/launchSettings.json**
  - [x] Přidání profilu s vlastními porty pomocí parametrů příkazové řádky

## Úpravy v serveru (voice-processing-service)

- [x] **Úprava RtpAudioReceiverFactory.cs - metoda CreateReceiverWithSpecificPorts**
  - [x] Odstranění pevně nastavených portů (40002 a 40003)
  - [x] Úprava vytváření UDP klientů pro použití parametrů rtpPort a rtcpPort
  - [x] Zajištění konzistence v nastavení IP adresy (použití parametru localAddress)
  - [x] Přidání lepšího logování pro diagnostiku problémů s porty

- [x] **Úprava SipServerService.cs - zajištění konzistence RTP portů**
  - [x] Extrakce RTP portu z SDP odpovědi
  - [x] Použití stejného portu pro vytvoření RtpAudioReceiver
  - [x] Přidání logování pro ověření, že porty v SDP odpovídají portům v RtpAudioReceiver

- [x] **Úprava Properties/launchSettings.json**
  - [x] Přidání profilu s vlastními porty pomocí proměnných prostředí
  - [x] Nastavení proměnných prostředí pro konfiguraci SIP a RTP portů

## Testování

- [ ] **Testování serveru s výchozími porty**
  - [ ] Spuštění serveru bez parametrů
  - [ ] Ověření, že server naslouchá na správných portech

- [ ] **Testování serveru s vlastními porty**
  - [ ] Spuštění serveru s proměnnými prostředí pro konfiguraci portů
  - [ ] Ověření, že server naslouchá na správných portech

- [ ] **Testování simulátoru s výchozími porty**
  - [ ] Spuštění simulátoru s minimálními parametry
  - [ ] Ověření, že simulátor naslouchá na správných portech

- [ ] **Testování simulátoru s vlastními porty**
  - [ ] Spuštění simulátoru s parametry pro konfiguraci portů
  - [ ] Ověření, že simulátor naslouchá na správných portech

- [ ] **Testování serveru a simulátoru na jedné stanici**
  - [ ] Spuštění serveru a simulátoru na jedné stanici s různými porty
  - [ ] Ověření, že simulátor se úspěšně připojí k serveru
  - [ ] Ověření, že RTP stream je správně přenášen
  - [ ] Ověření, že hovor je správně ukončen

## Shrnutí implementovaných změn

### 1. Úprava RtpAudioReceiverFactory.cs

Hlavní problém byl v metodě `CreateReceiverWithSpecificPorts`, která obsahovala pevně nastavené porty (40002 a 40003) místo použití parametrů `rtpPort` a `rtcpPort`. Tato metoda byla upravena tak, aby správně používala parametry pro vytvoření UDP klientů:

```csharp
public IAudioInputReceiver CreateReceiverWithSpecificPorts(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
{
    try
    {
        _logger.LogInformation($"[{callId}] Attempting to create RTP receiver with specific ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit UDP klienty s požadovanými porty
        var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
        var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));

        // Nastavení většího bufferu pro UDP klienty
        rtpClient.Client.ReceiveBufferSize = 1048576; // 1MB buffer
        rtpClient.Client.SendBufferSize = 1048576;
        rtcpClient.Client.ReceiveBufferSize = 1048576;
        rtcpClient.Client.SendBufferSize = 1048576;

        _logger.LogInformation($"[{callId}] Successfully created RTP receiver with ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit a vrátit RtpAudioReceiver
        return new RtpAudioReceiver(callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());
    }
    // ...
}
```

### 2. Úprava SipServerService.cs

V metodě `ProcessInviteAsync` byla upravena část, která extrahuje porty z SDP odpovědi a používá je pro vytvoření RtpAudioReceiver:

```csharp
// Vytvoření SDP odpovědi pomocí VoIPMediaSession
var answerSdp = _rtpSession.CreateAnswer(offerSdp);

_logger.LogInformation($"[{callId}] Created SDP answer");

// Log SDP details for debugging
_logger.LogDebug($"[{callId}] SDP answer: {answerSdp}");

// Získáme port z SDP odpovědi
int rtpPort = 0;
int rtcpPort = 0;

// Získáme port z SDP odpovědi
foreach (var media in answerSdp.Media)
{
    if (media.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase))
    {
        rtpPort = media.Port;
        rtcpPort = rtpPort + 1; // RTCP port je typicky RTP port + 1
        _logger.LogInformation($"[{callId}] Using RTP port {rtpPort} and RTCP port {rtcpPort} from SDP");
        break;
    }
}

// Ověříme, že jsme získali platné porty
if (rtpPort <= 0 || rtcpPort <= 0)
{
    _logger.LogError($"[{callId}] Failed to extract valid RTP/RTCP ports from SDP answer");
    throw new InvalidOperationException("Failed to extract valid RTP/RTCP ports from SDP answer");
}
```

### 3. Úprava simulátoru

Simulátor byl upraven tak, aby přijímal parametry příkazové řádky pro konfiguraci SIP a RTP portů:

```csharp
case "simulate":
    if (args.Length < 4)
    {
        Console.WriteLine("Usage: voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds] [local_sip_port] [rtp_port_min] [rtp_port_max]");
        Console.WriteLine("Example: voice-processing-simulator simulate test.wav 127.0.0.1 5060 30 5070 25000 25010");
        return;
    }
    string wavFile = args[1];
    string serverIp = args[2];
    int serverPort = int.Parse(args[3]);
    int callDurationSeconds = args.Length > 4 ? int.Parse(args[4]) : 30; // Výchozí hodnota 30 sekund
    int localSipPort = args.Length > 5 ? int.Parse(args[5]) : 5070; // Výchozí hodnota 5070
    int rtpPortMin = args.Length > 6 ? int.Parse(args[6]) : 25000; // Výchozí hodnota 25000
    int rtpPortMax = args.Length > 7 ? int.Parse(args[7]) : 25010; // Výchozí hodnota 25010
    await SimulateCallAsync(wavFile, serverIp, serverPort, callDurationSeconds, localSipPort, rtpPortMin, rtpPortMax);
    break;
```

Metoda `SimulateCallAsync` byla upravena tak, aby používala tyto parametry:

```csharp
private static async Task SimulateCallAsync(string wavFile, string serverIp, int serverPort, int callDurationSeconds, int localSipPort = 5070, int rtpPortMin = 25000, int rtpPortMax = 25010)
{
    // ...
    
    // Přidání UDP kanálu pro SIP komunikaci s konfigurovatelným portem
    var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, localSipPort);
    sipTransport.AddSIPChannel(sipChannel);
    _logger.LogInformation($"SIP UDP channel created on {sipChannel.ListeningEndPoint}");
    
    // Vytvoření VoIP media session s konfigurovatelným rozsahem portů
    _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
    {
        MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
        RtpPortRange = new PortRange(rtpPortMin, rtpPortMax),
    });
    
    // ...
}
```

### 4. Úprava launchSettings.json

V obou projektech byly přidány profily s vlastními porty:

**voice-processing-service/Properties/launchSettings.json**:
```json
"custom_ports": {
  "commandName": "Project",
  "dotnetRunMessages": true,
  "launchBrowser": false,
  "applicationUrl": "http://localhost:5045",
  "environmentVariables": {
    "ASPNETCORE_ENVIRONMENT": "Development",
    "SIPSERVER__LISTENPORT": "5061",
    "SIPSERVER__RTPPORTMIN": "31000",
    "SIPSERVER__RTPPORTMAX": "31010"
  }
}
```

**voice-processing-simulator/Properties/launchSettings.json**:
```json
"voice-processing-simulator-custom-ports": {
  "commandName": "Project",
  "commandLineArgs": "simulate sage.wav 127.0.0.1 5061 20 5071 26000 26010"
}
```

## Další kroky

1. Provést testování implementovaných změn
2. Aktualizovat dokumentaci
3. Vytvořit pull request s implementovanými změnami
