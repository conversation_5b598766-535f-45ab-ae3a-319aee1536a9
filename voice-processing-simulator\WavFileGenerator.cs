using System;
using System.IO;

namespace voice_processing_simulator
{
    /// <summary>
    /// Nástroj pro generování testovacích WAV souborů.
    /// </summary>
    public class WavFileGenerator
    {
        /// <summary>
        /// Generuje WAV soubor s tónem o zadané frekvenci.
        /// </summary>
        /// <param name="outputFile">Cesta k výstupnímu souboru.</param>
        /// <param name="durationSeconds">Délka souboru v sekundách.</param>
        /// <param name="toneFrequency">Frekvence tónu v Hz.</param>
        public static void GenerateWavFile(string outputFile, int durationSeconds, int toneFrequency)
        {
            Console.WriteLine($"Generating WAV file: {outputFile}, Duration: {durationSeconds}s, Tone: {toneFrequency}Hz");

            try
            {
                // Parametry WAV souboru
                int sampleRate = 8000; // 8kHz
                int bitsPerSample = 8; // 8-bit (µ-law)
                int channels = 1; // Mono

                using var fileStream = new FileStream(outputFile, FileMode.Create);
                using var writer = new BinaryWriter(fileStream);

                // Zápis WAV hlavičky
                // RIFF header
                writer.Write(new char[] { 'R', 'I', 'F', 'F' });
                writer.Write(36 + sampleRate * durationSeconds * channels * bitsPerSample / 8); // Velikost souboru - 8
                writer.Write(new char[] { 'W', 'A', 'V', 'E' });

                // Format chunk
                writer.Write(new char[] { 'f', 'm', 't', ' ' });
                writer.Write(16); // Velikost format chunk
                writer.Write((short)7); // Audio format (7 = µ-law)
                writer.Write((short)channels); // Počet kanálů
                writer.Write(sampleRate); // Vzorkovací frekvence
                writer.Write(sampleRate * channels * bitsPerSample / 8); // Byte rate
                writer.Write((short)(channels * bitsPerSample / 8)); // Block align
                writer.Write((short)bitsPerSample); // Bity na vzorek

                // Data chunk
                writer.Write(new char[] { 'd', 'a', 't', 'a' });
                writer.Write(sampleRate * durationSeconds * channels * bitsPerSample / 8); // Velikost dat

                // Generování audio dat
                double amplitude = 32767.0; // Maximální amplituda pro 16-bit PCM
                double angularFrequency = 2.0 * Math.PI * toneFrequency / sampleRate;

                for (int i = 0; i < sampleRate * durationSeconds; i++)
                {
                    // Generování sinusového signálu
                    double sample = amplitude * Math.Sin(angularFrequency * i);
                    
                    // Konverze na 16-bit PCM
                    short pcmSample = (short)sample;
                    
                    // Konverze na µ-law (zjednodušená implementace)
                    byte muLawSample = LinearToMuLaw(pcmSample);
                    
                    // Zápis vzorku
                    writer.Write(muLawSample);
                }

                Console.WriteLine($"WAV file generated successfully: {outputFile}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating WAV file: {ex.Message}");
            }
        }

        /// <summary>
        /// Konvertuje lineární 16-bit PCM vzorek na 8-bit µ-law vzorek.
        /// </summary>
        /// <param name="pcmSample">16-bit PCM vzorek.</param>
        /// <returns>8-bit µ-law vzorek.</returns>
        private static byte LinearToMuLaw(short pcmSample)
        {
            // Zjednodušená implementace µ-law kódování
            const int MULAW_BIAS = 33;
            int sign = (pcmSample >> 8) & 0x80;
            if (sign != 0)
                pcmSample = (short)-pcmSample;
            if (pcmSample > 32767)
                pcmSample = 32767;
            pcmSample = (short)(pcmSample + MULAW_BIAS);
            
            int exponent = 7;
            for (int i = 0; i < 8; i++)
            {
                if (pcmSample < (1 << i))
                {
                    exponent = i;
                    break;
                }
            }
            
            int mantissa = (pcmSample >> (exponent + 3)) & 0x0F;
            byte muLawSample = (byte)(~(sign | (exponent << 4) | mantissa));
            
            return muLawSample;
        }
    }
}
