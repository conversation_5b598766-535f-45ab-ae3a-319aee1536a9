# Rozšíření existující struktury projektu

## Popis úkolu

Tento úkol zahrnuje rozšíření existující struktury projektu v adres<PERSON><PERSON>i `voice-processing-service` o nové ad<PERSON>, definici rozhraní a základ<PERSON><PERSON><PERSON> t<PERSON>, a rozšíření DI kontejneru. Cílem je vytvořit solidní základ pro další implementaci.

## Technické detaily

### Rozšíření adresářové struktury

Existující projekt `voice-processing-service` bude rozšířen o následující adres<PERSON>ře:

```
voice-processing-service/
├── Interfaces/         # Rozhraní pro jednotlivé komponenty
├── Services/           # Implementace služeb
├── Configuration/      # Konfigurační třídy
├── Controllers/        # API kontrolery (již může existovat)
├── Program.cs          # Vstupní bod aplikace (již existuje)
└── appsettings.json    # Konfigurační soubor (již existuje)
```

### Definice rozhraní

#### Interfaces/IAudioBuffer.cs

```csharp
using System;
using System.Threading;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro buffer audio dat, který slouží jako prostředník mezi přijímačem a procesorem audio dat.
    /// </summary>
    public interface IAudioBuffer : IDisposable
    {
        /// <summary>
        /// Přidá audio data do bufferu.
        /// </summary>
        /// <param name="audioData">Audio data k přidání.</param>
        void Add(byte[] audioData);

        /// <summary>
        /// Pokusí se získat audio data z bufferu.
        /// </summary>
        /// <param name="audioData">Výstupní audio data, pokud jsou k dispozici.</param>
        /// <param name="millisecondsTimeout">Timeout v milisekundách.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>True, pokud byla data získána, jinak false.</returns>
        bool TryTake(out byte[] audioData, int millisecondsTimeout, CancellationToken cancellationToken);

        /// <summary>
        /// Signalizuje, že už žádná data nebudou přidána do bufferu.
        /// </summary>
        void CompleteAdding();

        /// <summary>
        /// Indikuje, zda bylo voláno CompleteAdding.
        /// </summary>
        bool IsAddingCompleted { get; }

        /// <summary>
        /// Indikuje, zda je buffer prázdný a bylo voláno CompleteAdding.
        /// </summary>
        bool IsCompleted { get; }
    }
}
```

#### Interfaces/IAudioInputReceiver.cs

```csharp
using System;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro přijímač audio dat (např. RTP).
    /// </summary>
    public interface IAudioInputReceiver : IDisposable
    {
        /// <summary>
        /// Spustí naslouchání na síťových portech a předávání audio dat do bufferu.
        /// </summary>
        /// <param name="buffer">Buffer pro ukládání přijatých audio dat.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken);

        /// <summary>
        /// Lokální endpoint pro RTP data.
        /// </summary>
        IPEndPoint RtpLocalEndPoint { get; }

        /// <summary>
        /// Lokální endpoint pro RTCP data.
        /// </summary>
        IPEndPoint RtcpLocalEndPoint { get; }
    }
}
```

#### Interfaces/IAudioProcessor.cs

```csharp
using System;
using System.Threading;
using System.Threading.Tasks;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro procesor audio dat (např. WAV, STT).
    /// </summary>
    public interface IAudioProcessor : IDisposable
    {
        /// <summary>
        /// Spustí zpracování audio dat z bufferu.
        /// </summary>
        /// <param name="buffer">Buffer obsahující audio data ke zpracování.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken);

        /// <summary>
        /// Identifikátor procesoru pro logování a rozlišení.
        /// </summary>
        string ProcessorId { get; }
    }
}
```

#### Interfaces/ICallSession.cs

```csharp
using System;
using System.Threading;
using System.Threading.Tasks;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro session jednoho hovoru.
    /// </summary>
    public interface ICallSession : IDisposable
    {
        /// <summary>
        /// Identifikátor hovoru (Call-ID).
        /// </summary>
        string CallId { get; }

        /// <summary>
        /// SIP User Agent pro tento hovor.
        /// </summary>
        SIPUserAgent UserAgent { get; }

        /// <summary>
        /// Původní INVITE požadavek, který zahájil hovor.
        /// </summary>
        SIPRequest InitialInviteRequest { get; }

        /// <summary>
        /// Čas zahájení hovoru.
        /// </summary>
        DateTime StartTime { get; }

        /// <summary>
        /// Spustí session (zahájí příjem a zpracování audio dat).
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task StartAsync(CancellationToken cancellationToken);

        /// <summary>
        /// Zastaví session (ukončí příjem a zpracování audio dat).
        /// </summary>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task StopAsync();
    }
}
```

#### Interfaces/ICallSessionManager.cs

```csharp
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro správce sessions hovorů.
    /// </summary>
    public interface ICallSessionManager
    {
        /// <summary>
        /// Vytvoří novou session pro hovor.
        /// </summary>
        /// <param name="userAgent">SIP User Agent pro tento hovor.</param>
        /// <param name="inviteRequest">INVITE požadavek, který zahájil hovor.</param>
        /// <param name="inputReceiverFactory">Tovární metoda pro vytvoření přijímače audio dat.</param>
        /// <param name="audioProcessorFactory">Tovární metoda pro vytvoření procesoru audio dat.</param>
        /// <returns>Task reprezentující asynchronní operaci, která vrací vytvořenou session.</returns>
        Task<ICallSession> CreateSessionAsync(
            SIPUserAgent userAgent,
            SIPRequest inviteRequest,
            Func<IAudioInputReceiver> inputReceiverFactory,
            Func<IAudioProcessor> audioProcessorFactory);

        /// <summary>
        /// Získá session podle identifikátoru hovoru.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <returns>Session hovoru, nebo null, pokud session neexistuje.</returns>
        ICallSession GetSession(string callId);

        /// <summary>
        /// Ukončí session hovoru.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task TerminateSessionAsync(string callId);

        /// <summary>
        /// Získá všechny aktivní session hovorů.
        /// </summary>
        /// <returns>Kolekce aktivních session hovorů.</returns>
        IEnumerable<ICallSession> GetAllSessions();
    }
}
```

### Konfigurační třídy

#### Configuration/SipServerOptions.cs

```csharp
namespace voice_processing_service.Configuration
{
    /// <summary>
    /// Konfigurační třída pro SIP server.
    /// </summary>
    public class SipServerOptions
    {
        /// <summary>
        /// IP adresa, na které bude SIP server naslouchat. "Any" pro všechny dostupné adresy.
        /// </summary>
        public string ListenIpAddress { get; set; } = "Any";

        /// <summary>
        /// Port, na kterém bude SIP server naslouchat.
        /// </summary>
        public int ListenPort { get; set; } = 5060;

        /// <summary>
        /// Minimální port pro RTP komunikaci.
        /// </summary>
        public int RtpPortMin { get; set; } = 10000;

        /// <summary>
        /// Maximální port pro RTP komunikaci.
        /// </summary>
        public int RtpPortMax { get; set; } = 19998;

        /// <summary>
        /// Adresář pro ukládání WAV nahrávek.
        /// </summary>
        public string WavRecordingDirectory { get; set; } = "RecordedCalls";
    }
}
```

### Rozšíření Program.cs

Program.cs bude rozšířen o registraci služeb a konfiguraci:

```csharp
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Konfigurace
builder.Services.Configure<SipServerOptions>(
    builder.Configuration.GetSection("SipServer"));

// Registrace služeb
builder.Services.AddSingleton<ICallSessionManager, CallSessionManager>();
builder.Services.AddHostedService<SipServerService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

// Původní kód pro weatherforecast endpoint může být zachován pro testování
// nebo odstraněn, pokud není potřeba

app.Run();
```

### Rozšíření appsettings.json

appsettings.json bude rozšířen o konfiguraci SIP serveru:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "SIPSorcery": "Information"
    }
  },
  "AllowedHosts": "*",
  "SipServer": {
    "ListenIpAddress": "Any",
    "ListenPort": 5060,
    "RtpPortMin": 10000,
    "RtpPortMax": 19998,
    "WavRecordingDirectory": "RecordedCalls"
  }
}
```

### Přidání NuGet balíčků

Do projektu budou přidány následující NuGet balíčky:

```xml
<ItemGroup>
  <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.2" />
  <PackageReference Include="SIPSorcery" Version="6.2.0" />
</ItemGroup>
```

## Testovací scénáře

### Unit testy pro rozhraní

1. **Test IAudioBuffer**
   - Ověřit, že rozhraní IAudioBuffer je správně definováno
   - Ověřit, že metody Add, TryTake, CompleteAdding jsou správně deklarovány

2. **Test IAudioInputReceiver**
   - Ověřit, že rozhraní IAudioInputReceiver je správně definováno
   - Ověřit, že metoda StartListeningAsync je správně deklarována

3. **Test IAudioProcessor**
   - Ověřit, že rozhraní IAudioProcessor je správně definováno
   - Ověřit, že metoda StartProcessingAsync je správně deklarována

4. **Test ICallSession**
   - Ověřit, že rozhraní ICallSession je správně definováno
   - Ověřit, že metody StartAsync a StopAsync jsou správně deklarovány

5. **Test ICallSessionManager**
   - Ověřit, že rozhraní ICallSessionManager je správně definováno
   - Ověřit, že metody CreateSessionAsync, GetSession, TerminateSessionAsync a GetAllSessions jsou správně deklarovány

### Integrační testy pro DI kontejner

1. **Test registrace služeb**
   - Ověřit, že všechny služby jsou správně registrovány v DI kontejneru
   - Ověřit, že služby lze získat z DI kontejneru

## Implementační kroky

1. Vytvořit adresářovou strukturu (Interfaces, Services, Configuration)
2. Definovat rozhraní v adresáři Interfaces
3. Vytvořit konfigurační třídy v adresáři Configuration
4. Rozšířit Program.cs o registraci služeb a konfiguraci
5. Rozšířit appsettings.json o konfiguraci SIP serveru
6. Přidat potřebné NuGet balíčky
7. Implementovat unit testy pro rozhraní
8. Implementovat integrační testy pro DI kontejner
