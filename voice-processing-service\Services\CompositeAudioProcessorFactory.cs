using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Factory that composes STT and optional WAV processors into a CompositeAudioProcessor.
    /// </summary>
    public class CompositeAudioProcessorFactory
    {
        private readonly ILoggerFactory _loggerFactory;
        private readonly IOptions<WavRecordingOptions> _wavOptions;
        private readonly PhonexiaSttProcessorFactory _sttFactory;
        private readonly WavAudioProcessorFactory _wavFactory;
        private readonly ILogger<CompositeAudioProcessor> _compositeLogger;

        public CompositeAudioProcessorFactory(
            ILoggerFactory loggerFactory,
            IOptions<WavRecordingOptions> wavOptions,
            PhonexiaSttProcessorFactory sttFactory,
            WavAudioProcessorFactory wavFactory)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _wavOptions = wavOptions ?? throw new ArgumentNullException(nameof(wavOptions));
            _sttFactory = sttFactory ?? throw new ArgumentNullException(nameof(sttFactory));
            _wavFactory = wavFactory ?? throw new ArgumentNullException(nameof(wavFactory));
            _compositeLogger = _loggerFactory.CreateLogger<CompositeAudioProcessor>();
        }

        /// <summary>
        /// Creates a composite processor for a call, always including STT and conditionally WAV.
        /// </summary>
        public IAudioProcessor CreateProcessor(string callId, string callerParty = "", string calledParty = "")
        {
            if (string.IsNullOrWhiteSpace(callId)) throw new ArgumentNullException(nameof(callId));

            var processors = new List<IAudioProcessor>
            {
                _sttFactory.CreateProcessor(callId, callerParty, calledParty)
            };

            // Enable WAV if configured and Directory present
            var wavOpts = _wavOptions.Value;
            bool wavEnabled = wavOpts?.Enabled == true && !string.IsNullOrWhiteSpace(wavOpts.Directory);

            if (wavEnabled)
            {
                processors.Add(_wavFactory.CreateProcessor(callId));
            }

            return new CompositeAudioProcessor(
                callId,
                processors,
                _loggerFactory,
                _compositeLogger);
        }
    }
}