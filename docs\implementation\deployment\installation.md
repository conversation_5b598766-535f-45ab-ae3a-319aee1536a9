# Installation Guide

This guide provides step-by-step instructions for installing and configuring the CCCZ Buddy Realtime Phonexia Server.

## Prerequisites

Before installing the server, ensure that the following prerequisites are met:

- **Operating System**: Windows Server 2019/2022 or Linux (Ubuntu 20.04+)
- **.NET Runtime**: .NET 9.0 Runtime or SDK
- **Network**: Open ports for SIP (default: 5060) and RTP (default: 10000-19998)
- **Storage**: Minimum 10GB free disk space for application and logs
- **Memory**: Minimum 4GB RAM (8GB recommended for production)
- **CPU**: Minimum 2 cores (4 cores recommended for production)
- **Phonexia API**: Valid Phonexia API key for speech-to-text functionality

## Installation Steps

### 1. Prepare the Environment

#### Windows

1. Install .NET 9.0 SDK or Runtime from [Microsoft's .NET download page](https://dotnet.microsoft.com/download/dotnet/9.0)
2. Ensure Windows Firewall allows traffic on the required ports (SIP and RTP)

#### Linux

```bash
# Install .NET 9.0 SDK
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y apt-transport-https
sudo apt-get update
sudo apt-get install -y dotnet-sdk-9.0

# Configure firewall
sudo ufw allow 5060/udp
sudo ufw allow 5060/tcp
sudo ufw allow 10000:19998/udp
```

### 2. Get the Application

#### Option 1: Build from Source

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/callc-ccczbuddy-realtime-phonexia-server.git
   cd callc-ccczbuddy-realtime-phonexia-server
   ```

2. Build the application:
   ```bash
   dotnet build -c Release
   ```

#### Option 2: Deploy Pre-built Package

1. Download the latest release package from the internal repository
2. Extract the package to the desired installation directory

### 3. Configure the Application

1. Navigate to the application directory
2. Edit the `appsettings.json` file to configure the application:

```json
{
  "SipServer": {
    "ListenIpAddress": "Any",  // Use specific IP or "Any" for all interfaces
    "ListenPort": 5060,        // SIP port
    "RtpPortMin": 10000,       // Minimum RTP port
    "RtpPortMax": 19998,       // Maximum RTP port
    "WavRecordingDirectory": "RecordedCalls"  // Directory for WAV recordings
  },
  "Phonexia": {
    "ApiUrl": "https://api.phonexia.com/v1",  // Phonexia API URL
    "ApiKey": "your-api-key-here",            // Your Phonexia API key
    "Language": "cs-CZ",                      // Language model
    "Model": "default",                       // Model name
    "ChunkSizeBytes": 8000                    // Audio chunk size
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  }
}
```

3. Create the directory for WAV recordings:
   ```bash
   mkdir -p RecordedCalls
   ```

### 4. Run the Application

#### As a Console Application

```bash
dotnet run --project voice-processing-service
```

#### As a Windows Service

1. Install the application as a Windows service using [NSSM](https://nssm.cc/):
   ```
   nssm install CCCZBuddyRealtimePhonexiaServer "C:\Path\To\dotnet.exe" "C:\Path\To\voice-processing-service.dll"
   nssm set CCCZBuddyRealtimePhonexiaServer AppDirectory "C:\Path\To\Application"
   nssm start CCCZBuddyRealtimePhonexiaServer
   ```

#### As a Linux Systemd Service

1. Create a systemd service file:
   ```bash
   sudo nano /etc/systemd/system/ccczbuddy-phonexia.service
   ```

2. Add the following content:
   ```
   [Unit]
   Description=CCCZ Buddy Realtime Phonexia Server
   After=network.target

   [Service]
   WorkingDirectory=/path/to/application
   ExecStart=/usr/bin/dotnet /path/to/application/voice-processing-service.dll
   Restart=always
   RestartSec=10
   SyslogIdentifier=ccczbuddy-phonexia
   User=www-data
   Environment=ASPNETCORE_ENVIRONMENT=Production

   [Install]
   WantedBy=multi-user.target
   ```

3. Enable and start the service:
   ```bash
   sudo systemctl enable ccczbuddy-phonexia
   sudo systemctl start ccczbuddy-phonexia
   ```

### 5. Verify Installation

1. Check if the service is running:
   ```bash
   # Windows
   nssm status CCCZBuddyRealtimePhonexiaServer
   
   # Linux
   sudo systemctl status ccczbuddy-phonexia
   ```

2. Check the logs for any errors:
   ```bash
   # Check application logs
   tail -f /path/to/application/logs/log-*.txt
   ```

3. Test with a SIP client:
   - Configure a SIP client (e.g., Linphone, Jitsi) to connect to the server
   - Make a test call
   - Verify that the call is established and audio is processed

## Troubleshooting

### Common Issues

1. **Service fails to start**
   - Check the logs for error messages
   - Verify that the configuration in appsettings.json is correct
   - Ensure that the required ports are not in use by other applications

2. **SIP client cannot connect**
   - Verify that the SIP port (default: 5060) is open in the firewall
   - Check that the server is listening on the correct IP address
   - Verify that the SIP client is configured correctly

3. **No audio processing**
   - Verify that the RTP ports (default: 10000-19998) are open in the firewall
   - Check that the WAV recording directory exists and is writable
   - Verify that the Phonexia API key is valid

4. **Phonexia STT not working**
   - Verify that the Phonexia API key is valid
   - Check the network connectivity to the Phonexia API
   - Verify that the language model is supported

## Next Steps

After successful installation, proceed to the [Configuration Guide](configuration.md) for detailed configuration options and the [Operations Guide](../operations/monitoring.md) for monitoring and maintenance instructions.
