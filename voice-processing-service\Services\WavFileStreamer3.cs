using System.Net.WebSockets;
using System.Text;
using NAudio.Wave;
using NAudio.Codecs;
using NAudio.Wave.SampleProviders;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using voice_processing_service.Configuration;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;

namespace voice_processing_service.Services
{
    /// <summary>
    /// WAV file streamer based on the working PhonexiaConsoleIntegrationSample
    /// Uses dual WebSocket pattern: one for audio, one for transcription results
    /// </summary>
    public class WavFileStreamer3
    {
        private readonly HttpClient _httpClient;
        private readonly PhonexiaOptions _options;
        private string? _sessionId;
        private ClientWebSocket? _websocketAudio;       // For sending audio data
        private ClientWebSocket? _websocketResponse;    // For receiving transcription results
        private string? _taskId;

        public WavFileStreamer3(HttpClient httpClient, IOptions<PhonexiaOptions> options)
        {
            _httpClient = httpClient;
            _options = options.Value;
        }        public async Task<bool> StreamWavFileAsync(string wavFilePath)
        {
            try
            {
                Console.WriteLine($"Starting WAV file streaming for: {wavFilePath}");

                // Step 1: Login
                if (!await LoginAsync())
                {
                    Console.WriteLine("❌ Login failed");
                    return false;
                }

                // Step 2: Get Technologies
                if (!await GetTechnologiesAsync())
                {
                    Console.WriteLine("❌ Get technologies failed");
                    return false;
                }

                // Step 3: Create dual WebSocket streams (exactly like working sample)
                _taskId = await CreateWebSocketStreamAsync();
                if (string.IsNullOrEmpty(_taskId))
                {
                    Console.WriteLine("❌ Create stream failed");
                    return false;
                }

                Console.WriteLine("✅ Transcription started successfully!");
                Console.WriteLine("WebSocket transcript Connected.");
                Console.WriteLine("Starting WAV file streaming...");

                // Step 4: Process and stream WAV file (replaces microphone input)
                if (!await ProcessAndStreamWavAsync(wavFilePath))
                {
                    Console.WriteLine("❌ WAV processing and streaming failed");
                    return false;
                }

                Console.WriteLine("WAV streaming completed.");

                // Step 5: Wait for final results
                Console.WriteLine("⏳ Waiting for final transcription results...");
                await Task.Delay(3000);

                // Step 6: Stop transcription
                await StopTranscriptionAsync(_taskId);

                Console.WriteLine("✅ WAV file streaming completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during WAV streaming: {ex.Message}");
                return false;
            }
            finally
            {
                await CleanupAsync();
            }
        }        private async Task<bool> LoginAsync()
        {
            try
            {
                Console.WriteLine("Logging in...");
                var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_options.Username}:{_options.Password}"));
                var loginRequest = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/login");
                loginRequest.Headers.Authorization = new AuthenticationHeaderValue("Basic", authHeader);

                var loginResponse = await _httpClient.SendAsync(loginRequest);
                loginResponse.EnsureSuccessStatusCode();
                var loginJson = await loginResponse.Content.ReadAsStringAsync();
                
                Console.WriteLine($"Login response: {loginJson}");
                
                dynamic loginData = JObject.Parse(loginJson);
                _sessionId = loginData.result.session.id;

                Console.WriteLine($"✅ Login successful! Session ID: {_sessionId}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Login error: {ex.Message}");
                return false;
            }
        }        private async Task<bool> GetTechnologiesAsync()
        {
            try
            {
                Console.WriteLine("Getting technologies...");
                var technologiesRequest = new HttpRequestMessage(HttpMethod.Get, $"{_options.ApiUrl}/technologies");
                technologiesRequest.Headers.Add("X-SessionID", _sessionId);

                var technologiesResponse = await _httpClient.SendAsync(technologiesRequest);
                technologiesResponse.EnsureSuccessStatusCode();
                var technologiesJson = await technologiesResponse.Content.ReadAsStringAsync();
                
                Console.WriteLine($"Available technologies: {technologiesJson}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Get technologies error: {ex.Message}");
                return false;
            }
        }        /// <summary>
        /// Creates dual WebSocket streams exactly like the working PhonexiaConsoleIntegrationSample
        /// </summary>
        private async Task<string?> CreateWebSocketStreamAsync()
        {
            try
            {
                Console.WriteLine("Creating Input Stream and connecting to websocket...");
                var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_options.Username}:{_options.Password}"));
                
                // Step 1: Create audio WebSocket for sending data
                _websocketAudio = new ClientWebSocket();
                var audioUri = new Uri($"ws://localhost:8600/input_stream/websocket?frequency=8000&n_channels=1");

                // Set headers for audio WebSocket (like working sample)
                _websocketAudio.Options.SetRequestHeader("Accept", "application/json");
                _websocketAudio.Options.SetRequestHeader("X-SessionID", _sessionId);

                await _websocketAudio.ConnectAsync(audioUri, CancellationToken.None);
                Console.WriteLine("✅ Audio WebSocket Connected.");
                
                // Receive handshake and get input stream ID
                var streamJson = await ReceiveHandshakeAsync(_websocketAudio);
                dynamic streamData = JObject.Parse(streamJson);
                var inputStreamId = streamData.result.input_stream;
                Console.WriteLine($"Input Stream ID received: {inputStreamId}");

                // Step 2: Bind STT technology to the input stream
                var sttRequest = new HttpRequestMessage(HttpMethod.Post,
                    $"{_options.ApiUrl}/technologies/stt/input_stream?input_stream={inputStreamId}&model={_options.Model}");
                sttRequest.Headers.Add("X-SessionID", _sessionId);
                
                HttpResponseMessage sttResponse = await _httpClient.SendAsync(sttRequest);
                sttResponse.EnsureSuccessStatusCode();
                var sttJson = await sttResponse.Content.ReadAsStringAsync();
                dynamic sttData = JObject.Parse(sttJson);
                
                Console.WriteLine($"STT binding response: {sttData}");
                var taskId = sttData.result.stream_task_info.id;
                Console.WriteLine($"Task ID: {taskId}");

                // Step 3: Create second WebSocket for receiving transcription results
                _websocketResponse = new ClientWebSocket();
                var responseUri = new Uri($"ws://localhost:8600/technologies/stt/input_stream?task={taskId}&interval=0.33&trigger_events=transcription");

                // Set headers for response WebSocket (CRITICAL: includes Authorization header)
                _websocketResponse.Options.SetRequestHeader("Authorization", "Basic " + authHeader);
                _websocketResponse.Options.SetRequestHeader("Accept", "application/json");
                _websocketResponse.Options.SetRequestHeader("X-SessionID", _sessionId);

                await _websocketResponse.ConnectAsync(responseUri, CancellationToken.None);
                Console.WriteLine("✅ Response WebSocket Connected.");

                // Start listening for transcription results on the response WebSocket
                _ = Task.Run(() => StartReceiveAsync(_websocketResponse));

                return taskId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ WebSocket connection error: {ex.Message}");
                return null;
            }
        }        /// <summary>
        /// Receives handshake message from WebSocket (from working sample)
        /// </summary>
        private async Task<string> ReceiveHandshakeAsync(ClientWebSocket websocket)
        {
            WebSocketReceiveResult result;
            byte[] buffer = new byte[1024 * 10];
            try
            {
                result = await websocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                Console.WriteLine("Handshake received");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ WebSocket receive error: {ex.Message}");
                return "";
            }
            
            if (result.MessageType == WebSocketMessageType.Close)
            {
                Console.WriteLine("WebSocket closed by server.");
                await websocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                return "";
            }
            
            if (result.MessageType == WebSocketMessageType.Text)
            {
                return Encoding.UTF8.GetString(buffer, 0, result.Count);
            }
            
            return "";
        }        /// <summary>
        /// Listens for transcription results (from working sample)
        /// </summary>
        private async Task StartReceiveAsync(ClientWebSocket websocket)
        {
            Console.WriteLine("Starting transcription result listener...");
            
            while (websocket.State == WebSocketState.Open)
            {
                WebSocketReceiveResult result;
                byte[] buffer = new byte[1024 * 10];
                try
                {
                    result = await websocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ WebSocket receive error: {ex.Message}");
                    break;
                }
                
                if (result.MessageType == WebSocketMessageType.Close)
                {
                    Console.WriteLine("Response WebSocket closed by server.");
                    break;
                }

                if (result.MessageType == WebSocketMessageType.Binary)
                {
                    Console.WriteLine($"Received binary message: {result.Count} bytes");
                }

                if (result.MessageType == WebSocketMessageType.Text)
                {
                    string message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    await ProcessTranscriptionResultAsync(message);
                }
            }
            
            Console.WriteLine("Transcription result listener stopped");
        }        /// <summary>
        /// Processes transcription results using the exact same logic as working sample
        /// </summary>
        private async Task ProcessTranscriptionResultAsync(string message)
        {
            try
            {
                Console.WriteLine($"Received transcription message: {message}");
                
                var sttData = JsonConvert.DeserializeObject<dynamic>(message);

                // Process one_best_result (from working sample)
                if (sttData?.result?.one_best_result?.segmentation != null)
                {
                    var text = string.Empty;
                    foreach (var segment in sttData.result.one_best_result.segmentation)
                    {
                        var wordValue = segment?.word?.ToString();
                        if (!string.IsNullOrEmpty(wordValue) && !wordValue.StartsWith("<"))
                        {
                            Console.WriteLine($"✅ Transcribed Text (one_best): {wordValue}");
                            text += $"{wordValue} ";                            
                        }
                    }
                    Console.WriteLine($"✅ Transcribed Text (one_best); Composed: {text.Trim()}");
                }

                // Process n_best_result (from working sample)
                if (sttData?.result?.n_best_result?.phrase_variants != null)
                {
                    foreach (var variant in sttData.result.n_best_result.phrase_variants)
                    {
                        if (variant?.variant != null)
                        {
                            foreach (var phrase in variant.variant)
                            {
                                var phraseValue = phrase?.phrase?.ToString();
                                if (!string.IsNullOrEmpty(phraseValue))
                                {
                                    Console.WriteLine($"✅ Transcribed Text (n_best): {phraseValue}");
                                }
                            }
                        }
                    }
                }

                // Process confusion_network_result (from working sample)
                if (sttData?.result?.confusion_network_result != null)
                {
                    foreach (var item in sttData.result.confusion_network_result)
                    {
                        var wordValue = item?.word?.ToString();
                        if (!string.IsNullOrEmpty(wordValue))
                        {
                            Console.WriteLine($"✅ Transcribed Text (confusion network): {wordValue}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error while parsing transcription JSON: {ex.Message}");
                Console.WriteLine($"Raw message: {message}");
            }
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Processes and streams WAV file data using the same proven logic from voice-processing-simulator
        /// </summary>
        private async Task<bool> ProcessAndStreamWavAsync(string wavFilePath)
        {
            try
            {
                if (!File.Exists(wavFilePath))
                {
                    Console.WriteLine($"❌ WAV file not found: {wavFilePath}");
                    return false;
                }                Console.WriteLine("Processing WAV file for streaming...");

                // Use the same TARGET PCM format as the working simulator
                const int targetSampleRateHz = 8000;
                const int targetChannels = 1;       // Mono
                const int targetBitsPerSample = 16;  // 16-bit linear PCM
                var targetWaveFormat = new WaveFormat(targetSampleRateHz, targetBitsPerSample, targetChannels);

                Console.WriteLine($"Target PCM format: {targetWaveFormat.Encoding}, {targetWaveFormat.SampleRate}Hz, {targetWaveFormat.Channels} channels, {targetWaveFormat.BitsPerSample} bits/sample");

                using var pcmStream = new MemoryStream(); // This will hold the final PCM data

                // NAudio processing chain (exactly like the working simulator):
                using (var reader = new WaveFileReader(wavFilePath))
                {
                    Console.WriteLine($"Source WAV file details: Format {reader.WaveFormat.Encoding}, {reader.WaveFormat.SampleRate}Hz, {reader.WaveFormat.Channels} channels, {reader.WaveFormat.BitsPerSample} bits/sample");

                    // Step 1: Ensure the stream is PCM and matches target bit depth and channels *before* resampling sample rate.
                    if (reader.WaveFormat.Encoding != WaveFormatEncoding.Pcm ||
                        reader.WaveFormat.BitsPerSample != targetWaveFormat.BitsPerSample ||
                        reader.WaveFormat.Channels != targetWaveFormat.Channels)
                    {
                        Console.WriteLine($"Converting source WAV ({reader.WaveFormat.Encoding}, {reader.WaveFormat.BitsPerSample}bit, {reader.WaveFormat.Channels}ch) to target PCM format before resampling.");
                        
                        // Create a format for an intermediate PCM stream that matches target channels and bit depth, but original sample rate
                        var intermediatePcmFormat = new WaveFormat(reader.WaveFormat.SampleRate, targetWaveFormat.BitsPerSample, targetWaveFormat.Channels);
                        using (var conversionStream = new WaveFormatConversionStream(intermediatePcmFormat, reader))
                        {
                            // If only sample rate needs changing now
                            if (conversionStream.WaveFormat.SampleRate != targetWaveFormat.SampleRate)
                            {
                                Console.WriteLine($"Resampling from {conversionStream.WaveFormat.SampleRate}Hz to {targetWaveFormat.SampleRate}Hz.");
                                using (var resampler = new MediaFoundationResampler(conversionStream, targetWaveFormat) { ResamplerQuality = 60 })
                                {
                                    WaveFileWriter.WriteWavFileToStream(pcmStream, resampler); // Write all resampled data
                                }
                            }
                            else
                            {
                                Console.WriteLine("✅ Audio is now in target format after initial conversion (no sample rate change needed).");
                                WaveFileWriter.WriteWavFileToStream(pcmStream, conversionStream); // Write all converted data
                            }
                        }
                    }
                    // Step 2: If already PCM and correct bit depth/channels, just check sample rate
                    else if (reader.WaveFormat.SampleRate != targetWaveFormat.SampleRate)
                    {
                        Console.WriteLine($"Source is {reader.WaveFormat.Encoding.ToString().ToUpper()} with correct channels/bit depth, but needs resampling from {reader.WaveFormat.SampleRate}Hz to {targetWaveFormat.SampleRate}Hz.");
                        using (var resampler = new MediaFoundationResampler(reader, targetWaveFormat) { ResamplerQuality = 60 })
                        {
                            WaveFileWriter.WriteWavFileToStream(pcmStream, resampler); // Write all resampled data
                        }
                    }
                    // Step 3: Already in the perfect target format
                    else
                    {
                        Console.WriteLine("✅ Source audio is already in the exact target PCM format. Copying directly.");
                        WaveFileWriter.WriteWavFileToStream(pcmStream, reader); // Write all original data
                    }
                } // reader is disposed here

                pcmStream.Position = 0; // Reset stream position for reading

                if (pcmStream.Length == 0)
                {
                    Console.WriteLine("❌ PCM stream is empty after processing. Nothing to send.");
                    return false;
                }

                double pcmStreamDurationSec = (double)pcmStream.Length / targetWaveFormat.AverageBytesPerSecond;
                Console.WriteLine($"Prepared PCM stream: Length {pcmStream.Length} bytes, Duration {pcmStreamDurationSec:F2}s.");

                // Now stream the properly converted PCM data in chunks
                var buffer = new byte[160]; // 160 bytes = 80 samples for 8kHz 16-bit = 10ms of audio (typical RTP packet size)
                int totalBytesSent = 0;
                int chunkCount = 0;

                while (pcmStream.Position < pcmStream.Length)
                {
                    int bytesRead = await pcmStream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0) break;

                    chunkCount++;
                    totalBytesSent += bytesRead;

                    if (_websocketAudio?.State == WebSocketState.Open)
                    {
                        await _websocketAudio.SendAsync(
                            new ArraySegment<byte>(buffer, 0, bytesRead),
                            WebSocketMessageType.Binary,
                            true,
                            CancellationToken.None);

                        // Log progress every 100 chunks (1 second of audio)
                        if (chunkCount % 100 == 0)
                        {
                            double progressSec = (double)totalBytesSent / targetWaveFormat.AverageBytesPerSecond;
                            Console.WriteLine($"Sent {chunkCount} audio chunks ({totalBytesSent} bytes, {progressSec:F1}s of audio)");
                        }

                        // Simulate real-time streaming - 10ms delay for each 10ms audio chunk
                        await Task.Delay(10);
                    }
                    else
                    {
                        Console.WriteLine("❌ Audio WebSocket is not open, stopping stream");
                        return false;
                    }
                }

                Console.WriteLine($"✅ WAV file streaming completed: {chunkCount} chunks, {totalBytesSent} bytes, {pcmStreamDurationSec:F2}s duration");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error processing and streaming WAV: {ex.Message}");
                return false;
            }
        }        /// <summary>
        /// Stops transcription (from working sample)
        /// </summary>
        private async Task StopTranscriptionAsync(string taskId)
        {
            try
            {
                Console.WriteLine($"Stopping transcription for task: {taskId}");
                var sttRequest = new HttpRequestMessage(HttpMethod.Delete, $"{_options.ApiUrl}/technologies/stt/input_stream?task={taskId}");
                sttRequest.Headers.Add("X-SessionID", _sessionId);
                var sttResponse = await _httpClient.SendAsync(sttRequest);
                sttResponse.EnsureSuccessStatusCode();
                Console.WriteLine("✅ Transcription stopped");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Stop transcription error: {ex.Message}");
            }
        }        /// <summary>
        /// Cleanup resources
        /// </summary>
        private async Task CleanupAsync()
        {
            try
            {
                Console.WriteLine("Cleaning up resources...");
                
                if (_websocketAudio?.State == WebSocketState.Open)
                {
                    await _websocketAudio.CloseAsync(WebSocketCloseStatus.NormalClosure, "Completed", CancellationToken.None);
                }
                _websocketAudio?.Dispose();
                
                if (_websocketResponse?.State == WebSocketState.Open)
                {
                    await _websocketResponse.CloseAsync(WebSocketCloseStatus.NormalClosure, "Completed", CancellationToken.None);
                }
                _websocketResponse?.Dispose();
                
                _websocketAudio = null;
                _websocketResponse = null;
                _sessionId = null;
                _taskId = null;
                
                Console.WriteLine("✅ Cleanup completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Cleanup error: {ex.Message}");
            }
        }
    }
}
