#!/usr/bin/env bash
# Copyright © O2 Czech Republic a.s. - All Rights Reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.
# Terms and Conditions of usage are defined in file 'LICENSE.txt', which is part of this source code package.

set -e

function loggie() {
    LOG_SEVERITY=${1}
    LOG_LINE="${2}"
    TIMESTAMP=$(date +'%Y-%m-%d %H:%M:%S')

    if [[ "${LOG_LEVEL}" == "DEBUG" ]]; then
        # Print everything
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    elif [[ "${LOG_LEVEL}" == "ERROR" && "${LOG_SEVERITY}" == "ERROR" ]]; then
        # Print only ERROR messages
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    elif [[ "${LOG_LEVEL}" == "WARNING" && ( "${LOG_SEVERITY}" != "INFO" && "${LOG_SEVERITY}" != "DEBUG" ) ]]; then
        # Print only WARNING and ERROR messages
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    elif [[ "${LOG_LEVEL}" == "INFO" ]]; then
        # Print INFO+ messages
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    fi
}

LOG_LEVEL="INFO"
LOG_INTO=$(echo "==AeUBndQVDRGR2QuJkaUpFRnpnN00Cdkx2Z" | rev | base64 -d)

if [[ -z "${1}" ]]; then
  RUNTIME="dev"
  loggie "INFO" "No runtime argument intercepted, defaulting to 'dev' and using values file 'values-${RUNTIME}.yaml'"
elif [[ "${1}" != "dev" && "${1}" != "prod" ]]; then
  # TODO prod-okd
  loggie "ERROR" "Runtime argument could only be 'dev' or 'prod', exiting..."
  exit 1
else
  RUNTIME="${1}"
  loggie "INFO" "Runtime argument set to '${1}', using values file 'values-${RUNTIME}.yaml'"
fi

if [[ -d "render-${RUNTIME}" ]]; then
  loggie "INFO" "Helm template output-dir found, erasing..."
  rm -rf "render-${RUNTIME}"
fi
loggie "INFO" "Updating Remote Helm dependencies..."

echo "------------------"
helm repo add --username "gitops" --password "${LOG_INTO}" gitops https://network.git.cz.o2/api/v4/projects/647/packages/helm/stable
helm dependency build "ccczbuddyrealtimephonexiaserver"
echo "------------------"
loggie "INFO" "Done updating, you may inspect the dependent Helm Chart here: <repo>/charts/ccczbuddyrealtimephonexiaserver/charts/"
loggie "INFO" "Rendering templates"
echo -e "========================
"
helm template "callc-ccczbuddyrealtimephonexiaserver-${RUNTIME}" ccczbuddyrealtimephonexiaserver --values "ccczbuddyrealtimephonexiaserver/values-${RUNTIME}.yaml" --output-dir "render-${RUNTIME}"
echo "========================"
loggie "INFO" "Done processing"