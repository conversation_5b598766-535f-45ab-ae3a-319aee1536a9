# Voice Processing Service - Docker Deployment

This document provides instructions for deploying the Voice Processing Service using Docker and Docker Compose.

## Features

- **SIP Registration Support**: Handles SIP REGISTER requests for Genesys endpoint 17999
- **Containerized Deployment**: Full Docker support with .NET 9
- **File Logging**: Serilog with rolling file logs
- **Environment Flexibility**: Configurable via environment variables
- **Health Monitoring**: Built-in health check endpoints
- **Persistent Storage**: External volumes for logs and recordings

## Quick Start

### Prerequisites

- Docker 20.10+ or Podman 3.0+
- Docker Compose 2.0+ (if using Docker Compose)
- At least 2GB RAM available
- Ports 5090/udp, 8081/tcp and 10000-10100/udp available (updated defaults)

### Local Development with Docker Compose

1. **Clone and navigate to the project directory**
2. **Create data directories**:
   ```bash
   mkdir -p data/logs data/recordings
   ```

3. **Start the services**:
   ```bash
   docker-compose up -d
   ```

4. **Check service status**:
   ```bash
   docker-compose ps
   curl http://localhost:8081/health
   ```

### Production Deployment

#### Option 1: Using Versioned Build Script (Recommended)
```bash
# Build with version tag
./build-versioned.sh 0.1

# Run with version tag
podman run -d \
  --name voice-processing-service \
  -p 5090:5090/udp \
  -p 10000-10100:10000-10100/udp \
  -p 8081:8081 \
  -v /host/logs:/app/logs \
  -v /host/recordings:/app/recordings \
  -e Phonexia__ApiUrl=http://your-phonexia-server:8600 \
  voice-processing-service:0.1
```

#### Option 2: Manual Build
```bash
# Build the production image
docker build -t voice-processing-service:0.1 ./voice-processing-service

# Run with production configuration (updated ports)
docker run -d \
  --name voice-processing-service \
  -p 5090:5090/udp \
  -p 10000-10100:10000-10100/udp \
  -p 8081:8081 \
  -v /host/logs:/app/logs \
  -v /host/recordings:/app/recordings \
  -e ASPNETCORE_ENVIRONMENT=Production \
  -e Phonexia__ApiUrl=http://your-phonexia-server:8600 \
  voice-processing-service:0.1
```

## Platform-Specific Commands

### Windows (Docker Desktop + PowerShell)
```powershell
# Build with version tag (no backslashes needed in PowerShell)
./build-versioned.sh 0.1

# Run container
docker run -d --name voice-processing-service -p 5090:5090/udp -p 10000-10100:10000-10100/udp -p 8081:8081 -v ${PWD}/data/recordings:/app/recordings -e Phonexia__ApiUrl=http://your-phonexia-server:8600 voice-processing-service:1.2.3

# Health check
curl http://localhost:8081/health
```

### RHEL9 (Podman)
```bash
# Load image from tar file
podman load -i voice-processing-service-0.1.tar

# Run container (fresh command without --restart)
podman run -d --name voice-processing-service -p 5090:5090/udp -p 10000-10100:10000-10100/udp -p 8081:8081 -v /data/recordings:/app/recordings -e Phonexia__ApiUrl=http://your-phonexia-server:8600 voice-processing-service:1.2.3

# Health check
curl http://localhost:8081/health

# Check container status
podman ps

# View logs
podman logs voice-processing-service
```

## Configuration

### Environment Variables

#### RTP Inactivity Timeout

- **RtpReceiver.InactivityTimeoutMs** (default: `30000` ms)
  Controls how long RTP media can be inactive before the session is considered ended.
  - Prevents premature media shutdown during SIP re-INVITE pauses.
  - Override via environment variable: `RtpReceiver__InactivityTimeoutMs`
  - Example:
    - Linux/Windows: `export RtpReceiver__InactivityTimeoutMs=45000`
    - Docker: `-e RtpReceiver__InactivityTimeoutMs=45000`
  - Recommended values:
    - 30000 ms (30s) for typical SIP/RTP deployments with re-INVITE support.
    - Lower values for aggressive cleanup (e.g., 5000 ms).

**Troubleshooting:**
If media stops before BYE, increase `RtpReceiver.InactivityTimeoutMs`.

#### Core Application
- `ASPNETCORE_ENVIRONMENT`: Application environment (Development/Production)
- `ASPNETCORE_URLS`: HTTP binding URLs (default: http://+:8081)

- `RECORDINGS_PATH`: Directory for recordings (default: /app/recordings)

#### SIP Server Configuration (Standard ASP.NET Core Format)
- `SipServer__ListenIpAddress`: SIP server listen IP (default: Any)
- `SipServer__ListenPort`: SIP server port (default: 5090)
- `SipServer__RtpPortMin`: Minimum RTP port (default: 10000)
- `SipServer__RtpPortMax`: Maximum RTP port (default: 19998)
- `SipServer__WavRecordingDirectory`: Recording directory (default: /app/recordings)
- `SipServer__OverrideSdpConnectionAddress`: Override IP address for SDP connection field (default: null - uses client's IP)

#### Phonexia API Configuration (Standard ASP.NET Core Format)
- `Phonexia__ApiUrl`: Phonexia API URL (default: http://localhost:8600) - Host and port are derived from this URL
- `Phonexia__Username`: Phonexia username (default: admin)
- `Phonexia__Password`: Phonexia password (default: phonexia)
- `Phonexia__Language`: Speech recognition language (default: cs-CZ)
- `Phonexia__Model`: Speech recognition model (default: CS_CZ_O2_6)
- `Phonexia__ChunkSizeBytes`: Audio chunk size in bytes (default: 8000)

### Volume Mounts

- `/app/logs`: Application logs (should be mounted to persistent storage)
- `/app/recordings`: Call recordings (should be mounted to persistent storage)
- `/app/appsettings.Production.json`: Optional configuration override

### Port Mappings

- `5090/udp`: SIP signaling port (updated default)
- `10000-19998/udp`: RTP media ports (adjust range as needed)
- `8081/tcp`: HTTP API and health check port (updated default)

## Health Monitoring

### Health Check Endpoints

- `GET /health`: Basic health status
- `GET /health/detailed`: Detailed health information including active SIP registrations

### Example Health Check Response

```json
{
  "Status": "Healthy",
  "Timestamp": "2025-01-16T10:30:00Z",
  "Version": "*******",
  "ActiveRegistrations": 1,
  "Environment": "Production"
}
```

## Deployment Scenarios

### Scenario 1: Local Development
Use `docker-compose.yml` with local Phonexia container:
```bash
docker-compose up -d
```

### Scenario 2: Production with Remote Phonexia
```bash
docker run -d \
  --name voice-processing-service \
  -p 5090:5090/udp \
  -p 10000-10100:10000-10100/udp \
  -p 8081:8081 \
  -v /data/voice-logs:/app/logs \
  -v /data/voice-recordings:/app/recordings \
  -e Phonexia__ApiUrl=http://phonexia.company.com:8600 \
  -e Phonexia__Username=production_user \
  -e Phonexia__Password=secure_password \
  voice-processing-service:latest
```

### Scenario 3: Kubernetes Deployment
See `k8s-deployment.yaml` for Kubernetes manifests (if available).

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure SIP and RTP ports are not in use
2. **Permission issues**: Check volume mount permissions
3. **Phonexia connectivity**: Verify Phonexia API URL and credentials
4. **Memory issues**: Ensure sufficient RAM for .NET application

### Logs

Check application logs:
```bash
docker logs voice-processing-service
# or
tail -f data/logs/voice-processing-service-*.log
```

### Debug Mode

Run with debug logging:
```bash
docker run -e Logging__LogLevel__Default=Debug voice-processing-service:latest
```

## Security Considerations

- Run container as non-root user (implemented in Dockerfile)
- Use secrets management for Phonexia credentials
- Restrict network access to required ports only
- Regularly update base images for security patches
- Monitor logs for suspicious SIP activity

## Performance Tuning

- Adjust RTP port range based on expected concurrent calls
- Configure appropriate memory limits
- Use SSD storage for logs and recordings
- Monitor CPU and memory usage under load

## Support

For issues and questions:
1. Check application logs
2. Verify configuration
3. Test health endpoints
4. Review Docker container status

## Run with WAV Recording

Enable on-disk WAV recording while keeping STT unchanged (fan-out via [C#.CompositeAudioProcessor](voice-processing-service/Services/CompositeAudioProcessor.cs:1)).

- Volume mount and environment overrides (PowerShell):
```powershell
docker run --rm -p 8080:8080 ^
  -v ${PWD}/data/recordings:/app/recordings ^
  -e WavRecording__Enabled=true ^
  -e WavRecording__Directory=/app/recordings ^
  -e WavRecording__FilenameTemplate="{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav" ^
  your-image:tag
```

Notes:
- Files are written under WavRecording:Directory (default /app/recordings).
- Rotation: a new segment is created when duration exceeds MaxFileDurationSeconds.
- Split on format change when enabled (e.g., re-INVITE/codec change).
- Logs indicate segment open/close/rotation in [C#.WavAudioProcessor](voice-processing-service/Services/WavAudioProcessor.cs:1).
