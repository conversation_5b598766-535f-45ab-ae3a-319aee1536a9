# Implementace SIP serveru

## Popis úkolu

Tento úkol zahrnuje implementaci SIP serveru jako IHostedService, k<PERSON><PERSON> bude naslouchat SIP požadavkům, zpracovávat je a delegovat je na CallSessionManager. SIP server bude zodpovědný za:

1. Inicializaci SIP transportu
2. Zpracování SIP požadavků (INVITE, BYE, CANCEL)
3. Vytváření a ukončování ho<PERSON>ů prostřednictvím CallSessionManager

## Technické detaily

### Implementace SipServerService

Třída SipServerService implementuje rozhraní IHostedService a je zodpovědná za inicializaci a správu SIP serveru.

```csharp
using System;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace SIP serveru jako IHostedService.
    /// </summary>
    public class SipServerService : IHostedService
    {
        private readonly ILogger<SipServerService> _logger;
        private readonly ICallSessionManager _sessionManager;
        private readonly SipServerOptions _options;
        private readonly ILoggerFactory _loggerFactory;
        private readonly Func<string, IPAddress, IAudioInputReceiver> _rtpReceiverFactory;
        private readonly Func<string, string, IAudioProcessor> _wavProcessorFactory;
        private SIPTransport _sipTransport;
        private SIPUserAgent _userAgent;

        /// <summary>
        /// Vytvoří novou instanci SipServerService.
        /// </summary>
        /// <param name="logger">Logger.</param>
        /// <param name="sessionManager">Správce sessions hovorů.</param>
        /// <param name="options">Konfigurace SIP serveru.</param>
        /// <param name="loggerFactory">Továrna pro vytváření loggerů.</param>
        /// <param name="rtpReceiverFactory">Tovární metoda pro vytvoření RTP přijímače.</param>
        /// <param name="wavProcessorFactory">Tovární metoda pro vytvoření WAV procesoru.</param>
        public SipServerService(
            ILogger<SipServerService> logger,
            ICallSessionManager sessionManager,
            IOptions<SipServerOptions> options,
            ILoggerFactory loggerFactory,
            Func<string, IPAddress, IAudioInputReceiver> rtpReceiverFactory,
            Func<string, string, IAudioProcessor> wavProcessorFactory)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _sessionManager = sessionManager ?? throw new ArgumentNullException(nameof(sessionManager));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _rtpReceiverFactory = rtpReceiverFactory ?? throw new ArgumentNullException(nameof(rtpReceiverFactory));
            _wavProcessorFactory = wavProcessorFactory ?? throw new ArgumentNullException(nameof(wavProcessorFactory));
        }

        /// <summary>
        /// Spustí SIP server.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting SIP server...");

            try
            {
                // Vytvoření SIP transportu
                _sipTransport = new SIPTransport();
                
                // Nastavení loggeru pro SIPSorcery
                SIPSorcery.LogFactory.Set(_loggerFactory);

                // Nastavení IP adresy pro naslouchání
                IPAddress listenAddress = _options.ListenIpAddress == "Any" ? IPAddress.Any : IPAddress.Parse(_options.ListenIpAddress);

                // Přidání SIP kanálu pro naslouchání
                var sipChannel = new SIPUDPChannel(listenAddress, _options.ListenPort);
                _sipTransport.AddSIPChannel(sipChannel);

                _logger.LogInformation($"SIP server listening on {listenAddress}:{_options.ListenPort}");

                // Vytvoření SIP User Agenta
                _userAgent = new SIPUserAgent(_sipTransport, null);

                // Registrace handleru pro INVITE požadavky
                _sipTransport.SIPTransportRequestReceived += OnSipRequestReceived;

                _logger.LogInformation("SIP server started successfully.");

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting SIP server.");
                throw;
            }
        }

        /// <summary>
        /// Zastaví SIP server.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping SIP server...");

            try
            {
                // Ukončení všech aktivních hovorů
                foreach (var session in _sessionManager.GetAllSessions())
                {
                    try
                    {
                        _logger.LogInformation($"Terminating call {session.CallId}...");
                        await _sessionManager.TerminateSessionAsync(session.CallId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"Error terminating call {session.CallId}.");
                    }
                }

                // Odregistrace handleru pro INVITE požadavky
                if (_sipTransport != null)
                {
                    _sipTransport.SIPTransportRequestReceived -= OnSipRequestReceived;
                }

                // Uvolnění SIP transportu
                _sipTransport?.Dispose();
                _sipTransport = null;

                _logger.LogInformation("SIP server stopped successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping SIP server.");
                throw;
            }
        }

        /// <summary>
        /// Handler pro příjem SIP požadavků.
        /// </summary>
        /// <param name="localSIPEndPoint">Lokální SIP endpoint.</param>
        /// <param name="remoteEndPoint">Vzdálený endpoint.</param>
        /// <param name="sipRequest">SIP požadavek.</param>
        private void OnSipRequestReceived(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            string callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Received {sipRequest.Method} request from {remoteEndPoint}.");

            try
            {
                switch (sipRequest.Method)
                {
                    case SIPMethodsEnum.INVITE:
                        // Zpracování INVITE požadavku
                        _ = ProcessInviteAsync(localSIPEndPoint, remoteEndPoint, sipRequest);
                        break;

                    case SIPMethodsEnum.BYE:
                        // Zpracování BYE požadavku
                        _ = ProcessByeAsync(sipRequest);
                        break;

                    case SIPMethodsEnum.CANCEL:
                        // Zpracování CANCEL požadavku
                        _ = ProcessCancelAsync(sipRequest);
                        break;

                    default:
                        // Ostatní požadavky necháme zpracovat SIPSorcery
                        _logger.LogDebug($"[{callId}] Letting SIPSorcery handle {sipRequest.Method} request.");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing {sipRequest.Method} request.");
            }
        }

        /// <summary>
        /// Zpracuje INVITE požadavek.
        /// </summary>
        /// <param name="localSIPEndPoint">Lokální SIP endpoint.</param>
        /// <param name="remoteEndPoint">Vzdálený endpoint.</param>
        /// <param name="sipRequest">SIP požadavek.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task ProcessInviteAsync(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            string callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing INVITE request.");

            try
            {
                // Vytvoření SIP User Agenta pro tento hovor
                var userAgent = new SIPUserAgent(_sipTransport, null);

                // Nastavení handleru pro příchozí hovor
                userAgent.OnIncomingCall += async (ua, req) =>
                {
                    _logger.LogInformation($"[{callId}] Incoming call from {req.Header.From.FriendlyName} to {req.URI.User}.");

                    try
                    {
                        // Přijetí hovoru
                        var uas = userAgent.AcceptCall(req);

                        // Vytvoření továrních metod pro komponenty
                        Func<IAudioInputReceiver> receiverFactory = () =>
                        {
                            // Získání lokální IP adresy
                            IPAddress localAddress = IPAddress.Parse(localSIPEndPoint.Address);

                            // Vytvoření RTP přijímače
                            return _rtpReceiverFactory(callId, localAddress);
                        };

                        Func<IAudioProcessor> processorFactory = () =>
                        {
                            // Vytvoření cesty k WAV souboru
                            string wavFilePath = System.IO.Path.Combine(
                                _options.WavRecordingDirectory,
                                $"{callId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.wav");

                            // Vytvoření adresáře, pokud neexistuje
                            System.IO.Directory.CreateDirectory(_options.WavRecordingDirectory);

                            // Vytvoření WAV procesoru
                            return _wavProcessorFactory(callId, wavFilePath);
                        };

                        // Vytvoření session
                        var session = await _sessionManager.CreateSessionAsync(userAgent, req, receiverFactory, processorFactory);

                        // Nastavení RTP endpointu pro SIP dialog
                        var rtpEndPoint = session is CallSession callSession
                            ? (callSession._audioReceiver as RtpAudioReceiver)?.RtpLocalEndPoint
                            : null;

                        if (rtpEndPoint != null)
                        {
                            _logger.LogInformation($"[{callId}] Setting RTP endpoint to {rtpEndPoint}.");
                            uas.Answer(new SDP.SDPMessage { RtpEndPoint = rtpEndPoint });
                        }
                        else
                        {
                            _logger.LogWarning($"[{callId}] Could not get RTP endpoint from receiver.");
                            uas.Reject(SIPResponseStatusCodesEnum.InternalServerError, "Could not allocate RTP endpoint");
                            await _sessionManager.TerminateSessionAsync(callId);
                            return;
                        }

                        // Spuštění session
                        await session.StartAsync(CancellationToken.None);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{callId}] Error processing incoming call.");
                        userAgent.Hangup();
                    }
                };

                // Zpracování INVITE požadavku
                userAgent.ProcessIncomingCall(sipRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing INVITE request.");
                
                // Odeslání chybové odpovědi
                SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                await _sipTransport.SendResponseAsync(errorResponse);
            }
        }

        /// <summary>
        /// Zpracuje BYE požadavek.
        /// </summary>
        /// <param name="sipRequest">SIP požadavek.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task ProcessByeAsync(SIPRequest sipRequest)
        {
            string callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing BYE request.");

            try
            {
                // Ukončení session
                var session = _sessionManager.GetSession(callId);
                if (session != null)
                {
                    _logger.LogInformation($"[{callId}] Found active session for BYE. Terminating session.");
                    await _sessionManager.TerminateSessionAsync(callId);
                }
                else
                {
                    _logger.LogWarning($"[{callId}] No active session found for BYE.");
                }

                // Odeslání odpovědi OK
                SIPResponse okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, "OK");
                await _sipTransport.SendResponseAsync(okResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing BYE request.");
                
                // Odeslání chybové odpovědi
                SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                await _sipTransport.SendResponseAsync(errorResponse);
            }
        }

        /// <summary>
        /// Zpracuje CANCEL požadavek.
        /// </summary>
        /// <param name="sipRequest">SIP požadavek.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task ProcessCancelAsync(SIPRequest sipRequest)
        {
            string callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing CANCEL request.");

            try
            {
                // Ukončení session
                var session = _sessionManager.GetSession(callId);
                if (session != null)
                {
                    _logger.LogInformation($"[{callId}] Found active session for CANCEL. Terminating session.");
                    await _sessionManager.TerminateSessionAsync(callId);
                }
                else
                {
                    _logger.LogWarning($"[{callId}] No active session found for CANCEL.");
                }

                // Odeslání odpovědi OK
                SIPResponse okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, "OK");
                await _sipTransport.SendResponseAsync(okResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing CANCEL request.");
                
                // Odeslání chybové odpovědi
                SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                await _sipTransport.SendResponseAsync(errorResponse);
            }
        }
    }
}
```

### Rozšíření Program.cs

Program.cs bude rozšířen o registraci SipServerService:

```csharp
// Registrace SIP serveru
builder.Services.AddSingleton<ICallSessionManager, CallSessionManager>();
builder.Services.AddHostedService<SipServerService>();

// Registrace továren pro komponenty
builder.Services.AddTransient<Func<string, IPAddress, IAudioInputReceiver>>(sp =>
{
    return (callId, localAddress) =>
    {
        var logger = sp.GetRequiredService<ILogger<RtpAudioReceiver>>();
        var options = sp.GetRequiredService<IOptions<SipServerOptions>>().Value;
        return new RtpAudioReceiver(callId, localAddress, options.RtpPortMin, options.RtpPortMax, logger);
    };
});

builder.Services.AddTransient<Func<string, string, IAudioProcessor>>(sp =>
{
    return (callId, wavFilePath) =>
    {
        var logger = sp.GetRequiredService<ILogger<WavAudioProcessor>>();
        return new WavAudioProcessor(callId, wavFilePath, logger);
    };
});
```

## Testovací scénáře

### Unit testy pro SipServerService

1. **Test konstruktoru**
   - Ověřit, že instance SipServerService je vytvořena bez chyb
   - Ověřit, že všechny závislosti jsou správně nastaveny

2. **Test metody StartAsync**
   - Ověřit, že metoda StartAsync inicializuje SIP transport
   - Ověřit, že metoda StartAsync přidá SIP kanál pro naslouchání
   - Ověřit, že metoda StartAsync registruje handler pro SIP požadavky

3. **Test metody StopAsync**
   - Ověřit, že metoda StopAsync ukončí všechny aktivní hovory
   - Ověřit, že metoda StopAsync odregistruje handler pro SIP požadavky
   - Ověřit, že metoda StopAsync uvolní SIP transport

4. **Test metody OnSipRequestReceived**
   - Ověřit, že metoda OnSipRequestReceived správně deleguje požadavky podle metody
   - Ověřit, že metoda OnSipRequestReceived zpracuje INVITE požadavek
   - Ověřit, že metoda OnSipRequestReceived zpracuje BYE požadavek
   - Ověřit, že metoda OnSipRequestReceived zpracuje CANCEL požadavek

5. **Test metody ProcessInviteAsync**
   - Ověřit, že metoda ProcessInviteAsync vytvoří SIP User Agenta
   - Ověřit, že metoda ProcessInviteAsync nastaví handler pro příchozí hovor
   - Ověřit, že metoda ProcessInviteAsync vytvoří session
   - Ověřit, že metoda ProcessInviteAsync nastaví RTP endpoint
   - Ověřit, že metoda ProcessInviteAsync spustí session

6. **Test metody ProcessByeAsync**
   - Ověřit, že metoda ProcessByeAsync ukončí session
   - Ověřit, že metoda ProcessByeAsync odešle odpověď OK

7. **Test metody ProcessCancelAsync**
   - Ověřit, že metoda ProcessCancelAsync ukončí session
   - Ověřit, že metoda ProcessCancelAsync odešle odpověď OK

### Integrační testy pro SipServerService a CallSessionManager

1. **Test zpracování INVITE požadavku**
   - Vytvořit instanci SipServerService a CallSessionManager
   - Simulovat příchozí INVITE požadavek
   - Ověřit, že byla vytvořena session
   - Ověřit, že session byla spuštěna

2. **Test zpracování BYE požadavku**
   - Vytvořit instanci SipServerService a CallSessionManager
   - Simulovat příchozí INVITE požadavek a vytvořit session
   - Simulovat příchozí BYE požadavek
   - Ověřit, že session byla ukončena

3. **Test zpracování CANCEL požadavku**
   - Vytvořit instanci SipServerService a CallSessionManager
   - Simulovat příchozí INVITE požadavek a vytvořit session
   - Simulovat příchozí CANCEL požadavek
   - Ověřit, že session byla ukončena

## Implementační kroky

1. Implementovat třídu SipServerService
2. Rozšířit Program.cs o registraci SipServerService
3. Implementovat unit testy pro SipServerService
4. Implementovat integrační testy pro SipServerService a CallSessionManager
5. Otestovat SIP server s reálným SIP klientem

## Simulace pro testování

Pro testování SIP serveru bude použit simulátor SIP klienta, který bude implementován v rámci úkolu [07_testing_simulator.md](07_testing_simulator.md). Simulátor bude schopen:

1. Vytvořit SIP spojení s SIP serverem
2. Odeslat INVITE požadavek
3. Streamovat audio data přes RTP
4. Ukončit hovor pomocí BYE nebo CANCEL

Simulátor bude použit pro E2E testování celého řešení.
