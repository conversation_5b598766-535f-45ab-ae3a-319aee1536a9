{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Simulator with Args", // Descriptive name for this configuration
            "type": "coreclr",
            "request": "launch", // This tells VS Code to launch your app with the debugger
            "preLaunchTask": "build", // Ensures your project is built before debugging
            // IMPORTANT: Adjust the path to your project's output DLL.
            // Replace 'netX.Y' with your actual target framework (e.g., net7.0, net8.0).
            // You can find this in your .csproj file (e.g., <TargetFramework>net8.0</TargetFramework>).
            "program": "${workspaceFolder}/voice-processing-simulator/bin/Debug/net9.0/voice-processing-simulator.dll",
            // --- YOUR COMMAND LINE ARGUMENTS GO HERE ---
            // Example for the "simulate" command:
            "args": [
                "simulate",
                "sage.wav",
                "127.0.0.1",
                "5060",
                "30"
            ],
            "cwd": "${workspaceFolder}/voice-processing-simulator", // Set the working directory to your project folder
            "console": "integratedTerminal", // Shows console output in VS Code's terminal
            "stopAtEntry": true // <-- CRITICAL FOR YOUR SCENARIO
                                // This will pause the debugger at the very beginning
                                // of your application's Main method (or even slightly before).
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ]
}