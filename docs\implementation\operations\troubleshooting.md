# Troubleshooting Guide

This guide provides information on how to diagnose and resolve common issues with the CCCZ Buddy Realtime Phonexia Server.

## Diagnostic Tools

### Log Files

The primary source of diagnostic information is the application logs. By default, logs are stored in the `logs` directory in the application root.

```bash
# View the latest log file
tail -f logs/log-YYYY-MM-DD.txt

# Search for errors in logs
grep -i "error" logs/log-*.txt

# Search for a specific call ID
grep "CALL-ID-HERE" logs/log-*.txt
```

### Service Status

Check the status of the service to ensure it's running:

```bash
# Windows (NSSM)
nssm status CCCZBuddyRealtimePhonexiaServer

# Linux (systemd)
sudo systemctl status ccczbuddy-phonexia
```

### Network Diagnostics

Verify that the required ports are open and the server is listening:

```bash
# Check if the SIP port is open (Windows)
netstat -an | findstr :5060

# Check if the SIP port is open (Linux)
netstat -an | grep :5060

# Check if RTP ports are available (Windows)
netstat -an | findstr :10000

# Check if RTP ports are available (Linux)
netstat -an | grep :10000
```

### Process Monitoring

Monitor the process resource usage:

```bash
# Windows
tasklist /fi "imagename eq dotnet.exe" /v
typeperf "\Process(dotnet)\% Processor Time" "\Process(dotnet)\Working Set"

# Linux
ps aux | grep dotnet
top -p $(pgrep dotnet)
```

## Common Issues and Solutions

### Application Fails to Start

#### Symptoms
- Service fails to start
- Error messages in logs about configuration or initialization

#### Possible Causes
1. Invalid configuration in appsettings.json
2. Required ports already in use
3. Missing dependencies
4. Insufficient permissions

#### Solutions
1. Check the logs for specific error messages
2. Verify that the configuration in appsettings.json is valid
3. Ensure that the required ports are not in use by other applications
4. Verify that the application has the necessary permissions to access resources

```bash
# Check for port conflicts (Windows)
netstat -ano | findstr :5060
taskkill /PID <PID> /F

# Check for port conflicts (Linux)
sudo lsof -i :5060
sudo kill -9 <PID>
```

### SIP Client Cannot Connect

#### Symptoms
- SIP client reports connection failure
- No incoming calls visible in logs

#### Possible Causes
1. Firewall blocking SIP traffic
2. Server not listening on the correct IP address
3. Incorrect SIP client configuration
4. Network issues between client and server

#### Solutions
1. Check firewall settings and ensure SIP port (default: 5060) is open
2. Verify that the server is configured to listen on the correct IP address
3. Check SIP client configuration (server address, port, protocol)
4. Test network connectivity between client and server

```bash
# Test SIP connectivity (from client)
telnet server-ip 5060

# Check server listening status (on server)
netstat -an | grep LISTEN | grep :5060
```

### No Audio Processing

#### Symptoms
- Calls connect but no audio is processed
- No WAV files are created
- No transcription results

#### Possible Causes
1. RTP ports blocked by firewall
2. Incorrect RTP port range configuration
3. Audio buffer or processor issues
4. Insufficient disk space for WAV files

#### Solutions
1. Check firewall settings and ensure RTP port range is open
2. Verify RTP port range configuration in appsettings.json
3. Check logs for errors related to audio processing
4. Verify disk space availability

```bash
# Check disk space (Windows)
dir
fsutil volume diskfree C:

# Check disk space (Linux)
df -h

# Test RTP port accessibility (from client)
telnet server-ip 10000
```

### Phonexia STT Not Working

#### Symptoms
- WAV files are created but no transcription results
- Errors related to Phonexia API in logs

#### Possible Causes
1. Invalid Phonexia API key
2. Network connectivity issues to Phonexia API
3. Unsupported language model
4. Phonexia service outage

#### Solutions
1. Verify that the Phonexia API key is valid
2. Check network connectivity to the Phonexia API
3. Verify that the language model is supported
4. Check Phonexia service status

```bash
# Test connectivity to Phonexia API
curl -I https://api.phonexia.com/v1

# Check API key validity (replace with your key)
curl -H "Authorization: Bearer YOUR_API_KEY" https://api.phonexia.com/v1/info
```

### High CPU or Memory Usage

#### Symptoms
- Server becomes slow or unresponsive
- High CPU or memory usage reported by system monitoring

#### Possible Causes
1. Too many concurrent calls
2. Memory leaks
3. Inefficient audio processing
4. Insufficient server resources

#### Solutions
1. Limit the number of concurrent calls
2. Restart the service to reclaim memory
3. Update to the latest version of the software
4. Increase server resources (CPU, memory)

```bash
# Monitor resource usage (Windows)
typeperf "\Process(dotnet)\% Processor Time" "\Process(dotnet)\Working Set" -sc 10

# Monitor resource usage (Linux)
top -p $(pgrep dotnet)
```

### Call Disconnects Unexpectedly

#### Symptoms
- Calls disconnect after a short time
- Error messages about session termination in logs

#### Possible Causes
1. Network issues
2. SIP session timeout
3. RTP stream interruption
4. Server resource constraints

#### Solutions
1. Check network stability between client and server
2. Adjust SIP session timeout settings
3. Verify RTP stream continuity
4. Monitor server resource usage during calls

```bash
# Check for network issues (Windows)
ping -t client-ip

# Check for network issues (Linux)
ping -c 100 client-ip
```

## Advanced Troubleshooting

### Enabling Debug Logging

For more detailed logging, modify the `appsettings.json` file to set the log level to Debug:

```json
"Logging": {
  "LogLevel": {
    "Default": "Debug",
    "Microsoft": "Information",
    "Microsoft.Hosting.Lifetime": "Information"
  }
}
```

Restart the service to apply the changes.

### Capturing Network Traffic

Use Wireshark to capture and analyze SIP and RTP traffic:

1. Install Wireshark
2. Start a capture on the server's network interface
3. Apply filter `sip || rtp` to focus on relevant traffic
4. Make a test call
5. Analyze the captured packets for issues

### Memory Dump Analysis

For persistent issues, create and analyze a memory dump:

#### Windows
```
# Create memory dump
procdump -ma <PID> dump.dmp

# Analyze with WinDbg
windbg -z dump.dmp
```

#### Linux
```
# Create memory dump
sudo gcore $(pgrep dotnet)

# Analyze with LLDB
lldb --core core.$(pgrep dotnet)
```

## Contacting Support

If you cannot resolve the issue using this guide, contact support with the following information:

1. Detailed description of the issue
2. Steps to reproduce the issue
3. Relevant log files
4. Server configuration (sanitized of sensitive information)
5. System information (OS, .NET version, hardware specs)
6. Network configuration
7. Any error messages or codes

## Preventive Measures

To prevent issues:

1. Regularly monitor system health
2. Keep the software updated to the latest version
3. Perform regular backups of configuration and data
4. Implement proper resource scaling for expected load
5. Conduct periodic test calls to verify functionality
6. Review logs for warning signs of potential issues
