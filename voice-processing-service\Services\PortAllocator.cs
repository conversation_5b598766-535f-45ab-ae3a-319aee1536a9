using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    public class PortAllocator : IPortAllocator, IDisposable
    {
        private readonly int _min;
        private readonly int _max;
        private readonly ILogger<PortAllocator> _logger;

        // Enhanced thread-safe tracking of allocated port pairs
        private readonly ConcurrentDictionary<string, int> _allocatedPorts = new();
        private readonly ConcurrentDictionary<string, PortAllocationInfo> _portTracking = new();
        private readonly object _allocationLock = new();
        private readonly Timer _cleanupTimer;
        private bool _disposed = false;

        /// <summary>
        /// Information about a port allocation for leak detection and monitoring
        /// </summary>
        private class PortAllocationInfo
        {
            public string CallId { get; set; }
            public int RtpPort { get; set; }
            public int RtcpPort { get; set; }
            public DateTime AllocatedAt { get; set; }
            public DateTime LastActivity { get; set; }
            public bool IsActive { get; set; }
            public string AllocationSource { get; set; } // "regular" or "ephemeral"
        }

        public PortAllocator(IOptions<SipServerOptions> options, ILogger<PortAllocator> logger)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));
            var value = options.Value;
            _min = value.RtpPortMin % 2 == 0 ? value.RtpPortMin : value.RtpPortMin + 1;
            _max = value.RtpPortMax;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Start periodic cleanup timer (every 10 minutes)
            _cleanupTimer = new Timer(PerformPeriodicCleanup, null,
                (int)TimeSpan.FromMinutes(10).TotalMilliseconds,
                (int)TimeSpan.FromMinutes(10).TotalMilliseconds);
                
            _logger.LogInformation($"PortAllocator initialized with enhanced tracking. Port range: {_min}-{_max}");
        }

        public Task<(UdpClient rtpClient, UdpClient rtcpClient)> AllocateRtpPairAsync(string callId, IPAddress localAddress)
        {
            lock (_allocationLock)
            {
                // Try binding sequential ports in configured range, skipping already allocated ones
                for (int port = _min; port <= _max; port += 2)
                {
                    // Skip if this port pair is already allocated
                    if (_allocatedPorts.ContainsKey($"{port}") || _allocatedPorts.ContainsKey($"{port + 1}"))
                    {
                        continue;
                    }

                    _logger.LogInformation($"[{callId}] Trying RTP={port}, RTCP={port + 1}");
                    if (TryBindPair(callId, localAddress, port, port + 1, out var rtp, out var rtcp))
                    {
                        // Enhanced tracking of allocated ports
                        _allocatedPorts[$"{port}"] = port;
                        _allocatedPorts[$"{port + 1}"] = port + 1;
                        _allocatedPorts[$"callId:{callId}"] = port; // For easy cleanup
                        
                        // Track allocation info for monitoring and leak detection
                        var allocationInfo = new PortAllocationInfo
                        {
                            CallId = callId,
                            RtpPort = port,
                            RtcpPort = port + 1,
                            AllocatedAt = DateTime.UtcNow,
                            LastActivity = DateTime.UtcNow,
                            IsActive = true,
                            AllocationSource = "regular"
                        };
                        _portTracking[callId] = allocationInfo;
    
                        _logger.LogInformation($"[{callId}] Bound RTP={port}, RTCP={port + 1}. {GetAllocationStatus()}");
                        return Task.FromResult((rtp, rtcp));
                    }
                }
            }

            // Range exhausted: fallback to ephemeral consecutive ports
            _logger.LogWarning($"Port range {_min}-{_max} exhausted for {callId}; using ephemeral ports");

            // Allocate RTP on an OS-assigned ephemeral port
            var ephRtp = new UdpClient(AddressFamily.InterNetwork) { ExclusiveAddressUse = true };
            ephRtp.Client.Bind(new IPEndPoint(localAddress, 0));
            int ephemeralPort = ((IPEndPoint)ephRtp.Client.LocalEndPoint).Port;

            // Allocate RTCP on the next port
            var ephRtcp = new UdpClient(AddressFamily.InterNetwork) { ExclusiveAddressUse = true };
            ephRtcp.Client.Bind(new IPEndPoint(localAddress, ephemeralPort + 1));

            // Track ephemeral allocation
            var ephemeralAllocationInfo = new PortAllocationInfo
            {
                CallId = callId,
                RtpPort = ephemeralPort,
                RtcpPort = ephemeralPort + 1,
                AllocatedAt = DateTime.UtcNow,
                LastActivity = DateTime.UtcNow,
                IsActive = true,
                AllocationSource = "ephemeral"
            };
            _portTracking[callId] = ephemeralAllocationInfo;
            
            _logger.LogInformation($"[{callId}] Ephemeral RTP={ephemeralPort}, RTCP={ephemeralPort + 1}");
            return Task.FromResult((ephRtp, ephRtcp));
        }

        public Task ReleasePortsAsync(string callId, UdpClient rtpClient, UdpClient rtcpClient)
        {
            try
            {
                // Get the allocated port for this call
                if (_allocatedPorts.TryGetValue($"callId:{callId}", out int rtpPort))
                {
                    lock (_allocationLock)
                    {
                        // Remove from tracking
                        _allocatedPorts.TryRemove($"{rtpPort}", out _);
                        _allocatedPorts.TryRemove($"{rtpPort + 1}", out _);
                        _allocatedPorts.TryRemove($"callId:{callId}", out _);
                    }
                }

                // Remove from port tracking dictionary
                if (_portTracking.TryRemove(callId, out var allocationInfo))
                {
                    _logger.LogDebug($"[{callId}] Removed port tracking info for RTP={allocationInfo.RtpPort}, RTCP={allocationInfo.RtcpPort}");
                }

                // Close clients to free bound sockets, allowing rebind on same ports
                rtpClient?.Close();
                rtcpClient?.Close();
                _logger.LogInformation($"[{callId}] Released RTP and RTCP ports. {GetAllocationStatus()}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error releasing RTP and RTCP ports");
                throw;
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// Gets diagnostic information about currently allocated ports.
        /// </summary>
        public string GetAllocationStatus()
        {
            lock (_allocationLock)
            {
                var callIds = _allocatedPorts.Keys.Where(k => k.StartsWith("callId:")).Count();
                var totalPorts = _allocatedPorts.Keys.Where(k => !k.StartsWith("callId:")).Count();
                return $"Active calls: {callIds}, Allocated ports: {totalPorts}, Range: {_min}-{_max}";
            }
        }

        public Task<(UdpClient rtpClient, UdpClient rtcpClient)> AllocateSpecificPairAsync(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
        {
            _logger.LogInformation($"[{callId}] Trying specific RTP={rtpPort}, RTCP={rtcpPort}");
            if (TryBindPair(callId, localAddress, rtpPort, rtcpPort, out var rtp, out var rtcp))
            {
                _logger.LogInformation($"[{callId}] Bound specific RTP={rtpPort}, RTCP={rtcpPort}");
                return Task.FromResult((rtp, rtcp));
            }
            _logger.LogError($"[{callId}] Failed to bind specific ports RTP={rtpPort}, RTCP={rtcpPort}");
            throw new InvalidOperationException($"Could not bind specific ports RTP={rtpPort}, RTCP={rtcpPort}");
        }

        private bool TryBindPair(string callId, IPAddress address, int rtpPort, int rtcpPort, out UdpClient rtp, out UdpClient rtcp)
        {
            rtp = null;
            rtcp = null;
            try
            {
                rtp = new UdpClient(AddressFamily.InterNetwork);
                // 1.Enable address reuse to allow our IPv4‐bound socket to coexist with SIPSorcery’s dual-stack wildcard bind.
                // This prevents “Address already in use” errors when SIPSorcery internally binds on [::]:port.
                // 2.ExclusiveAddressUse removed; ReuseAddress used to avoid dual-bind locking ports (packets were dropped).
                // 3.ExclusiveAddressUse removed; SO_REUSEADDR used to share IPv4 ports.
                // Prevents dual-bind conflict where SIPSorcery’s wildcard bind locked the port and dropped RTP packets.
                rtp.Client.SetSocketOption(
                    SocketOptionLevel.Socket,
                    SocketOptionName.ReuseAddress,
                    true);
                rtp.Client.Bind(new IPEndPoint(address, rtpPort));

                rtcp = new UdpClient(AddressFamily.InterNetwork);
                rtcp.Client.SetSocketOption(
                    SocketOptionLevel.Socket,
                    SocketOptionName.ReuseAddress,
                    true);
                rtcp.Client.Bind(new IPEndPoint(address, rtcpPort));

                return true;
            }
            catch (SocketException ex)
            {
                _logger.LogDebug($"[{callId}] Port bind failed for {rtpPort}/{rtcpPort}: {ex.SocketErrorCode}");
                rtp?.Close();
                rtcp?.Close();
                return false;
            }
        }

        /// <summary>
        /// Periodic cleanup method called by timer to remove stale/inactive port allocations
        /// </summary>
        private void PerformPeriodicCleanup(object state)
        {
            if (_disposed)
                return;

            try
            {
                var staleThreshold = TimeSpan.FromMinutes(30); // Consider allocations stale after 30 minutes of inactivity
                var cutoffTime = DateTime.UtcNow - staleThreshold;
                var staleAllocations = new List<string>();

                // Find stale allocations
                foreach (var kvp in _portTracking)
                {
                    var allocation = kvp.Value;
                    if (allocation.LastActivity < cutoffTime || !allocation.IsActive)
                    {
                        staleAllocations.Add(kvp.Key);
                    }
                }

                // Remove stale allocations
                foreach (var callId in staleAllocations)
                {
                    if (_portTracking.TryRemove(callId, out var staleAllocation))
                    {
                        // Also remove from allocated ports tracking if it's not ephemeral
                        if (staleAllocation.AllocationSource == "regular")
                        {
                            lock (_allocationLock)
                            {
                                _allocatedPorts.TryRemove($"{staleAllocation.RtpPort}", out _);
                                _allocatedPorts.TryRemove($"{staleAllocation.RtcpPort}", out _);
                                _allocatedPorts.TryRemove($"callId:{callId}", out _);
                            }
                        }

                        _logger.LogWarning($"[{callId}] Cleaned up stale port allocation: RTP={staleAllocation.RtpPort}, RTCP={staleAllocation.RtcpPort}, " +
                                         $"Source={staleAllocation.AllocationSource}, LastActivity={staleAllocation.LastActivity:yyyy-MM-dd HH:mm:ss}");
                    }
                }

                if (staleAllocations.Count > 0)
                {
                    _logger.LogInformation($"Periodic cleanup completed. Removed {staleAllocations.Count} stale port allocations. {GetAllocationStatus()}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during periodic port allocation cleanup");
            }
        }

        /// <summary>
        /// Updates the last activity timestamp for a call's port allocation
        /// </summary>
        public void UpdatePortActivity(string callId)
        {
            if (_portTracking.TryGetValue(callId, out var allocation))
            {
                allocation.LastActivity = DateTime.UtcNow;
                _logger.LogDebug($"[{callId}] Updated port activity timestamp");
            }
        }

        /// <summary>
        /// Marks a port allocation as inactive
        /// </summary>
        public void MarkPortsInactive(string callId)
        {
            if (_portTracking.TryGetValue(callId, out var allocation))
            {
                allocation.IsActive = false;
                _logger.LogDebug($"[{callId}] Marked ports as inactive");
            }
        }

        /// <summary>
        /// Gets detailed allocation information for monitoring
        /// </summary>
        public Dictionary<string, object> GetDetailedAllocationInfo()
        {
            var info = new Dictionary<string, object>();
            var allocations = new List<object>();

            foreach (var kvp in _portTracking)
            {
                var allocation = kvp.Value;
                allocations.Add(new
                {
                    CallId = allocation.CallId,
                    RtpPort = allocation.RtpPort,
                    RtcpPort = allocation.RtcpPort,
                    AllocatedAt = allocation.AllocatedAt,
                    LastActivity = allocation.LastActivity,
                    IsActive = allocation.IsActive,
                    AllocationSource = allocation.AllocationSource,
                    AgeMinutes = (DateTime.UtcNow - allocation.AllocatedAt).TotalMinutes
                });
            }

            info["TotalAllocations"] = _portTracking.Count;
            info["ActiveAllocations"] = _portTracking.Values.Count(a => a.IsActive);
            info["RegularAllocations"] = _portTracking.Values.Count(a => a.AllocationSource == "regular");
            info["EphemeralAllocations"] = _portTracking.Values.Count(a => a.AllocationSource == "ephemeral");
            info["PortRange"] = $"{_min}-{_max}";
            info["Allocations"] = allocations;

            return info;
        }

        /// <summary>
        /// Dispose method to clean up resources and implement IDisposable
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method for proper disposal pattern
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // Dispose the cleanup timer
                    _cleanupTimer?.Dispose();

                    // Log cleanup of remaining allocations
                    var remainingAllocations = _portTracking.Count;
                    if (remainingAllocations > 0)
                    {
                        _logger.LogWarning($"Disposing PortAllocator with {remainingAllocations} remaining port allocations");
                        
                        // Clear all tracking dictionaries
                        _portTracking.Clear();
                        _allocatedPorts.Clear();
                    }

                    _logger.LogInformation("PortAllocator disposed successfully");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during PortAllocator disposal");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// Finalizer to ensure cleanup if Dispose is not called
        /// </summary>
        ~PortAllocator()
        {
            Dispose(false);
        }
    }
}