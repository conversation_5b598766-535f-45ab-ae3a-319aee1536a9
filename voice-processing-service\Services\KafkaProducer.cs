using System;
using System.Threading;
using System.Threading.Tasks;
using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Třída pro odesílání zpráv do Kafka brokeru.
    /// </summary>
    public class KafkaProducer : IDisposable
    {
        private readonly IProducer<string, string> _producer;
        private readonly string _topicName;
        private readonly ILogger<KafkaProducer> _logger;
        private bool _isDisposed = false;

        /// <summary>
        /// Vytvoří novou instanci KafkaProducer.
        /// </summary>
        /// <param name="options">Konfigurace Kafka brokeru.</param>
        /// <param name="logger">Logger.</param>
        public KafkaProducer(IOptions<KafkaOptions> options, ILogger<KafkaProducer> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            var config = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _topicName = config.TopicName ?? throw new ArgumentException("TopicName must be specified", nameof(options));

            var producerConfig = new ProducerConfig
            {
                BootstrapServers = config.BootstrapServers,
                Acks = Acks.All
            };

            _producer = new ProducerBuilder<string, string>(producerConfig).Build();
            _logger.LogInformation($"KafkaProducer created for topic {_topicName}");
        }

        /// <summary>
        /// Odešle zprávu do Kafka brokeru.
        /// </summary>
        /// <param name="key">Klíč zprávy.</param>
        /// <param name="value">Hodnota zprávy.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task ProduceAsync(string key, string value, CancellationToken cancellationToken)
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(nameof(KafkaProducer));
            }

            try
            {
                var message = new Message<string, string>
                {
                    Key = key,
                    Value = value
                };

                var deliveryResult = await _producer.ProduceAsync(_topicName, message, cancellationToken);
                _logger.LogDebug($"Message delivered to {deliveryResult.TopicPartitionOffset}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error producing message to Kafka topic {_topicName}");
                throw;
            }
        }

        /// <summary>
        /// Uvolní prostředky.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
            {
                return;
            }

            _logger.LogInformation("Disposing KafkaProducer.");
            _producer?.Dispose();
            _isDisposed = true;
            GC.SuppressFinalize(this);
        }
    }
}
