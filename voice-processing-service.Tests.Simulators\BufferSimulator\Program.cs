using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;

namespace voice_processing_service.Tests.Simulators.BufferSimulator
{
    class Program
    {
        static async Task Main(string[] args)
        {
            // Vytvoření loggeru
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Trace);
            });
            ILogger<BlockingCollectionAudioBuffer> logger = loggerFactory.CreateLogger<BlockingCollectionAudioBuffer>();
            
            // Vytvoření bufferu
            using var buffer = new BlockingCollectionAudioBuffer(logger);

            // Vytvoření CancellationTokenSource pro ukončení simulace
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10)); // Simulace bude běžet 10 sekund

            // Spuštění producenta a konzumenta
            Task producer = ProducerAsync(buffer, cts.Token);
            Task consumer = ConsumerAsync(buffer, cts.Token);

            // Čekání na ukončení simulace
            try
            {
                await Task.WhenAll(producer, consumer);
                Console.WriteLine("Simulace úspěšně dokončena.");
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Simulace byla zrušena.");
            }
        }

        static async Task ProducerAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            try
            {
                var random = new Random();
                while (!cancellationToken.IsCancellationRequested)
                {
                    // Vytvoření náhodných audio dat
                    byte[] audioData = new byte[320]; // 20ms G.711 při 8kHz
                    random.NextBytes(audioData);

                    // Přidání dat do bufferu
                    buffer.Add(audioData);
                    Console.WriteLine($"Producent: Přidáno {audioData.Length} bytů do bufferu.");

                    // Simulace prodlevy mezi pakety (20ms)
                    await Task.Delay(20, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Producent: Zrušen.");
            }
            finally
            {
                buffer.CompleteAdding();
                Console.WriteLine("Producent: Ukončeno přidávání do bufferu.");
            }
        }

        static async Task ConsumerAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            try
            {
                long totalBytesProcessed = 0;
                while (!buffer.IsCompleted || buffer.TryTake(out _ , 0, CancellationToken.None))
                {
                    // Pokus o získání dat z bufferu
                    if (buffer.TryTake(out var audioData, 100, cancellationToken))
                    {
                        // Zpracování dat
                        totalBytesProcessed += audioData.Length;
                        Console.WriteLine($"Konzument: Zpracováno {audioData.Length} bytů. Celkem: {totalBytesProcessed} bytů.");

                        // Simulace zpracování dat (může být rychlejší nebo pomalejší než producent)
                        await Task.Delay(new Random().Next(10, 30), cancellationToken);
                    }
                }
                Console.WriteLine($"Konzument: Buffer je prázdný a přidávání ukončeno. Celkem zpracováno {totalBytesProcessed} bytů.");
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Konzument: Zrušen.");
            }
        }
    }
}