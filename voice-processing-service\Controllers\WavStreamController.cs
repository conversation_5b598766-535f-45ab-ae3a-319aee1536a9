using Microsoft.AspNetCore.Mvc;
using voice_processing_service.Services;

namespace voice_processing_service.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class WavStreamController : ControllerBase
    {
        private readonly WavFileStreamer3 _wavFileStreamer3;

        public WavStreamController(WavFileStreamer3 wavFileStreamer3)
        {
            _wavFileStreamer3 = wavFileStreamer3;
        }        [HttpGet("health")]
        public IActionResult Health()
        {
            Console.WriteLine("Health check endpoint called");
            return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
        }

        [HttpPost("stream-v3")]
        public async Task<IActionResult> StreamWavFileV3([FromBody] StreamWavRequest request)
        {
            try
            {
                var result = await _wavFileStreamer3.StreamWavFileAsync(request.FilePath);
                
                return Ok(new { success = result, message = result ? "WAV file streaming (v3) completed" : "WAV file streaming (v3) failed" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        [HttpPost("stream-sage-v3")]
        public async Task<IActionResult> StreamSageWavFileV3()
        {
            Console.WriteLine("StreamSageWavFileV3 endpoint called");
            try
            {
                // Use the sage.wav file from the voice-processing-simulator folder
                var sagePath = @"c:\Projects\o2\callc-ccczbuddyrealtimephonexiaserver\voice-processing-simulator\sage.wav";
                Console.WriteLine($"Attempting to stream file (v3): {sagePath}");
                
                var result = await _wavFileStreamer3.StreamWavFileAsync(sagePath);
                
                Console.WriteLine($"✅ Streaming result (v3): {result}");
                return Ok(new { success = result, message = result ? "Sage WAV file streaming (v3) completed" : "Sage WAV file streaming (v3) failed" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error in StreamSageWavFileV3: {ex.Message}");
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }
    }

    public class StreamWavRequest
    {
        public string FilePath { get; set; } = "";
    }
}
