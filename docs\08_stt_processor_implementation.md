# Implementace STT procesoru

## Popis úkolu

Tento úkol zahrnuje implementaci STT (Speech-to-Text) procesoru, k<PERSON><PERSON> bude zpracovávat audio data z bufferu a odesílat je do STT služby Phonexia. STT procesor bude implementovat rozhraní `IAudioProcessor` a bude sloužit jako alternativa k `WavAudioProcessor`. Hlavní funkcionalita zahrnuje:

1. Příjem audio dat z bufferu
2. Zpracování audio dat a jejich odeslání do STT služby
3. Zpracování výsledků STT a jejich ukládání
4. Poskytování rozhraní pro přístup k výsledkům STT

## Technické detaily

### Implementace SttAudioProcessor

Třída `SttAudioProcessor` implementuje rozhraní `IAudioProcessor` a je zodpovědná za zpracování audio dat a jejich odeslání do STT služby.

```csharp
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Models;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace procesoru pro zpracování audio dat a jejich odeslání do STT služby.
    /// </summary>
    public class SttAudioProcessor : IAudioProcessor
    {
        private readonly ILogger<SttAudioProcessor> _logger;
        private readonly string _callId;
        private readonly PhonexiaOptions _options;
        private readonly HttpClient _httpClient;
        private readonly List<SttResult> _sttResults = new List<SttResult>();
        private readonly MemoryStream _audioBuffer = new MemoryStream();
        private readonly object _resultsLock = new object();
        private string _sessionId;
        private bool _isDisposed = false;

        /// <summary>
        /// Identifikátor procesoru.
        /// </summary>
        public string ProcessorId => $"STT_{_callId}";

        /// <summary>
        /// Vytvoří novou instanci SttAudioProcessor.
        /// </summary>
        /// <param name="callId">ID hovoru.</param>
        /// <param name="options">Konfigurace Phonexia služby.</param>
        /// <param name="httpClient">HTTP klient pro komunikaci s Phonexia API.</param>
        /// <param name="logger">Logger.</param>
        public SttAudioProcessor(
            string callId,
            IOptions<PhonexiaOptions> options,
            HttpClient httpClient,
            ILogger<SttAudioProcessor> logger)
        {
            _callId = callId ?? throw new ArgumentNullException(nameof(callId));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _logger.LogInformation($"[{_callId}] SttAudioProcessor created.");
        }

        /// <summary>
        /// Spustí zpracování audio dat z bufferu.
        /// </summary>
        /// <param name="buffer">Buffer s audio daty.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{_callId}] Starting STT processing from buffer.");

            try
            {
                // Vytvoření STT session
                _sessionId = await CreateSessionAsync(cancellationToken);
                _logger.LogInformation($"[{_callId}] Created STT session with ID: {_sessionId}");

                // Zpracování audio dat z bufferu
                while (!buffer.IsCompleted || buffer.TryTake(out _, 0, CancellationToken.None))
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        _logger.LogInformation($"[{_callId}] STT processing cancellation requested.");
                        break;
                    }

                    // Blokující čekání s timeoutem a cancellation tokenem
                    if (buffer.TryTake(out byte[] audioData, 500, cancellationToken))
                    {
                        await ProcessAudioDataAsync(audioData, cancellationToken);
                    }
                }

                // Finalizace STT session
                await FinalizeSessionAsync(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] STT processing cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during STT processing.");
            }
            finally
            {
                _logger.LogInformation($"[{_callId}] STT processing stopped.");
            }
        }

        /// <summary>
        /// Vytvoří novou STT session.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>ID vytvořené session.</returns>
        private async Task<string> CreateSessionAsync(CancellationToken cancellationToken)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/sessions");
                request.Headers.Add("X-API-Key", _options.ApiKey);

                var content = new
                {
                    config = new
                    {
                        language = _options.Language,
                        model = _options.Model,
                        audio_format = new
                        {
                            encoding = "MULAW",
                            sample_rate = 8000,
                            channels = 1
                        }
                    }
                };

                request.Content = new StringContent(
                    JsonSerializer.Serialize(content),
                    Encoding.UTF8,
                    "application/json");

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var sessionResponse = JsonSerializer.Deserialize<PhonexiaSessionResponse>(responseContent);

                return sessionResponse.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to create STT session.");
                throw;
            }
        }

        /// <summary>
        /// Zpracuje audio data a odešle je do STT služby.
        /// </summary>
        /// <param name="audioData">Audio data k zpracování.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task ProcessAudioDataAsync(byte[] audioData, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(_sessionId) || audioData == null || audioData.Length == 0)
            {
                return;
            }

            try
            {
                // Přidání audio dat do bufferu
                _audioBuffer.Write(audioData, 0, audioData.Length);

                // Pokud máme dostatek dat, odešleme je do STT služby
                if (_audioBuffer.Length >= _options.ChunkSizeBytes)
                {
                    await SendAudioChunkAsync(cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error processing audio data.");
            }
        }

        /// <summary>
        /// Odešle chunk audio dat do STT služby.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task SendAudioChunkAsync(CancellationToken cancellationToken)
        {
            try
            {
                // Získání dat z bufferu
                byte[] audioChunk = _audioBuffer.ToArray();
                _audioBuffer.SetLength(0); // Reset bufferu

                // Odeslání dat do STT služby
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/sessions/{_sessionId}/data");
                request.Headers.Add("X-API-Key", _options.ApiKey);
                request.Content = new ByteArrayContent(audioChunk);
                request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                // Získání výsledků
                await GetResultsAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to send audio chunk to STT service.");
            }
        }

        /// <summary>
        /// Získá výsledky STT z aktuální session.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task GetResultsAsync(CancellationToken cancellationToken)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_options.ApiUrl}/sessions/{_sessionId}/result");
                request.Headers.Add("X-API-Key", _options.ApiKey);

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var resultResponse = JsonSerializer.Deserialize<PhonexiaResultResponse>(responseContent);

                if (resultResponse?.Result?.Transcripts != null)
                {
                    lock (_resultsLock)
                    {
                        foreach (var transcript in resultResponse.Result.Transcripts)
                        {
                            var sttResult = new SttResult
                            {
                                Text = transcript.Text,
                                StartTime = TimeSpan.FromSeconds(transcript.Start),
                                EndTime = TimeSpan.FromSeconds(transcript.End),
                                Confidence = transcript.Confidence,
                                IsFinal = transcript.IsFinal
                            };

                            _sttResults.Add(sttResult);
                            _logger.LogInformation($"[{_callId}] New STT result: {sttResult.Text} ({sttResult.StartTime} - {sttResult.EndTime})");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to get STT results.");
            }
        }

        /// <summary>
        /// Finalizuje STT session.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task FinalizeSessionAsync(CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(_sessionId))
            {
                return;
            }

            try
            {
                // Odeslání zbývajících dat
                if (_audioBuffer.Length > 0)
                {
                    await SendAudioChunkAsync(cancellationToken);
                }

                // Ukončení session
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/sessions/{_sessionId}/finalize");
                request.Headers.Add("X-API-Key", _options.ApiKey);

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                // Získání finálních výsledků
                await GetResultsAsync(cancellationToken);

                _logger.LogInformation($"[{_callId}] STT session finalized. Total results: {_sttResults.Count}");

                // Uložení výsledků do souboru
                await SaveResultsToFileAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to finalize STT session.");
            }
        }

        /// <summary>
        /// Uloží výsledky STT do souboru.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task SaveResultsToFileAsync(CancellationToken cancellationToken)
        {
            try
            {
                string resultsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SttResults");
                Directory.CreateDirectory(resultsDirectory);

                string filePath = Path.Combine(resultsDirectory, $"{_callId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json");

                using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None))
                using (var writer = new StreamWriter(fileStream, Encoding.UTF8))
                {
                    var json = JsonSerializer.Serialize(new
                    {
                        callId = _callId,
                        sessionId = _sessionId,
                        timestamp = DateTime.UtcNow,
                        results = _sttResults
                    }, new JsonSerializerOptions { WriteIndented = true });

                    await writer.WriteAsync(json);
                }

                _logger.LogInformation($"[{_callId}] STT results saved to file: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to save STT results to file.");
            }
        }

        /// <summary>
        /// Získá všechny výsledky STT.
        /// </summary>
        /// <returns>Seznam výsledků STT.</returns>
        public IReadOnlyList<SttResult> GetResults()
        {
            lock (_resultsLock)
            {
                return _sttResults.AsReadOnly();
            }
        }

        /// <summary>
        /// Uvolní prostředky.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
            {
                return;
            }

            _logger.LogInformation($"[{_callId}] Disposing SttAudioProcessor.");

            try
            {
                // Finalizace session, pokud ještě nebyla finalizována
                if (!string.IsNullOrEmpty(_sessionId))
                {
                    _ = FinalizeSessionAsync(CancellationToken.None).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during SttAudioProcessor disposal.");
            }

            _audioBuffer.Dispose();
            _isDisposed = true;
            GC.SuppressFinalize(this);
        }
    }
}
```

### Implementace modelů pro Phonexia API

Pro práci s Phonexia API budou implementovány následující modely:

```csharp
using System.Text.Json.Serialization;

namespace voice_processing_service.Models
{
    /// <summary>
    /// Model pro odpověď z Phonexia API při vytvoření session.
    /// </summary>
    public class PhonexiaSessionResponse
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    /// <summary>
    /// Model pro odpověď z Phonexia API s výsledky STT.
    /// </summary>
    public class PhonexiaResultResponse
    {
        [JsonPropertyName("result")]
        public PhonexiaResult Result { get; set; }
    }

    /// <summary>
    /// Model pro výsledek STT z Phonexia API.
    /// </summary>
    public class PhonexiaResult
    {
        [JsonPropertyName("transcripts")]
        public PhonexiaTranscript[] Transcripts { get; set; }
    }

    /// <summary>
    /// Model pro transkript z Phonexia API.
    /// </summary>
    public class PhonexiaTranscript
    {
        [JsonPropertyName("text")]
        public string Text { get; set; }

        [JsonPropertyName("start")]
        public double Start { get; set; }

        [JsonPropertyName("end")]
        public double End { get; set; }

        [JsonPropertyName("confidence")]
        public double Confidence { get; set; }

        [JsonPropertyName("is_final")]
        public bool IsFinal { get; set; }
    }

    /// <summary>
    /// Model pro výsledek STT.
    /// </summary>
    public class SttResult
    {
        /// <summary>
        /// Text transkriptu.
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// Čas začátku transkriptu.
        /// </summary>
        public TimeSpan StartTime { get; set; }

        /// <summary>
        /// Čas konce transkriptu.
        /// </summary>
        public TimeSpan EndTime { get; set; }

        /// <summary>
        /// Míra jistoty (0-1).
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// Indikuje, zda je transkript finální.
        /// </summary>
        public bool IsFinal { get; set; }
    }
}
```

### Implementace konfigurace pro Phonexia API

Pro konfiguraci Phonexia API bude implementována následující třída:

```csharp
namespace voice_processing_service.Configuration
{
    /// <summary>
    /// Konfigurace pro Phonexia API.
    /// </summary>
    public class PhonexiaOptions
    {
        /// <summary>
        /// URL Phonexia API.
        /// </summary>
        public string ApiUrl { get; set; } = "https://api.phonexia.com/v1";

        /// <summary>
        /// API klíč pro Phonexia API.
        /// </summary>
        public string ApiKey { get; set; }

        /// <summary>
        /// Jazyk pro STT.
        /// </summary>
        public string Language { get; set; } = "cs-CZ";

        /// <summary>
        /// Model pro STT.
        /// </summary>
        public string Model { get; set; } = "default";

        /// <summary>
        /// Velikost chunku audio dat v bytech.
        /// </summary>
        public int ChunkSizeBytes { get; set; } = 8000; // 1 sekunda při 8kHz
    }
}
```

### Rozšíření Program.cs

Program.cs bude rozšířen o registraci SttAudioProcessor:

```csharp
// Registrace konfigurace pro Phonexia API
builder.Services.Configure<PhonexiaOptions>(builder.Configuration.GetSection("Phonexia"));

// Registrace HttpClient pro Phonexia API
builder.Services.AddHttpClient();

// Registrace továrny pro SttAudioProcessor
builder.Services.AddTransient<Func<string, IAudioProcessor>>(sp =>
{
    return (callId) =>
    {
        var logger = sp.GetRequiredService<ILogger<SttAudioProcessor>>();
        var options = sp.GetRequiredService<IOptions<PhonexiaOptions>>();
        var httpClient = sp.GetRequiredService<HttpClient>();
        return new SttAudioProcessor(callId, options, httpClient, logger);
    };
});
```

### Rozšíření appsettings.json

Soubor appsettings.json bude rozšířen o konfiguraci pro Phonexia API:

```json
{
  "Phonexia": {
    "ApiUrl": "https://api.phonexia.com/v1",
    "ApiKey": "your-api-key-here",
    "Language": "cs-CZ",
    "Model": "default",
    "ChunkSizeBytes": 8000
  }
}
```

## Testovací scénáře

### Unit testy pro SttAudioProcessor

1. **Test konstruktoru**
   - Ověřit, že instance SttAudioProcessor je vytvořena bez chyb
   - Ověřit, že všechny závislosti jsou správně nastaveny

2. **Test metody StartProcessingAsync**
   - Ověřit, že metoda StartProcessingAsync vytvoří STT session
   - Ověřit, že metoda StartProcessingAsync zpracuje audio data z bufferu
   - Ověřit, že metoda StartProcessingAsync finalizuje STT session

3. **Test metody ProcessAudioDataAsync**
   - Ověřit, že metoda ProcessAudioDataAsync přidá audio data do bufferu
   - Ověřit, že metoda ProcessAudioDataAsync odešle data do STT služby, když je buffer plný

4. **Test metody SendAudioChunkAsync**
   - Ověřit, že metoda SendAudioChunkAsync odešle audio data do STT služby
   - Ověřit, že metoda SendAudioChunkAsync získá výsledky STT

5. **Test metody GetResultsAsync**
   - Ověřit, že metoda GetResultsAsync získá výsledky STT z API
   - Ověřit, že metoda GetResultsAsync zpracuje výsledky STT

6. **Test metody FinalizeSessionAsync**
   - Ověřit, že metoda FinalizeSessionAsync odešle zbývající data
   - Ověřit, že metoda FinalizeSessionAsync ukončí STT session
   - Ověřit, že metoda FinalizeSessionAsync získá finální výsledky STT
   - Ověřit, že metoda FinalizeSessionAsync uloží výsledky do souboru

7. **Test metody GetResults**
   - Ověřit, že metoda GetResults vrátí seznam výsledků STT

### Integrační testy pro SttAudioProcessor

1. **Test zpracování audio dat**
   - Vytvořit instanci SttAudioProcessor
   - Simulovat příjem audio dat z bufferu
   - Ověřit, že audio data jsou zpracována a odeslána do STT služby
   - Ověřit, že výsledky STT jsou získány a uloženy

2. **Test ukončení zpracování**
   - Vytvořit instanci SttAudioProcessor
   - Simulovat příjem audio dat z bufferu
   - Simulovat ukončení zpracování
   - Ověřit, že STT session je finalizována
   - Ověřit, že výsledky STT jsou uloženy do souboru

## Implementační kroky

1. Implementovat modely pro Phonexia API
2. Implementovat konfiguraci pro Phonexia API
3. Implementovat třídu SttAudioProcessor
4. Rozšířit Program.cs o registraci SttAudioProcessor
5. Rozšířit appsettings.json o konfiguraci pro Phonexia API
6. Implementovat unit testy pro SttAudioProcessor
7. Implementovat integrační testy pro SttAudioProcessor
8. Otestovat SttAudioProcessor s reálnou Phonexia API

## Použití SttAudioProcessor

SttAudioProcessor lze použít jako alternativu k WavAudioProcessor v rámci CallSession. Pro použití SttAudioProcessor je potřeba upravit tovární metodu v SipServerService:

```csharp
Func<IAudioProcessor> processorFactory = () =>
{
    // Použití SttAudioProcessor místo WavAudioProcessor
    return _sttProcessorFactory(callId);
};
```

Výsledky STT lze získat z SttAudioProcessor pomocí metody GetResults:

```csharp
var sttProcessor = (SttAudioProcessor)session.AudioProcessor;
var results = sttProcessor.GetResults();

foreach (var result in results)
{
    Console.WriteLine($"Text: {result.Text}, Time: {result.StartTime} - {result.EndTime}, Confidence: {result.Confidence}");
}
```

Výsledky STT jsou také automaticky ukládány do souboru ve formátu JSON v adresáři SttResults.
