using System;
using System.Collections.Concurrent;
using System.Threading;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace audio bufferu pomocí BlockingCollection.
    /// </summary>
    public class BlockingCollectionAudioBuffer : IAudioBuffer
    {
        private readonly BlockingCollection<byte[]> _buffer = new BlockingCollection<byte[]>(new ConcurrentQueue<byte[]>(), 50);
        private readonly ILogger<BlockingCollectionAudioBuffer> _logger;
        private readonly string _instanceId = Guid.NewGuid().ToString("N").Substring(0, 6); // Pro logování

        /// <summary>
        /// Inicializuje novou instanci třídy <see cref="BlockingCollectionAudioBuffer"/>.
        /// </summary>
        /// <param name="logger">Logger pro logování událostí.</param>
        public BlockingCollectionAudioBuffer(ILogger<BlockingCollectionAudioBuffer> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _logger.LogDebug($"[{_instanceId}] Buffer created.");
        }

        /// <inheritdoc/>
        public void Add(byte[] audioData)
        {
            if (!_buffer.IsAddingCompleted)
            {
                try
                {
                    _buffer.Add(audioData);
                    _logger.LogTrace($"[{_instanceId}] Added {audioData.Length} bytes to buffer. Current count: {_buffer.Count}");
                }
                catch (InvalidOperationException)
                {
                    // Může nastat, pokud se zavolá CompleteAdding a pak Add
                    _logger.LogWarning($"[{_instanceId}] Attempted to add data after CompleteAdding was called.");
                }
            }
        }

        /// <inheritdoc/>
        public byte[] Take(CancellationToken cancellationToken)
        {
            var audioData = _buffer.Take(cancellationToken);
            _logger.LogTrace($"[{_instanceId}] Took {audioData.Length} bytes from buffer (cancellable). Remaining count: {_buffer.Count}");
            return audioData;
        }
        /// <inheritdoc/>
        public bool TryTake(out byte[] audioData, int millisecondsTimeout, CancellationToken cancellationToken)
        {
            bool success = _buffer.TryTake(out audioData, millisecondsTimeout, cancellationToken);
            if (success && audioData != null)
            {
                _logger.LogDebug($"[{_instanceId}] Successfully took {audioData.Length} bytes from buffer. Remaining count: {_buffer.Count}");
            }
            else if (millisecondsTimeout > 0)
            {
                //_logger.LogDebug($"[{_instanceId}] Failed to take data from buffer after waiting {millisecondsTimeout}ms. Buffer count: {_buffer.Count}, IsCompleted: {IsCompleted}");
            }
            return success;
        }

        /// <inheritdoc/>
        public void CompleteAdding()
        {
            try
            {
                if (!_buffer.IsAddingCompleted)
                {
                    _logger.LogInformation($"[{_instanceId}] Completing adding to buffer.");
                    _buffer.CompleteAdding();
                }
            }
            catch (ObjectDisposedException ex)
            {
                _logger.LogWarning(ex, $"[{_instanceId}] Attempted to complete adding after buffer was disposed.");
            }
        }

        /// <inheritdoc/>
        public bool IsAddingCompleted => _buffer.IsAddingCompleted;

        /// <inheritdoc/>
        public bool IsCompleted => _buffer.IsCompleted;

        /// <inheritdoc/>
        public void Dispose()
        {
            _logger.LogDebug($"[{_instanceId}] Disposing buffer.");
            CompleteAdding(); // Zajistí, že konzumenti přestanou čekat
            _buffer.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
