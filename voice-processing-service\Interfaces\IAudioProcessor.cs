using System;
using System.Threading;
using System.Threading.Tasks;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro procesor audio dat (např. WAV, STT).
    /// </summary>
    public interface IAudioProcessor : IDisposable
    {
        /// <summary>
        /// Spustí zpracování audio dat z bufferu.
        /// </summary>
        /// <param name="buffer">Buffer obsahující audio data ke zpracování.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken);
        Task InitializeAsync(CancellationToken cancellationToken)
            => Task.CompletedTask;

        /// <summary>
        /// Identifikátor procesoru pro logování a rozlišení.
        /// </summary>
        string ProcessorId { get; }
    }
}

