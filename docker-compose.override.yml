# Override configuration for local development
version: '3.8'

services:
  voice-processing-service:
    environment:
      # Development environment
      - ASPNETCORE_ENVIRONMENT=Development
      
      # Enable more detailed logging for development
      - Logging__LogLevel__Default=Debug
      - Logging__LogLevel__voice_processing_service=Debug
      # Fix Docker networking for Phonexia API access
      - Phonexia__ApiUrl=http://host.docker.internal:8600
      
      # Use local Phonexia container (standard ASP.NET Core format)
      # ApiHost and ApiPort are now derived from ApiUrl
    
    # Mount source code for development (optional)
    volumes:
      - voice-recordings:/app/recordings
      # Uncomment for development with hot reload
      # - ./voice-processing-service:/app/src:ro
    
    # Expose additional ports for debugging
    ports:
      - "5090:5090/udp"
      - "10000-10100:10000-10100/udp"
      - "8081:8081"
      # Uncomment for debugging
      # - "5000:5000"

  phonexia:
    # Use development image or specific version
    # image: phonexia/speech-platform:dev
    environment:
      - PHONEXIA_LICENSE_KEY=${PHONEXIA_LICENSE_KEY:-}
      - PHONEXIA_USERNAME=admin
      - PHONEXIA_PASSWORD=phonexia
      - PHONEXIA_LOG_LEVEL=DEBUG
    
    # Mount configuration for development
    volumes:
      - phonexia-data:/opt/phonexia/data
      - phonexia-models:/opt/phonexia/models
      # Uncomment to mount custom configuration
      # - ./phonexia-config:/opt/phonexia/config:ro
