using System;
using System.Reflection;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class WavAudioProcessorFactoryTests
    {
        [Fact]
        public void Constructor_ShouldInitializeFactory()
        {
            var options = Options.Create(new WavRecordingOptions { Enabled = true, Directory = "testdir" });
            var factory = new WavAudioProcessorFactory(new NullLoggerFactory(), options);
            Assert.NotNull(factory);
        }

        [Fact]
        public void CreateProcessor_ShouldReturnWavAudioProcessorAndCorrectProcessorId()
        {
            var directory = "testdir";
            var options = Options.Create(new WavRecordingOptions { Enabled = true, Directory = directory, FilenameTemplate = "files/{callId}.wav" });
            var factory = new WavAudioProcessorFactory(new NullLoggerFactory(), options);
            var processor = factory.CreateProcessor("testcall");
            
            Assert.NotNull(processor);
            Assert.IsType<WavAudioProcessor>(processor);

            // ProcessorId is "WAV_{filenameWithoutExtension}"
            var pid = processor.ProcessorId;
            Assert.Equal("WAV_testcall", pid);
        }

        [Fact]
        public void Template_Rendering_Across_SegmentIndices()
        {
            var template = "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav";
            var callId = "testcall";
            var utcStart = new DateTime(2025, 8, 9, 12, 0, 0, DateTimeKind.Utc);

            // Use reflection to invoke private static RenderTemplate
            var mi = typeof(WavAudioProcessorFactory).GetMethod("RenderTemplate", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.NotNull(mi);

            string seg1 = (string)mi.Invoke(null, new object[] { template, callId, utcStart, 1 })!;
            string seg2 = (string)mi.Invoke(null, new object[] { template, callId, utcStart, 2 })!;

            Assert.EndsWith("_call-testcall_seg-001.wav", seg1);
            Assert.EndsWith("_call-testcall_seg-002.wav", seg2);

            // Also verify utcStart/date tokens
            Assert.StartsWith("20250809/", seg1);
            Assert.Contains("20250809T120000Z", seg1);
        }
[Fact]
        public void RendersFilenameTemplateTokens()
        {
            var directory = System.IO.Path.Combine(System.IO.Path.GetTempPath(), Guid.NewGuid().ToString("N"));
            var template = "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav";
            var utcStart = new DateTime(2025, 8, 9, 12, 0, 0, DateTimeKind.Utc);
            var callId = "testcall";
            var segmentIndex = 1;

            // Invoke private RenderTemplate(string, string, DateTime, int)
            var mi = typeof(WavAudioProcessorFactory).GetMethod("RenderTemplate", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.NotNull(mi);

            var relative = (string)mi!.Invoke(null, new object[] { template, callId, utcStart, segmentIndex })!;
            // Combine with base directory (normalize separators)
            var fullPath = System.IO.Path.Combine(directory, relative.TrimStart(System.IO.Path.DirectorySeparatorChar, System.IO.Path.AltDirectorySeparatorChar));

            var expectedDir = System.IO.Path.Combine(directory, "20250809");
            var actualDir = System.IO.Path.GetDirectoryName(fullPath);
            Assert.Equal(expectedDir, actualDir);

            var fileName = System.IO.Path.GetFileName(fullPath);
            Assert.Equal("20250809T120000Z_call-testcall_seg-001.wav", fileName);
        }

        [Fact]
        public async System.Threading.Tasks.Task FallsBackOnInvalidTemplate()
        {
            // Treat whitespace template as invalid -> factory should fall back to legacy single-path behavior
            var tempRoot = System.IO.Path.Combine(System.IO.Path.GetTempPath(), $"wav_ut_{Guid.NewGuid():N}");
            System.IO.Directory.CreateDirectory(tempRoot);

            try
            {
                var opts = Options.Create(new WavRecordingOptions
                {
                    Enabled = true,
                    Directory = tempRoot,
                    FilenameTemplate = "   " // invalid/blank -> triggers fallback
                });

                var factory = new WavAudioProcessorFactory(new NullLoggerFactory(), opts);
                using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
                var processor = factory.CreateProcessor("testcall");

                var procTask = System.Threading.Tasks.Task.Run(() => processor.StartProcessingAsync(buffer, System.Threading.CancellationToken.None));

                var sample = new byte[] { 1, 2, 3, 4, 5 };
                buffer.Add(sample);
                buffer.CompleteAdding();

                await procTask;
                processor.Dispose();

                // Assert: exactly one file created in tempRoot with legacy name, and it doesn't contain unknown token text
                var files = System.IO.Directory.GetFiles(tempRoot, "*.wav", System.IO.SearchOption.AllDirectories);
                Assert.Single(files);
                var name = System.IO.Path.GetFileName(files[0]);
                Assert.False(string.IsNullOrWhiteSpace(name));
                Assert.DoesNotContain("{unknown}", name);
                Assert.StartsWith("call_testcall_", name);
                Assert.True(name.EndsWith(".wav", StringComparison.OrdinalIgnoreCase));
            }
            finally
            {
                if (System.IO.Directory.Exists(tempRoot))
                {
                    try { System.IO.Directory.Delete(tempRoot, true); } catch { /* ignore */ }
                }
            }
        }
    }
}