using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Thread-safe synchronization service for per-Call-ID operations.
    /// Prevents race conditions when multiple INVITE/re-INVITE requests arrive for the same Call-ID.
    /// </summary>
    public class CallSynchronizationService : ICallSynchronizationService, IDisposable
    {
        private readonly ILogger<CallSynchronizationService> _logger;
        private readonly ConcurrentDictionary<string, CallLockEntry> _locks = new();
        private readonly TimeSpan _defaultTimeout = TimeSpan.FromSeconds(30);
        private readonly Timer _cleanupTimer;
        private readonly object _cleanupLock = new();
        private bool _disposed = false;

        /// <summary>
        /// Represents a lock entry for a specific Call-ID.
        /// </summary>
        private class CallLockEntry
        {
            public SemaphoreSlim Semaphore { get; }
            public int ReferenceCount;
            public DateTime LastAccessTime;

            public CallLockEntry()
            {
                Semaphore = new SemaphoreSlim(1, 1); // Binary semaphore (mutex-like behavior)
                ReferenceCount = 0;
                LastAccessTime = DateTime.UtcNow;
            }

            public void Dispose()
            {
                Semaphore?.Dispose();
            }
        }

        public CallSynchronizationService(ILogger<CallSynchronizationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Start cleanup timer to remove unused locks every 5 minutes
            _cleanupTimer = new Timer(CleanupUnusedLocks, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
            
            _logger.LogInformation("CallSynchronizationService initialized");
        }

        /// <summary>
        /// Gets the current number of active locks (for monitoring/debugging).
        /// </summary>
        public int ActiveLockCount => _locks.Count;

        /// <summary>
        /// Acquires a lock for the specified Call-ID and executes the provided function.
        /// </summary>
        public async Task<T> ExecuteWithLockAsync<T>(
            string callId,
            Func<Task<T>> function,
            TimeSpan? timeout = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(callId))
                throw new ArgumentException("Call-ID cannot be null or empty", nameof(callId));
            if (function == null)
                throw new ArgumentNullException(nameof(function));

            var lockTimeout = timeout ?? _defaultTimeout;
            var lockEntry = GetOrCreateLockEntry(callId);

            var waitStopwatch = System.Diagnostics.Stopwatch.StartNew();
            _logger.LogDebug($"[{callId}] [LockDiag] Wait started (timeout: {lockTimeout.TotalSeconds}s)");

            bool acquired = false;
            try
            {
                // Acquire the semaphore with timeout
                acquired = await lockEntry.Semaphore.WaitAsync(lockTimeout, cancellationToken);
                waitStopwatch.Stop();

                if (!acquired)
                {
                    _logger.LogWarning($"[{callId}] [LockDiag] Failed to acquire lock within {lockTimeout.TotalSeconds} seconds. Waited {waitStopwatch.ElapsedMilliseconds} ms.");
                    throw new TimeoutException($"Failed to acquire lock for Call-ID '{callId}' within {lockTimeout.TotalSeconds} seconds");
                }

                _logger.LogDebug($"[{callId}] [LockDiag] Lock acquired after {waitStopwatch.ElapsedMilliseconds} ms");
                lockEntry.LastAccessTime = DateTime.UtcNow;

                var execStopwatch = System.Diagnostics.Stopwatch.StartNew();
                _logger.LogDebug($"[{callId}] [LockDiag] Execution started");

                try
                {
                    // Execute the function while holding the lock
                    var result = await function();
                    execStopwatch.Stop();
                    _logger.LogDebug($"[{callId}] [LockDiag] Execution finished in {execStopwatch.ElapsedMilliseconds} ms");
                    return result;
                }
                finally
                {
                    // Always release the semaphore
                    lockEntry.Semaphore.Release();
                    _logger.LogDebug($"[{callId}] [LockDiag] Lock released");
                }
            }
            finally
            {
                // Decrement reference count
                DecrementReferenceCount(callId, lockEntry);
            }
        }

        /// <summary>
        /// Acquires a lock for the specified Call-ID and executes the provided action.
        /// </summary>
        public async Task ExecuteWithLockAsync(
            string callId,
            Func<Task> action,
            TimeSpan? timeout = null,
            CancellationToken cancellationToken = default)
        {
            await ExecuteWithLockAsync(callId, async () =>
            {
                await action();
                return (object)null; // Convert void Task to Task<object>
            }, timeout, cancellationToken);
        }

        /// <summary>
        /// Releases all locks and cleans up resources for the specified Call-ID.
        /// </summary>
        public async Task CleanupLocksAsync(string callId)
        {
            if (string.IsNullOrWhiteSpace(callId))
                return;

            _logger.LogDebug($"[{callId}] Cleaning up locks");

            if (_locks.TryRemove(callId, out var lockEntry))
            {
                // Wait a short time to ensure any pending operations complete
                await Task.Delay(100);
                
                // Dispose the semaphore
                lockEntry.Dispose();
                _logger.LogDebug($"[{callId}] Lock cleanup completed");
            }
            else
            {
                _logger.LogDebug($"[{callId}] No locks found to cleanup");
            }
        }

        /// <summary>
        /// Gets or creates a lock entry for the specified Call-ID.
        /// </summary>
        private CallLockEntry GetOrCreateLockEntry(string callId)
        {
            return _locks.AddOrUpdate(callId,
                // Factory for new entry
                _ => {
                    var entry = new CallLockEntry();
                    entry.ReferenceCount = 1;
                    _logger.LogTrace($"[{callId}] Created new lock entry");
                    return entry;
                },
                // Update function for existing entry
                (_, existing) => {
                    Interlocked.Increment(ref existing.ReferenceCount);
                    existing.LastAccessTime = DateTime.UtcNow;
                    _logger.LogTrace($"[{callId}] Incremented lock reference count to {existing.ReferenceCount}");
                    return existing;
                });
        }

        /// <summary>
        /// Decrements the reference count and removes the entry if no longer needed.
        /// </summary>
        private void DecrementReferenceCount(string callId, CallLockEntry lockEntry)
        {
            var newCount = Interlocked.Decrement(ref lockEntry.ReferenceCount);
            _logger.LogTrace($"[{callId}] Decremented lock reference count to {newCount}");

            // If reference count reaches zero, we can potentially remove it
            // However, we don't remove immediately to avoid thrashing
            // The cleanup timer will handle removal of unused entries
        }

        /// <summary>
        /// Timer callback to cleanup unused lock entries.
        /// </summary>
        private void CleanupUnusedLocks(object state)
        {
            if (_disposed)
                return;

            lock (_cleanupLock)
            {
                if (_disposed)
                    return;

                var cutoffTime = DateTime.UtcNow.AddMinutes(-10); // Remove entries unused for 10+ minutes
                var removedCount = 0;

                var entriesToRemove = new List<string>();

                foreach (var kvp in _locks)
                {
                    var callId = kvp.Key;
                    var entry = kvp.Value;

                    // Only remove if reference count is 0 and hasn't been accessed recently
                    if (entry.ReferenceCount <= 0 && entry.LastAccessTime < cutoffTime)
                    {
                        entriesToRemove.Add(callId);
                    }
                }

                foreach (var callId in entriesToRemove)
                {
                    if (_locks.TryRemove(callId, out var entry))
                    {
                        entry.Dispose();
                        removedCount++;
                        _logger.LogTrace($"[{callId}] Removed unused lock entry during cleanup");
                    }
                }

                if (removedCount > 0)
                {
                    _logger.LogDebug($"Cleanup removed {removedCount} unused lock entries. Active locks: {_locks.Count}");
                }
            }
        }

        /// <summary>
        /// Disposes the synchronization service and all resources.
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            lock (_cleanupLock)
            {
                if (_disposed)
                    return;

                _disposed = true;

                // Stop the cleanup timer
                _cleanupTimer?.Dispose();

                // Dispose all lock entries
                foreach (var kvp in _locks)
                {
                    kvp.Value.Dispose();
                }

                _locks.Clear();
                _logger.LogInformation("CallSynchronizationService disposed");
            }
        }
    }
}