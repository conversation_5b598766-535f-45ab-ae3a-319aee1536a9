# Use the official .NET 9 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app

# Create directory for recordings
RUN mkdir -p /app/recordings

# Expose SIP and RTP ports
# SIP signaling port (changed from 5060 to avoid conflicts)
EXPOSE 5090/udp
# RTP media ports range
EXPOSE 10000-19998/udp
# HTTP port for API (changed from 8080 to avoid conflicts)
EXPOSE 8081

# Use the official .NET 9 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy project file and restore dependencies
COPY ["voice-processing-service.csproj", "./"]
RUN dotnet restore "voice-processing-service.csproj"

# Copy source code
COPY . .

# Build the application
RUN dotnet build "voice-processing-service.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "voice-processing-service.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app

# Copy published application
COPY --from=publish /app/publish .

# Set environment variables with defaults
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8081
ENV RECORDINGS_PATH=/app/recordings

# Configuration defaults (standard ASP.NET Core format - can be overridden)
ENV SipServer__ListenIpAddress=Any
ENV SipServer__ListenPort=5090
ENV SipServer__RtpPortMin=10000
ENV SipServer__RtpPortMax=19998

# Phonexia API configuration (standard ASP.NET Core format - can be overridden)
ENV Phonexia__ApiUrl=http://localhost:8600
ENV Phonexia__Username=admin
ENV Phonexia__Password=phonexia
ENV Phonexia__Language=cs-CZ
ENV Phonexia__Model=CS_CZ_O2_6
ENV Phonexia__ChunkSizeBytes=8000


# Install curl for runtime health probes (used by --health-cmd) before switching to non-root
RUN apt-get update && apt-get install -y --no-install-recommends curl ca-certificates && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser


# Set the entry point
ENTRYPOINT ["dotnet", "voice-processing-service.dll"]
