# Plán testování pro běh serveru a simulátoru na jedné stanici

Tento dokument obsahuje podrobný plán testování implementovaných změn, k<PERSON><PERSON> umo<PERSON>ují běh serveru (voice-processing-service) a simulátoru (voice-processing-simulator) na jedné stanici.

## Příprava testovacího prostředí

Před zahájením testování je potřeba připravit testovací prostředí:

1. <PERSON><PERSON><PERSON><PERSON><PERSON> se, že máte aktuální verzi kódu s implementovanými změnami
2. Zkompilujte oba projekty:
   ```bash
   dotnet build voice-processing-service/voice-processing-service.csproj
   dotnet build voice-processing-simulator/voice-processing-simulator.csproj
   ```
3. Připravte testovací WAV soubor (pokud ještě nemáte):
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- generate test.wav 10 440
   ```

## 1. Testování serveru s výchozími porty

### 1.1 Spuštění serveru bez parametrů

**Příkaz:**
```bash
dotnet run --project voice-processing-service/voice-processing-service.csproj
```

**Očekávaný výsledek:**
- Server se spustí a naslouchá na výchozím SIP portu (5060)
- Server používá výchozí rozsah RTP portů (30002-30010)

**Způsob ověření:**
- V logu serveru by měla být zpráva: `SIP server listening on 0.0.0.0:5060`
- V logu serveru by měla být zpráva o vytvoření VoIPMediaSession s RTP porty

### 1.2 Ověření, že server naslouchá na správných portech

**Příkaz:**
```bash
# Na Windows
netstat -ano | findstr 5060
# Na Linux/macOS
netstat -tulpn | grep 5060
```

**Očekávaný výsledek:**
- Výpis by měl obsahovat řádek s portem 5060 a procesem serveru

## 2. Testování serveru s vlastními porty

### 2.1 Spuštění serveru s proměnnými prostředí pro konfiguraci portů

**Příkaz:**
```bash
# Windows
set SIPSERVER__LISTENPORT=5061
set SIPSERVER__RTPPORTMIN=31000
set SIPSERVER__RTPPORTMAX=31010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Linux/macOS
SIPSERVER__LISTENPORT=5061 SIPSERVER__RTPPORTMIN=31000 SIPSERVER__RTPPORTMAX=31010 dotnet run --project voice-processing-service/voice-processing-service.csproj
```

**Očekávaný výsledek:**
- Server se spustí a naslouchá na SIP portu 5061
- Server používá rozsah RTP portů 31000-31010

**Způsob ověření:**
- V logu serveru by měla být zpráva: `SIP server listening on 0.0.0.0:5061`
- V logu serveru by měla být zpráva o vytvoření VoIPMediaSession s RTP porty

### 2.2 Ověření, že server naslouchá na správných portech

**Příkaz:**
```bash
# Na Windows
netstat -ano | findstr 5061
# Na Linux/macOS
netstat -tulpn | grep 5061
```

**Očekávaný výsledek:**
- Výpis by měl obsahovat řádek s portem 5061 a procesem serveru

## 3. Testování simulátoru s výchozími porty

### 3.1 Spuštění simulátoru s minimálními parametry

**Příkaz:**
```bash
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 10
```

**Očekávaný výsledek:**
- Simulátor se spustí a naslouchá na výchozím SIP portu (5070)
- Simulátor používá výchozí rozsah RTP portů (25000-25010)

**Způsob ověření:**
- V logu simulátoru by měla být zpráva: `SIP UDP channel created on 0.0.0.0:5070`
- V logu simulátoru by měla být zpráva o vytvoření VoIP media session s RTP porty

### 3.2 Ověření, že simulátor naslouchá na správných portech

**Příkaz:**
```bash
# Na Windows
netstat -ano | findstr 5070
# Na Linux/macOS
netstat -tulpn | grep 5070
```

**Očekávaný výsledek:**
- Výpis by měl obsahovat řádek s portem 5070 a procesem simulátoru

## 4. Testování simulátoru s vlastními porty

### 4.1 Spuštění simulátoru s parametry pro konfiguraci portů

**Příkaz:**
```bash
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 10 5071 26000 26010
```

**Očekávaný výsledek:**
- Simulátor se spustí a naslouchá na SIP portu 5071
- Simulátor používá rozsah RTP portů 26000-26010

**Způsob ověření:**
- V logu simulátoru by měla být zpráva: `SIP UDP channel created on 0.0.0.0:5071`
- V logu simulátoru by měla být zpráva o vytvoření VoIP media session s RTP porty

### 4.2 Ověření, že simulátor naslouchá na správných portech

**Příkaz:**
```bash
# Na Windows
netstat -ano | findstr 5071
# Na Linux/macOS
netstat -tulpn | grep 5071
```

**Očekávaný výsledek:**
- Výpis by měl obsahovat řádek s portem 5071 a procesem simulátoru

## 5. Testování serveru a simulátoru na jedné stanici

### 5.1 Spuštění serveru a simulátoru na jedné stanici s různými porty

**Příkazy:**
```bash
# Terminál 1 - Server (Windows)
set SIPSERVER__LISTENPORT=5060
set SIPSERVER__RTPPORTMIN=30000
set SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 1 - Server (Linux/macOS)
SIPSERVER__LISTENPORT=5060 SIPSERVER__RTPPORTMIN=30000 SIPSERVER__RTPPORTMAX=30010 dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 2 - Simulátor
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 10 5070 25000 25010
```

**Očekávaný výsledek:**
- Server se spustí a naslouchá na SIP portu 5060 a RTP portech 30000-30010
- Simulátor se spustí a naslouchá na SIP portu 5070 a RTP portech 25000-25010
- Simulátor se úspěšně připojí k serveru
- RTP stream je správně přenášen
- Hovor je správně ukončen po 10 sekundách

**Způsob ověření:**
- V logu serveru by měla být zpráva o přijetí INVITE požadavku
- V logu serveru by měla být zpráva o vytvoření RTP přijímače s konkrétními porty
- V logu serveru by měla být zpráva o přijetí RTP paketů
- V logu simulátoru by měla být zpráva o úspěšném navázání spojení
- V logu simulátoru by měla být zpráva o odesílání RTP paketů
- V logu simulátoru by měla být zpráva o ukončení hovoru

### 5.2 Ověření, že RTP stream je správně přenášen

**Způsob ověření:**
- V logu serveru by měly být zprávy o přijetí RTP paketů
- V adresáři RecordedCalls by měl být vytvořen WAV soubor s nahraným hovorem
- Přehrajte WAV soubor a ověřte, že obsahuje očekávaný zvuk

## 6. Testování více instancí serveru na jedné stanici

### 6.1 Spuštění více instancí serveru na jedné stanici s různými porty

**Příkazy:**
```bash
# Terminál 1 - Server 1 (Windows)
set SIPSERVER__LISTENPORT=5060
set SIPSERVER__RTPPORTMIN=30000
set SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 1 - Server 1 (Linux/macOS)
SIPSERVER__LISTENPORT=5060 SIPSERVER__RTPPORTMIN=30000 SIPSERVER__RTPPORTMAX=30010 dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 2 - Server 2 (Windows)
set SIPSERVER__LISTENPORT=5061
set SIPSERVER__RTPPORTMIN=31000
set SIPSERVER__RTPPORTMAX=31010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 2 - Server 2 (Linux/macOS)
SIPSERVER__LISTENPORT=5061 SIPSERVER__RTPPORTMIN=31000 SIPSERVER__RTPPORTMAX=31010 dotnet run --project voice-processing-service/voice-processing-service.csproj
```

**Očekávaný výsledek:**
- Server 1 se spustí a naslouchá na SIP portu 5060 a RTP portech 30000-30010
- Server 2 se spustí a naslouchá na SIP portu 5061 a RTP portech 31000-31010

**Způsob ověření:**
- V logu serveru 1 by měla být zpráva: `SIP server listening on 0.0.0.0:5060`
- V logu serveru 2 by měla být zpráva: `SIP server listening on 0.0.0.0:5061`
- Ověřte pomocí netstat, že oba servery naslouchají na správných portech

## 7. Testování více instancí simulátoru na jedné stanici

### 7.1 Spuštění více instancí simulátoru na jedné stanici s různými porty

**Příkazy:**
```bash
# Terminál 1 - Simulátor 1
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 10 5070 25000 25010

# Terminál 2 - Simulátor 2
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5061 10 5071 26000 26010
```

**Očekávaný výsledek:**
- Simulátor 1 se spustí a naslouchá na SIP portu 5070 a RTP portech 25000-25010
- Simulátor 2 se spustí a naslouchá na SIP portu 5071 a RTP portech 26000-26010

**Způsob ověření:**
- V logu simulátoru 1 by měla být zpráva: `SIP UDP channel created on 0.0.0.0:5070`
- V logu simulátoru 2 by měla být zpráva: `SIP UDP channel created on 0.0.0.0:5071`
- Ověřte pomocí netstat, že oba simulátory naslouchají na správných portech

## 8. Testování kompletního scénáře

### 8.1 Spuštění dvou instancí serveru a dvou instancí simulátoru na jedné stanici

**Příkazy:**
```bash
# Terminál 1 - Server 1
SIPSERVER__LISTENPORT=5060 SIPSERVER__RTPPORTMIN=30000 SIPSERVER__RTPPORTMAX=30010 dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 2 - Server 2
SIPSERVER__LISTENPORT=5061 SIPSERVER__RTPPORTMIN=31000 SIPSERVER__RTPPORTMAX=31010 dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 3 - Simulátor 1
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 10 5070 25000 25010

# Terminál 4 - Simulátor 2
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5061 10 5071 26000 26010
```

**Očekávaný výsledek:**
- Všechny instance se spustí a naslouchají na správných portech
- Oba simulátory se úspěšně připojí k příslušným serverům
- RTP streamy jsou správně přenášeny
- Hovory jsou správně ukončeny po 10 sekundách

**Způsob ověření:**
- V logu serverů by měly být zprávy o přijetí INVITE požadavků
- V logu serverů by měly být zprávy o vytvoření RTP přijímačů s konkrétními porty
- V logu serverů by měly být zprávy o přijetí RTP paketů
- V logu simulátorů by měly být zprávy o úspěšném navázání spojení
- V logu simulátorů by měly být zprávy o odesílání RTP paketů
- V logu simulátorů by měly být zprávy o ukončení hovorů
- V adresáři RecordedCalls by měly být vytvořeny WAV soubory s nahranými hovory

## Závěr

Po provedení všech testů by mělo být jasné, zda implementované změny umožňují běh serveru a simulátoru na jedné stanici bez konfliktů portů. Pokud některý z testů selže, je potřeba identifikovat příčinu a provést potřebné úpravy.
