{{- range $bitswan, $values := .Values.sites }}
{{- range $instance, $data := $values }}
{{- $bitsafe := cat $bitswan "-" $instance | replace "_" "" | nospace }}
{{- if $data.enabled }}
{{- $global := $.Values.default }}
{{- $autoscaling := mergeOverwrite ($global.autoscaling  | deepCopy) (default dict $data.autoscaling) }}
{{- $hostAliases := mergeOverwrite ($global.hostAliases | deepCopy) (default dict $data.hostAliases) }}
{{- $livenessProbe := mergeOverwrite ($global.livenessProbe | deepCopy) (default dict $data.livenessProbe) }}
{{- $pvc := mergeOverwrite ($global.pvc | deepCopy) (default dict $data.pvc) }}
{{- $resources := mergeOverwrite ($global.resources | deepCopy)  (default dict $data.resources) }}
{{- $startupProbe := mergeOverwrite ($global.startupProbe | deepCopy) (default dict $data.startupProbe) }}
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ $bitsafe }}
  labels:
    {{- include "initial.labels" $ | nindent 4 }}
spec:
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      {{- include "initial.selectorLabels" $ | nindent 6 }}
  serviceName: {{ $.Release.Name }}-{{ $bitsafe }}
  minReadySeconds: 10
  {{- if not $autoscaling.enabled }}
  replicas: {{ default $global.replicas $data.replicas }}
  {{- end }}
  template:
    metadata:
      labels:
        {{- include "initial.selectorLabels" $ | nindent 8 }}
    spec:
      imagePullSecrets:
        - name: {{ $.Release.Name }}-gitlab-dockerconfig
      serviceAccountName: {{ include "initial.serviceAccountName" $ }}
      securityContext:
        {{- toYaml $.Values.podSecurityContext | nindent 8 }}
      terminationGracePeriodSeconds: {{ default $global.terminationGracePeriodSeconds $data.terminationGracePeriodSeconds }}
      {{- if $hostAliases.enabled }}
      hostAliases:
        {{- range $targetAddress, $targetHostname := $hostAliases.records }}
        - ip: {{ $targetAddress }}
          hostnames:
          - "{{ $targetHostname }}"
        {{- end }}
      {{- end }}
      {{- if ne $data.environment "production" }}
      initContainers:
        - name: gitops-bitswans
          image: "{{ $.Values.image.registry }}/ntwcl/gitopsntw/git:latest"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: {{ $.Release.Name }}-{{ $bitsafe }}
            - secretRef:
                name: {{ $.Release.Name }}-bitswans
          command:
            - "/opt/checkout.sh"
          tty: true
          stdin: true
          volumeMounts:
            - name: bitswans-source
              mountPath: /opt/bspumpo2
            - name: ephemeral-data
              mountPath: /opt/temp
            - name: checkout-script
              mountPath: /opt/checkout.sh
              subPath: checkout.sh
          resources:
            limits:
              cpu: 100m
              memory: 128Mi
            requests:
              cpu: 100m
              memory: 128Mi
        {{- end }}
      containers:
        - name: {{ $bitsafe }}
          securityContext:
            {{- toYaml $.Values.securityContext | nindent 12 }}
          image: "{{ $.Values.image.registry }}/deployments/{{ $.Chart.Name }}/{{ $bitswan  }}:{{ $data.imageVersion }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
          env:
            - name: PYTHONUNBUFFERED
              value: "1"
            - name: HTTP_PROXY
            - name: HTTPS_PROXY
            - name: REQUESTS_CA_BUNDLE
              value: "/etc/ssl/certs/O2CZ.CA.bundle.pem"
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: NODENAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          {{- if $data.debug }}
          command:
            - "tail"
            - "-f"
            - "/dev/null"
          {{- else }}
          args: [ {{ $bitswan | quote }} ]
          startupProbe:
            tcpSocket:
              port: {{ $.Values.service.port }}
            failureThreshold: {{ $startupProbe.failureThreshold }}
            periodSeconds: {{ $startupProbe.periodSeconds }}
          livenessProbe:
            tcpSocket:
              port: {{ $.Values.service.port }}
            initialDelaySeconds: {{ $livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ $livenessProbe.periodSeconds }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ $.Values.service.port }}
              protocol: TCP
          resources:
            {{- $resources | toYaml | nindent 12 }}
          volumeMounts:
            {{- if $pvc.enabled }}
            - name: persistent-data
              mountPath: /opt/data
            {{- end }}
            {{- if ne $data.environment "production" }}
            - name: bitswans-source
              mountPath: /opt/bspumpo2
            {{- end }}
            - name: ephemeral-data
              mountPath: /opt/temp
            - name: site-conf
              mountPath: /opt/etc/site.conf
              subPath: site.conf
            - name: global-conf
              mountPath: /opt/global_configs/
      volumes:
        {{- if ne $data.environment "production" }}
        - name: bitswans-source
          emptyDir: {}
        - name: checkout-script
          configMap:
            name: {{ $.Release.Name }}-{{ $bitsafe }}
            items:
              - key: checkout.sh
                path: checkout.sh
            defaultMode: 0755
        {{- end }}
        - name: ephemeral-data
          emptyDir: {}
        - name: site-conf
          secret:
            secretName: {{ $.Release.Name }}-{{ $bitsafe }}
            items:
              - key: site.conf
                path: site.conf
        - name: global-conf
          secret:
            secretName: {{ $.Release.Name }}-global-config
      {{- with $.Values.nodeSelector }}
      nodeSelector:
        {{- toYaml $ | nindent 8 }}
      {{- end }}
      {{- with $.Values.affinity }}
      affinity:
        {{- toYaml $ | nindent 8 }}
      {{- end }}
      {{- with $.Values.tolerations }}
      tolerations:
        {{- toYaml $ | nindent 8 }}
      {{- end }}
  {{- if $pvc.enabled }}
  volumeClaimTemplates:
  - metadata:
      name: persistent-data
    spec:
      accessModes:
      {{- $pvc.accessModes | toYaml | nindent 8 }}
      resources:
      {{- $pvc.resources | toYaml | nindent 8 }}
      storageClassName: {{ $pvc.storageClassName }}
  {{- end }}
{{- end }}
{{- end }}
{{- end }}
