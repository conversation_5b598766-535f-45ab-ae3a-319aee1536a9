using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;

namespace voice_processing_service.Interfaces
{
    public interface IPortAllocator
    {
        Task<(UdpClient rtpClient, UdpClient rtcpClient)> AllocateRtpPairAsync(string callId, IPAddress localAddress);
        Task<(UdpClient rtpClient, UdpClient rtcpClient)> AllocateSpecificPairAsync(string callId, IPAddress localAddress, int rtpPort, int rtcpPort);
        Task ReleasePortsAsync(string callId, UdpClient rtpClient, UdpClient rtcpClient);
    }
}