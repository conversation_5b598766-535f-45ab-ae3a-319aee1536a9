# Umožnění běhu serveru a simulátoru na jedné stanici

## Problém

Aktuálně není možné spustit server (voice-processing-service) a simulátor (voice-processing-simulator) na jedné stanici, protože:

1. Používají pevně nastavené porty, k<PERSON><PERSON> mohou být v konfliktu
2. Implementace není plně kompatibilní s poslední verzí SIPSorcery (8.0.11)
3. RTP porty v SDP nemusí odpovídat portům, na kterých komponenty skutečně naslouchají

## Řešení

Navrhované řešení zahrnuje úpravy v obou komponentách, které umožní konfiguraci portů a zajistí kompatibilitu s aktuální verzí SIPSorcery.

### 1. Úpravy v simulátoru (voice-processing-simulator)

#### 1.1 Přidání parametrů příkazové řádky pro konfiguraci SIP a RTP portů

Rozšíříme stávající příkaz "simulate" o další parametry:

```csharp
case "simulate":
    if (args.Length < 4)
    {
        Console.WriteLine("Usage: voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds] [local_sip_port] [rtp_port_min] [rtp_port_max]");
        Console.WriteLine("Example: voice-processing-simulator simulate test.wav 127.0.0.1 5060 30 5070 25000 25010");
        return;
    }
    string wavFile = args[1];
    string serverIp = args[2];
    int serverPort = int.Parse(args[3]);
    int callDurationSeconds = args.Length > 4 ? int.Parse(args[4]) : 30; // Výchozí hodnota 30 sekund
    int localSipPort = args.Length > 5 ? int.Parse(args[5]) : 5070; // Výchozí hodnota 5070
    int rtpPortMin = args.Length > 6 ? int.Parse(args[6]) : 25000; // Výchozí hodnota 25000
    int rtpPortMax = args.Length > 7 ? int.Parse(args[7]) : 25010; // Výchozí hodnota 25010
    await SimulateCallAsync(wavFile, serverIp, serverPort, callDurationSeconds, localSipPort, rtpPortMin, rtpPortMax);
    break;
```

#### 1.2 Úprava metody SimulateCallAsync

Upravíme metodu SimulateCallAsync, aby používala konfigurované porty:

```csharp
private static async Task SimulateCallAsync(string wavFile, string serverIp, int serverPort, int callDurationSeconds, int localSipPort = 5070, int rtpPortMin = 25000, int rtpPortMax = 25010)
{
    // Existující kód...

    // Přidání UDP kanálu pro SIP komunikaci s konfigurovatelným portem
    var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, localSipPort);
    sipTransport.AddSIPChannel(sipChannel);
    _logger.LogInformation($"SIP UDP channel created on {sipChannel.ListeningEndPoint}");

    // Vytvoření VoIP media session s konfigurovatelným rozsahem portů
    _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
    {
        MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
        RtpPortRange = new PortRange(rtpPortMin, rtpPortMax),
    });

    // Zbytek existujícího kódu...
}
```

#### 1.3 Aktualizace nápovědy

Aktualizujeme metodu ShowUsage, aby obsahovala informace o nových parametrech:

```csharp
private static void ShowUsage()
{
    Console.WriteLine("Usage:");
    Console.WriteLine("  voice-processing-simulator generate <output_file> <duration_seconds> <tone_frequency>");
    Console.WriteLine("  voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds] [local_sip_port] [rtp_port_min] [rtp_port_max]");
    Console.WriteLine("Examples:");
    Console.WriteLine("  voice-processing-simulator generate test.wav 10 440");
    Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5060 30");
    Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5060 30 5070 25000 25010");
}
```

### 2. Úpravy v serveru (voice-processing-service)

#### 2.1 Konfigurace pomocí appsettings.json a proměnných prostředí

Server používá konfiguraci z appsettings.json, kterou lze přepsat pomocí proměnných prostředí. ASP.NET Core a Kestrel nativně podporují tuto funkcionalitu, takže není potřeba přidávat vlastní kód pro zpracování parametrů příkazové řádky.

Konfigurace v appsettings.json:

```json
"SipServer": {
  "ListenIpAddress": "Any",
  "ListenPort": 5060,
  "RtpPortMin": 30002,
  "RtpPortMax": 30010,
  "WavRecordingDirectory": "RecordedCalls"
}
```

Přepsání konfigurace pomocí proměnných prostředí:

```bash
# Windows
set SIPSERVER__LISTENPORT=5061
set SIPSERVER__RTPPORTMIN=31000
set SIPSERVER__RTPPORTMAX=31010

# Linux/macOS
export SIPSERVER__LISTENPORT=5061
export SIPSERVER__RTPPORTMIN=31000
export SIPSERVER__RTPPORTMAX=31010
```

ASP.NET Core automaticky nahradí dvojtečky v názvech sekcí konfigurace dvěma podtržítky v názvech proměnných prostředí.
```

#### 2.2 Zajištění správného nastavení RTP portů v SDP

Ujistíme se, že při vytváření VoIPMediaSession je správně nastaven rozsah portů:

```csharp
var _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(_options.RtpPortMin, _options.RtpPortMax),
});
```

## Příklady použití

### Spuštění serveru s vlastními porty

```bash
# Windows - nastavení proměnných prostředí před spuštěním
set SIPSERVER__LISTENPORT=5061
set SIPSERVER__RTPPORTMIN=31000
set SIPSERVER__RTPPORTMAX=31010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Linux/macOS - nastavení proměnných prostředí před spuštěním
export SIPSERVER__LISTENPORT=5061
export SIPSERVER__RTPPORTMIN=31000
export SIPSERVER__RTPPORTMAX=31010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Alternativně lze použít příkaz s proměnnými prostředí v jednom řádku
# Windows
set SIPSERVER__LISTENPORT=5061 && set SIPSERVER__RTPPORTMIN=31000 && set SIPSERVER__RTPPORTMAX=31010 && dotnet run --project voice-processing-service/voice-processing-service.csproj

# Linux/macOS
SIPSERVER__LISTENPORT=5061 SIPSERVER__RTPPORTMIN=31000 SIPSERVER__RTPPORTMAX=31010 dotnet run --project voice-processing-service/voice-processing-service.csproj
```

### Spuštění simulátoru s vlastními porty

```bash
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30 5070 25000 25010
```

### Spuštění serveru a simulátoru na jedné stanici

Pro spuštění serveru a simulátoru na jedné stanici je potřeba zajistit, aby používaly různé porty. Například:

1. Server:
   - SIP port: 5060 (standardní)
   - RTP porty: 30000-30010

2. Simulátor:
   - SIP port: 5070
   - RTP porty: 25000-25010

```bash
# Spuštění serveru (Windows)
set SIPSERVER__LISTENPORT=5060
set SIPSERVER__RTPPORTMIN=30000
set SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Spuštění serveru (Linux/macOS)
export SIPSERVER__LISTENPORT=5060
export SIPSERVER__RTPPORTMIN=30000
export SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Spuštění simulátoru (v jiném terminálu)
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30 5070 25000 25010
```

## Technické detaily implementace

### Kompatibilita s SIPSorcery 8.0.11

Aktuální implementace používá SIPSorcery verze 8.0.11. Při implementaci změn je potřeba zajistit, že:

1. Všechny volání API jsou kompatibilní s touto verzí
2. Oba projekty (server i simulátor) používají stejnou verzi knihovny
3. Jsou dodrženy správné postupy pro vytváření SIP a RTP spojení

### Správné nastavení RTP portů

Klíčovým aspektem je zajistit, že porty specifikované v SDP odpovídají portům, na kterých komponenty skutečně naslouchají. To je zajištěno:

1. V serveru: Nastavením `RtpPortRange` v `VoIPMediaSessionConfig` na hodnoty z konfigurace
2. V simulátoru: Nastavením `RtpPortRange` v `VoIPMediaSessionConfig` na hodnoty z parametrů příkazové řádky

### Bezpečnostní aspekty

Při spouštění více instancí na jedné stanici je potřeba dbát na:

1. Firewall - ujistěte se, že všechny potřebné porty jsou povoleny
2. Oprávnění - ujistěte se, že aplikace má oprávnění naslouchat na zadaných portech
3. Konflikty - ujistěte se, že porty nejsou používány jinými aplikacemi

## Další možná vylepšení

1. Přidání konfiguračního souboru pro simulátor
2. Automatické hledání volných portů
3. Lepší logování a diagnostika
4. Automatické testy pro ověření funkčnosti
5. Implementace TLS pro zabezpečenou komunikaci (až bude k dispozici certifikát)
