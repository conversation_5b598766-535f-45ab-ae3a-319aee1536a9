using Microsoft.AspNetCore.Mvc;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Controllers
{
    /// <summary>
    /// Health check controller for monitoring application status.
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly ILogger<HealthController> _logger;
        private readonly ISipRegistrationManager _registrationManager;

        public HealthController(ILogger<HealthController> logger, ISipRegistrationManager registrationManager)
        {
            _logger = logger;
            _registrationManager = registrationManager;
        }

        /// <summary>
        /// Basic health check endpoint.
        /// </summary>
        /// <returns>Health status information.</returns>
        [HttpGet]
        public IActionResult Get()
        {
            try
            {
                var healthInfo = new
                {
                    Status = "Healthy",
                    Timestamp = DateTime.UtcNow,
                    Version = GetType().Assembly.GetName().Version?.ToString() ?? "Unknown",
                    ActiveRegistrations = _registrationManager.ActiveRegistrationCount,
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
                };

                _logger.LogDebug("Health check requested - Status: Healthy, Active registrations: {ActiveRegistrations}", 
                    healthInfo.ActiveRegistrations);

                return Ok(healthInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                
                var errorInfo = new
                {
                    Status = "Unhealthy",
                    Timestamp = DateTime.UtcNow,
                    Error = ex.Message
                };

                return StatusCode(500, errorInfo);
            }
        }

        /// <summary>
        /// Detailed health check with component status.
        /// </summary>
        /// <returns>Detailed health status information.</returns>
        [HttpGet("detailed")]
        public IActionResult GetDetailed()
        {
            try
            {
                var registrations = _registrationManager.GetActiveRegistrations().ToList();
                
                var detailedHealth = new
                {
                    Status = "Healthy",
                    Timestamp = DateTime.UtcNow,
                    Version = GetType().Assembly.GetName().Version?.ToString() ?? "Unknown",
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                    Components = new
                    {
                        SipRegistrationManager = new
                        {
                            Status = "Healthy",
                            ActiveRegistrations = registrations.Count,
                            Registrations = registrations.Select(r => new
                            {
                                r.UserUri,
                                r.ContactUri,
                                r.RemoteEndPoint,
                                r.ExpiresAt,
                                r.IsActive
                            })
                        }
                    },
                    SystemInfo = new
                    {
                        MachineName = Environment.MachineName,
                        ProcessorCount = Environment.ProcessorCount,
                        WorkingSet = Environment.WorkingSet,
                        OSVersion = Environment.OSVersion.ToString()
                    }
                };

                return Ok(detailedHealth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Detailed health check failed");
                
                var errorInfo = new
                {
                    Status = "Unhealthy",
                    Timestamp = DateTime.UtcNow,
                    Error = ex.Message
                };

                return StatusCode(500, errorInfo);
            }
        }
    }
}
