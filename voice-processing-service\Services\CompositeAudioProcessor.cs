using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Composite audio processor that runs multiple child processors in parallel,
    /// broadcasting audio from a single source buffer to dedicated child buffers.
    /// </summary>
    public class CompositeAudioProcessor : IAudioProcessor
    {
        private readonly ILogger<CompositeAudioProcessor> _logger;
        private readonly ILoggerFactory _loggerFactory;
        private readonly string _callId;
        private readonly List<IAudioProcessor> _processors;
        private readonly List<IAudioBuffer> _childBuffers = new List<IAudioBuffer>();
        private bool _disposed;

        public string ProcessorId => $"COMPOSITE_{_callId}";

        public CompositeAudioProcessor(
            string callId,
            IEnumerable<IAudioProcessor> processors,
            ILoggerFactory loggerFactory,
            ILogger<CompositeAudioProcessor> logger)
        {
            _callId = callId ?? throw new ArgumentNullException(nameof(callId));
            if (processors == null) throw new ArgumentNullException(nameof(processors));
            _processors = processors.ToList();
            if (_processors.Count == 0)
            {
                throw new ArgumentException("At least one child processor must be provided.", nameof(processors));
            }
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _logger.LogInformation($"[{_callId}] CompositeAudioProcessor created with {_processors.Count} child processors: {string.Join(", ", _processors.Select(p => p.ProcessorId))}");
        }

        public async Task InitializeAsync(CancellationToken cancellationToken)
        {
            // Initialize all children (no-op for most)
            foreach (var p in _processors)
            {
                try
                {
                    await p.InitializeAsync(cancellationToken).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{_callId}] Error initializing child processor {p.ProcessorId}");
                    // Continue initializing others to maximize resilience
                }
            }
        }

        public async Task StartProcessingAsync(IAudioBuffer sourceBuffer, CancellationToken cancellationToken)
        {
            if (sourceBuffer == null) throw new ArgumentNullException(nameof(sourceBuffer));
            _logger.LogInformation($"[{_callId}] Composite processing starting. Source buffer hash: {sourceBuffer.GetHashCode()}");

            // Create dedicated buffers for each child processor
            foreach (var _ in _processors)
            {
                var childLogger = _loggerFactory.CreateLogger<BlockingCollectionAudioBuffer>();
                _childBuffers.Add(new BlockingCollectionAudioBuffer(childLogger));
            }

            // Start child processors
            var childTasks = new List<Task>(_processors.Count);
            for (int i = 0; i < _processors.Count; i++)
            {
                var proc = _processors[i];
                var buf = _childBuffers[i];
                childTasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        await proc.StartProcessingAsync(buf, cancellationToken).ConfigureAwait(false);
                    }
                    catch (OperationCanceledException)
                    {
                        // Expected on shutdown
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{_callId}] Child processor {proc.ProcessorId} failed.");
                    }
                }, cancellationToken));
            }

            // Broadcast loop
            try
            {
                while (!sourceBuffer.IsCompleted || sourceBuffer.TryTake(out _, 0, CancellationToken.None))
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        _logger.LogInformation($"[{_callId}] Composite processing cancellation requested.");
                        break;
                    }

                    if (sourceBuffer.TryTake(out var audioData, 500, cancellationToken))
                    {
                        // Fan-out the same immutable byte[] to all child buffers
                        for (int i = 0; i < _childBuffers.Count; i++)
                        {
                            try
                            {
                                _childBuffers[i].Add(audioData);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, $"[{_callId}] Failed to add audio to child buffer {i}.");
                            }
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] Composite broadcast cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during composite broadcast.");
            }
            finally
            {
                // Signal completion to all children
                foreach (var buf in _childBuffers)
                {
                    try
                    {
                        buf.CompleteAdding();
                    }
                    catch (ObjectDisposedException)
                    {
                        // Ignore
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"[{_callId}] Error completing child buffer.");
                    }
                }

                // Wait for children
                try
                {
                    await Task.WhenAll(childTasks).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"[{_callId}] One or more child processors finished with errors.");
                }

                _logger.LogInformation($"[{_callId}] Composite processing stopped.");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _logger.LogInformation($"[{_callId}] Disposing CompositeAudioProcessor.");

            foreach (var p in _processors)
            {
                try
                {
                    p.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"[{_callId}] Error disposing child processor {p.ProcessorId}");
                }
            }

            foreach (var b in _childBuffers)
            {
                try
                {
                    b.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"[{_callId}] Error disposing child buffer.");
                }
            }

            _disposed = true;
            GC.SuppressFinalize(this);
        }
    }
}