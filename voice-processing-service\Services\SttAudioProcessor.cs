using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Models;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace procesoru pro zpracování audio dat a jejich o<PERSON> do STT služby.
    /// </summary>
    public class SttAudioProcessor : IAudioProcessor
    {
        private readonly ILogger<SttAudioProcessor> _logger;
        private readonly string _callId;
        private readonly PhonexiaOptions _options;
        private readonly HttpClient _httpClient;
        private readonly List<SttResult> _sttResults = new List<SttResult>();
        private readonly MemoryStream _audioBuffer = new MemoryStream();
        private readonly object _resultsLock = new object();
        private string _sessionId;
        private bool _isDisposed = false;

        /// <summary>
        /// Identifikátor procesoru.
        /// </summary>
        public string ProcessorId => $"STT_{_callId}";

        /// <summary>
        /// Vytvoří novou instanci SttAudioProcessor.
        /// </summary>
        /// <param name="callId">ID hovoru.</param>
        /// <param name="options">Konfigurace Phonexia služby.</param>
        /// <param name="httpClient">HTTP klient pro komunikaci s Phonexia API.</param>
        /// <param name="logger">Logger.</param>
        public SttAudioProcessor(
            string callId,
            IOptions<PhonexiaOptions> options,
            HttpClient httpClient,
            ILogger<SttAudioProcessor> logger)
        {
            _callId = callId ?? throw new ArgumentNullException(nameof(callId));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _logger.LogInformation($"[{_callId}] SttAudioProcessor created.");
        }

        /// <summary>
        /// No-op initialization for STT processor.
        /// </summary>
        public Task InitializeAsync(CancellationToken cancellationToken) => Task.CompletedTask;

        /// <summary>
        /// Spustí zpracování audio dat z bufferu.
        /// </summary>
        /// <param name="buffer">Buffer s audio daty.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{_callId}] Starting STT processing from buffer.");

            try
            {
                // Vytvoření STT session
                _sessionId = await CreateSessionAsync(cancellationToken);
                _logger.LogInformation($"[{_callId}] Created STT session with ID: {_sessionId}");

                // Zpracování audio dat z bufferu
                while (!buffer.IsCompleted || buffer.TryTake(out _, 0, CancellationToken.None))
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        _logger.LogInformation($"[{_callId}] STT processing cancellation requested.");
                        break;
                    }

                    // Blokující čekání s timeoutem a cancellation tokenem
                    if (buffer.TryTake(out byte[] audioData, 500, cancellationToken))
                    {
                        await ProcessAudioDataAsync(audioData, cancellationToken);
                    }
                }

                // Finalizace STT session
                await FinalizeSessionAsync(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] STT processing cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during STT processing.");
            }
            finally
            {
                _logger.LogInformation($"[{_callId}] STT processing stopped.");
            }
        }

        /// <summary>
        /// Vytvoří novou STT session.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>ID vytvořené session.</returns>
        private async Task<string> CreateSessionAsync(CancellationToken cancellationToken)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/sessions");
                request.Headers.Add("X-API-Key", _options.ApiKey);

                var content = new
                {
                    config = new
                    {
                        language = _options.Language,
                        model = _options.Model,
                        audio_format = new
                        {
                            encoding = "MULAW",
                            sample_rate = 8000,
                            channels = 1
                        }
                    }
                };

                request.Content = new StringContent(
                    JsonSerializer.Serialize(content),
                    Encoding.UTF8,
                    "application/json");

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var sessionResponse = JsonSerializer.Deserialize<PhonexiaSessionResponse>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                return sessionResponse.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to create STT session.");
                throw;
            }
        }

        /// <summary>
        /// Zpracuje audio data a odešle je do STT služby.
        /// </summary>
        /// <param name="audioData">Audio data k zpracování.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task ProcessAudioDataAsync(byte[] audioData, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(_sessionId) || audioData == null || audioData.Length == 0)
            {
                return;
            }

            try
            {
                // Přidání audio dat do bufferu
                _audioBuffer.Write(audioData, 0, audioData.Length);

                // Pokud máme dostatek dat, odešleme je do STT služby
                if (_audioBuffer.Length >= _options.ChunkSizeBytes)
                {
                    await SendAudioChunkAsync(cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error processing audio data.");
            }
        }

        /// <summary>
        /// Odešle chunk audio dat do STT služby.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task SendAudioChunkAsync(CancellationToken cancellationToken)
        {
            try
            {
                // Získání dat z bufferu
                byte[] audioChunk = _audioBuffer.ToArray();
                _audioBuffer.SetLength(0); // Reset bufferu

                // Odeslání dat do STT služby
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/sessions/{_sessionId}/data");
                request.Headers.Add("X-API-Key", _options.ApiKey);
                request.Content = new ByteArrayContent(audioChunk);
                request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                // Získání výsledků
                await GetResultsAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to send audio chunk to STT service.");
            }
        }

        /// <summary>
        /// Získá výsledky STT z aktuální session.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task GetResultsAsync(CancellationToken cancellationToken)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_options.ApiUrl}/sessions/{_sessionId}/result");
                request.Headers.Add("X-API-Key", _options.ApiKey);

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var resultResponse = JsonSerializer.Deserialize<PhonexiaResultResponse>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (resultResponse?.Result?.Transcripts != null)
                {
                    lock (_resultsLock)
                    {
                        foreach (var transcript in resultResponse.Result.Transcripts)
                        {
                            var sttResult = new SttResult
                            {
                                Text = transcript.Text,
                                StartTime = TimeSpan.FromSeconds(transcript.Start),
                                EndTime = TimeSpan.FromSeconds(transcript.End),
                                Confidence = transcript.Confidence,
                                IsFinal = transcript.IsFinal
                            };

                            _sttResults.Add(sttResult);
                            _logger.LogInformation($"[{_callId}] New STT result: {sttResult.Text} ({sttResult.StartTime} - {sttResult.EndTime})");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to get STT results.");
            }
        }

        /// <summary>
        /// Finalizuje STT session.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task FinalizeSessionAsync(CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(_sessionId))
            {
                return;
            }

            try
            {
                // Odeslání zbývajících dat
                if (_audioBuffer.Length > 0)
                {
                    await SendAudioChunkAsync(cancellationToken);
                }

                // Ukončení session
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/sessions/{_sessionId}/finalize");
                request.Headers.Add("X-API-Key", _options.ApiKey);

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                // Získání finálních výsledků
                await GetResultsAsync(cancellationToken);

                _logger.LogInformation($"[{_callId}] STT session finalized. Total results: {_sttResults.Count}");

                // Uložení výsledků do souboru
                await SaveResultsToFileAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to finalize STT session.");
            }
        }

        /// <summary>
        /// Uloží výsledky STT do souboru.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        private async Task SaveResultsToFileAsync(CancellationToken cancellationToken)
        {
            try
            {
                string resultsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SttResults");
                Directory.CreateDirectory(resultsDirectory);

                string filePath = Path.Combine(resultsDirectory, $"{_callId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json");

                using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None))
                using (var writer = new StreamWriter(fileStream, Encoding.UTF8))
                {
                    var json = JsonSerializer.Serialize(new
                    {
                        callId = _callId,
                        sessionId = _sessionId,
                        timestamp = DateTime.UtcNow,
                        results = _sttResults
                    }, new JsonSerializerOptions { WriteIndented = true, PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

                    await writer.WriteAsync(json);
                }

                _logger.LogInformation($"[{_callId}] STT results saved to file: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to save STT results to file.");
            }
        }

        /// <summary>
        /// Získá všechny výsledky STT.
        /// </summary>
        /// <returns>Seznam výsledků STT.</returns>
        public IReadOnlyList<SttResult> GetResults()
        {
            lock (_resultsLock)
            {
                return _sttResults.AsReadOnly();
            }
        }

        /// <summary>
        /// Uvolní prostředky.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
            {
                return;
            }

            _logger.LogInformation($"[{_callId}] Disposing SttAudioProcessor.");

            try
            {
                // Finalizace session, pokud ještě nebyla finalizována
                if (!string.IsNullOrEmpty(_sessionId))
                {
                    _ = FinalizeSessionAsync(CancellationToken.None).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during SttAudioProcessor disposal.");
            }

            _audioBuffer.Dispose();
            _isDisposed = true;
            GC.SuppressFinalize(this);
        }
    }
}
