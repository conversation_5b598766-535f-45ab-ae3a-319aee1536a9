# Declare variables to be passed into your templates.

replicaCount: 1

# Docker Registry definitions
image:
  registry: network.git.cz.o2:5005
  pullPolicy: IfNotPresent
  name: python_application
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

nameOverride: ""
fullnameOverride: ""
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

persistentVolumeClaim:
  enabled: true

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  baseDomain: okd-jzm.network.cz.o2
  className: "nginx"
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - paths:
        - path: /
          pathType: Prefix
  tls:
    enabled: true

resources:
   limits:
      cpu: 100m
      memory: 128Mi
   requests:
      cpu: 100m
      memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

affinity: {}
nodeSelector: {}
podAnnotations: {}

podReloader:
  enabled: true
  filename: ConfigMap.html.yaml

podMonitor:
  enabled: true

podSecurityContext: {}
securityContext: {}
tolerations: []