using Xunit;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Reflection;
using voice_processing_simulator;

namespace VoiceProcessingService.Tests.Unit.Simulators
{
    public class SipClientSimulatorTests
    {
        [Fact]
        public async Task Program_Main_WithNoArgs_PrintsUsage()
        {
            var assembly = typeof(WavFileGenerator).Assembly;
            var programType = assembly.GetType("voice_processing_simulator.Program");
            var main = programType!.GetMethod("Main", BindingFlags.Static | BindingFlags.NonPublic);
            var writer = new StringWriter();
            Console.SetOut(writer);
            var task = (Task)main!.Invoke(null, new object[] { new string[0] });
            await task;
            var output = writer.ToString();
            Assert.Contains("voice-processing-simulator generate", output);
        }

        [Fact]
        public async Task Program_Main_WithInvalidPort_ThrowsFormatException()
        {
            var assembly = typeof(WavFileGenerator).Assembly;
            var programType = assembly.GetType("voice_processing_simulator.Program");
            var main = programType!.GetMethod("Main", BindingFlags.Static | BindingFlags.NonPublic);
            string[] args = new string[] { "simulate", "test.wav", "127.0.0.1", "notaport" };
            var task = (Task)main!.Invoke(null, new object[] { args });
            await Assert.ThrowsAsync<FormatException>(() => task);
        }

        [Fact]
        public void GenerateWavFile_GeneratesFileWithCorrectSize()
        {
            var tempFile = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.wav");
            try
            {
                WavFileGenerator.GenerateWavFile(tempFile, 1, 440);
                Assert.True(File.Exists(tempFile));
                var fileInfo = new FileInfo(tempFile);
                Assert.Equal(44 + 8000, fileInfo.Length);
            }
            finally
            {
                if (File.Exists(tempFile))
                    File.Delete(tempFile);
            }
        }

        [Fact]
        public void GenerateWavFile_WithInvalidPath_PrintsError()
        {
            var writer = new StringWriter();
            Console.SetOut(writer);
            string invalidPath = Path.Combine(Path.GetTempPath(), "?:\\invalid.wav");
            WavFileGenerator.GenerateWavFile(invalidPath, 1, 440);
            var output = writer.ToString();
            Assert.Contains("Error generating WAV file", output);
        }
    }
}