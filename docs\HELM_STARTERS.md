# Helm Starters

[[_TOC_]]

--- 

To install `<PERSON><PERSON>` follow this tutorial: https://confluence.cz.o2/x/R1oPBg. [Helm Chart Starter](https://v2.helm.sh/docs/developing_charts/#chart-starter-packs) is a convenient quickstart of your K8s `Deployment`, see below for basic usage.

# Update

To update your `charts/starters` content to the latest versions of `<PERSON><PERSON>` Starters, there is a shell-script available: [charts/starters/starters_sync.sh](charts/starters/starters_sync.sh). Usage:

``` shell
$ cd charts
$ ./starters_sync.sh
2024-05-09 14:35:02 | INFO | Fetching new Helm Starters from '******************.o2:ntwcl/gitopsntw.git': 'templates/helm/starters/'
===============================
Updating origin
remote: Enumerating objects: 18788, done.
remote: Total 18788 (delta 0), reused 0 (delta 0), pack-reused 18788
Receiving objects: 100% (18788/18788), 5.69 MiB | 5.69 MiB/s, done.
Resolving deltas: 100% (14611/14611), done.
From network.git.cz.o2:ntwcl/gitopsntw
 * [new branch]        argocd_push_sync -> origin/argocd_push_sync
 * [new branch]        backup           -> origin/backup
 * [new branch]        main             -> origin/main
===============================
2024-05-09 14:35:04 | INFO | Done fetching
2024-05-09 14:35:04 | INFO | Finishing the update
2024-05-09 14:35:04 | INFO | Done updating Helm Starters
```


# Using the Chart Starter

Enter your `charts/` folder that was created during this GitLab repository initialzation. There should be three folders: 

1. `ccczbuddyrealtimephonexiaserver`: this is the main folder of your Deployment
2. `nginx`: this is the testing Nginx Application
3. `starters`: here be them Helm Starters

To use the Helm Chart Starter:

``` shell
$ cd charts
$ helm create ccczbuddyrealtimephonexiaserver --starter $PWD/starters/python
```

Path to the Starter has to be absolute, hence the `$PWD` EnvVar usage. You may also download additional Helm Chart Starters, see below for more information.

---

**IMPORTANT NOTE**: Keep in mind that the only `gitops-deployable` folders of this repository are `charts/ccczbuddyrealtimephonexiaserver` nad `charts/nginx`. You may have any number of other Helm Charts located within the `charts/` folder for sure: but **charts/ccczbuddyrealtimephonexiaserver** will always be used by ArgoCD to actually deploy an application from this repository.

---

# Anatomy of the Starter

Let's dissect the `charts/starters/python` as it showcases a few tips & tricks you may find handy during your development cycle. First there is a task you **should** perform: 

1. When you created the Helm Chart from Starter, you used the `ccczbuddyrealtimephonexiaserver` as name of the Helm Chart itself
2. Our K8s `Deployments` assume the name consists of `Application Project` and `Application Name` for better orientation
3. AppProject = `callc` and Application = `ccczbuddyrealtimephonexiaserver`

Your freshly created Helm Chart has the following configuration:

``` yaml
apiVersion: v2
appVersion: 0.1.0
description: A Helm chart for Kubernetes
name: ccczbuddyrealtimephonexiaserver
type: application
version: 0.1.0
```

Please change the `name` field before anything else:

``` yaml
name: callc-ccczbuddyrealtimephonexiaserver
```

## Pre-installed configurations

The Helm Chart created is tuned-up for deployment within the O2CZ K8s infrastructure and prepared for our current GitOps orchestration.

1. `values-dev.yaml` and `values-prod.yaml` are created
2. Docker Registry credentials preconfigured
3. Various configuration elements provided:

Template for `Deployment` of Python application (`Dockerfile`(s) included) showing methods of including `ConfigMap` and `Secret` data into the container runtime. How to restart container on `ConfigMap` (or any other) change. Volume mounts and `PersistentStorageClaim` usage demonstrated. `PodMonitor` for Prometheus prepared, should your app support it. 

To explain every detail here is pointless, the configuration Helm templates you either can understand or easily learn understand :) We're here to help. As for the Dockerfiles mentioned, there are two provided:

## Docker builds

Following two examples of `Dockerfile` are provided to help you: 

1. [charts/starters/python/files/Dockerfile.single](/charts/starters/python/files/Dockerfile.single): `single` stage build for dependencies that do not require `gcc` or such tools for module builds
2. [charts/starters/python/Dockerfile.multi](/charts/starters/python/Dockerfile.multi): `multi-stage` build as in opposed to the `single` build

Base-image for these Python builds is here: [Dockerfile.python](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/templates/dockerfiles/Dockerfile.python) and it's custom O2CZ build based on the Debian Bookworm and made for use within our available K8s platforms (namely for the elevated `Security Constraints` model of the `OpenShift` / `OKD`) 

## Test the Helm Starter

The easiest way to see what your Helm Starter will render is to use the `template` command:

``` yaml
$ cd charts
$ helm template callc-ccczbuddyrealtimephonexiaserver-dev ccczbuddyrealtimephonexiaserver --values ccczbuddyrealtimephonexiaserver/values-dev.yaml --output-dir rendered
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/ServiceAccount.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/Secret.ldap.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/ConfigMap.html.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/ConfigMap.ldap.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/ConfigMap.o2czca.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/PersistentVolumeClaim.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/Service.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/Deployment.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/Ingress.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/PodMonitor.yaml
wrote rendered/callc-ccczbuddyrealtimephonexiaserver/templates/tests/test-connection.yaml
```

See the `charts/rendered` folder for results.

## Other Helm Chart Starters

See the [BitSwan Helm Starter](../../../../docs/BITSWAN_HELM.md)
