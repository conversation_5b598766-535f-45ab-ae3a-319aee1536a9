using System;
using System.Text.Json.Serialization;

namespace voice_processing_service.Models
{
    /// <summary>
    /// Model pro odpověď z Phonexia API při vytvoření session.
    /// </summary>
    public class PhonexiaSessionResponse
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    /// <summary>
    /// Model pro odpověď z Phonexia API s výsledky STT.
    /// </summary>
    public class PhonexiaResultResponse
    {
        [JsonPropertyName("result")]
        public PhonexiaResult Result { get; set; }
    }

    /// <summary>
    /// Model pro výsledek STT z Phonexia API.
    /// </summary>
    public class PhonexiaResult
    {
        [JsonPropertyName("transcripts")]
        public PhonexiaTranscript[] Transcripts { get; set; }
    }

    /// <summary>
    /// Model pro transkript z Phonexia API.
    /// </summary>
    public class PhonexiaTranscript
    {
        [JsonPropertyName("text")]
        public string Text { get; set; }

        [JsonPropertyName("start")]
        public double Start { get; set; }

        [JsonPropertyName("end")]
        public double End { get; set; }

        [JsonPropertyName("confidence")]
        public double Confidence { get; set; }

        [JsonPropertyName("is_final")]
        public bool IsFinal { get; set; }
    }

    /// <summary>
    /// Model pro výsledek STT.
    /// </summary>
    public class SttResult
    {
        /// <summary>
        /// Text transkriptu.
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// Čas začátku transkriptu.
        /// </summary>
        public TimeSpan StartTime { get; set; }

        /// <summary>
        /// Čas konce transkriptu.
        /// </summary>
        public TimeSpan EndTime { get; set; }

        /// <summary>
        /// Míra jistoty (0-1).
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// Indikuje, zda je transkript finální.
        /// </summary>
        public bool IsFinal { get; set; }
    }
}
