using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using voice_processing_service.Services;
using Xunit;
using Xunit.Abstractions;

namespace voice_processing_service.Tests.Unit.Services
{
    /// <summary>
    /// Unit tests for CallSynchronizationService to verify per-Call-ID locking behavior,
    /// timeout handling, deadlock prevention, and cleanup mechanisms.
    /// </summary>
    public class CallSynchronizationServiceTests : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly CallSynchronizationService _synchronizationService;
        private readonly ILogger<CallSynchronizationService> _logger;

        public CallSynchronizationServiceTests(ITestOutputHelper output)
        {
            _output = output;
            _logger = NullLogger<CallSynchronizationService>.Instance;
            _synchronizationService = new CallSynchronizationService(_logger);
        }

        public void Dispose()
        {
            _synchronizationService?.Dispose();
        }

        [Fact]
        public async Task ExecuteWithLockAsync_ShouldExecuteFunctionSuccessfully()
        {
            // Arrange
            const string callId = "test-call-id";
            var executed = false;

            // Act
            var result = await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
            {
                executed = true;
                await Task.Delay(10);
                return "success";
            });

            // Assert
            Assert.True(executed);
            Assert.Equal("success", result);
        }

        [Fact]
        public async Task ExecuteWithLockAsync_ShouldExecuteActionSuccessfully()
        {
            // Arrange
            const string callId = "test-call-id";
            var executed = false;

            // Act
            await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
            {
                executed = true;
                await Task.Delay(10);
            });

            // Assert
            Assert.True(executed);
        }

        [Fact]
        public async Task ExecuteWithLockAsync_ShouldSerializeOperationsForSameCallId()
        {
            // Arrange
            const string callId = "same-call-id";
            var executionOrder = new List<int>();
            var tasks = new List<Task>();

            // Act - Start multiple operations for the same Call-ID
            for (int i = 0; i < 5; i++)
            {
                int operationId = i;
                tasks.Add(_synchronizationService.ExecuteWithLockAsync(callId, async () =>
                {
                    lock (executionOrder)
                    {
                        executionOrder.Add(operationId);
                    }
                    await Task.Delay(50); // Simulate work
                }));
            }

            await Task.WhenAll(tasks);

            // Assert - Operations should have executed serially
            Assert.Equal(5, executionOrder.Count);
            Assert.Equal(new[] { 0, 1, 2, 3, 4 }, executionOrder);
        }

        [Fact]
        public async Task ExecuteWithLockAsync_ShouldAllowConcurrentOperationsForDifferentCallIds()
        {
            // Arrange
            var callIds = new[] { "call-1", "call-2", "call-3" };
            var startTimes = new Dictionary<string, DateTime>();
            var endTimes = new Dictionary<string, DateTime>();
            var tasks = new List<Task>();

            // Act - Start operations for different Call-IDs
            foreach (var callId in callIds)
            {
                tasks.Add(_synchronizationService.ExecuteWithLockAsync(callId, async () =>
                {
                    startTimes[callId] = DateTime.UtcNow;
                    await Task.Delay(100); // Simulate work
                    endTimes[callId] = DateTime.UtcNow;
                }));
            }

            var stopwatch = Stopwatch.StartNew();
            await Task.WhenAll(tasks);
            stopwatch.Stop();

            // Assert - All operations should run concurrently, taking less time than sequential
            Assert.True(stopwatch.ElapsedMilliseconds < 250, 
                $"Expected concurrent execution < 250ms, but took {stopwatch.ElapsedMilliseconds}ms");
            Assert.Equal(3, startTimes.Count);
            Assert.Equal(3, endTimes.Count);
        }

        [Fact]
        public async Task ExecuteWithLockAsync_ShouldThrowTimeoutException_WhenTimeoutExceeded()
        {
            // Arrange
            const string callId = "timeout-test";
            var firstTaskStarted = new TaskCompletionSource<bool>();
            var timeout = TimeSpan.FromMilliseconds(100);

            // Start a long-running operation that holds the lock
            var longRunningTask = _synchronizationService.ExecuteWithLockAsync(callId, async () =>
            {
                firstTaskStarted.SetResult(true);
                await Task.Delay(500); // Hold lock for 500ms
            });

            // Wait for first task to start
            await firstTaskStarted.Task;

            // Act & Assert - Second operation should timeout
            var timeoutException = await Assert.ThrowsAsync<TimeoutException>(async () =>
            {
                await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
                {
                    await Task.CompletedTask;
                }, timeout);
            });

            Assert.Contains("Failed to acquire lock", timeoutException.Message);
            Assert.Contains(callId, timeoutException.Message);

            // Cleanup - wait for long-running task to complete
            await longRunningTask;
        }

        [Fact]
        public async Task ExecuteWithLockAsync_ShouldRespectCancellationToken()
        {
            // Arrange
            const string callId = "cancellation-test";
            var firstTaskStarted = new TaskCompletionSource<bool>();
            using var cts = new CancellationTokenSource();

            // Start a long-running operation that holds the lock
            var longRunningTask = _synchronizationService.ExecuteWithLockAsync(callId, async () =>
            {
                firstTaskStarted.SetResult(true);
                await Task.Delay(500); // Hold lock for 500ms
            });

            // Wait for first task to start
            await firstTaskStarted.Task;

            // Cancel after 50ms
            cts.CancelAfter(50);

            // Act & Assert - Second operation should be cancelled
            await Assert.ThrowsAsync<OperationCanceledException>(async () =>
            {
                await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
                {
                    await Task.CompletedTask;
                }, cancellationToken: cts.Token);
            });

            // Cleanup - wait for long-running task to complete
            await longRunningTask;
        }

        [Fact]
        public async Task ExecuteWithLockAsync_ShouldHandleExceptionsAndReleaseLock()
        {
            // Arrange
            const string callId = "exception-test";
            var firstOperationCompleted = false;
            var secondOperationCompleted = false;

            // Act - First operation throws exception
            await Assert.ThrowsAsync<InvalidOperationException>(async () =>
            {
                await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
                {
                    await Task.Delay(10);
                    throw new InvalidOperationException("Test exception");
                });
            });

            // Second operation should still be able to acquire the lock
            await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
            {
                secondOperationCompleted = true;
                await Task.CompletedTask;
            });

            // Assert
            Assert.True(secondOperationCompleted);
        }

        [Fact]
        public async Task CleanupLocksAsync_ShouldRemoveLockForCallId()
        {
            // Arrange
            const string callId = "cleanup-test";
            
            // Execute an operation to create a lock entry
            await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
            {
                await Task.Delay(10);
            });

            var initialCount = _synchronizationService.ActiveLockCount;

            // Act
            await _synchronizationService.CleanupLocksAsync(callId);

            // Assert
            Assert.True(_synchronizationService.ActiveLockCount <= initialCount);
        }

        [Fact]
        public void ActiveLockCount_ShouldReflectCurrentLockCount()
        {
            // Arrange
            var initialCount = _synchronizationService.ActiveLockCount;

            // Act & Assert - Count should increase when locks are active
            var task1 = _synchronizationService.ExecuteWithLockAsync("call-1", async () =>
            {
                // Check count while lock is held
                var countDuringExecution = _synchronizationService.ActiveLockCount;
                Assert.True(countDuringExecution >= initialCount);
                await Task.Delay(50);
            });

            var task2 = _synchronizationService.ExecuteWithLockAsync("call-2", async () =>
            {
                var countDuringExecution = _synchronizationService.ActiveLockCount;
                Assert.True(countDuringExecution >= initialCount);
                await Task.Delay(50);
            });

            Task.WaitAll(task1, task2);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public async Task ExecuteWithLockAsync_ShouldThrowArgumentException_ForInvalidCallId(string invalidCallId)
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(async () =>
            {
                await _synchronizationService.ExecuteWithLockAsync(invalidCallId, async () =>
                {
                    await Task.CompletedTask;
                });
            });
        }

        [Fact]
        public async Task ExecuteWithLockAsync_ShouldThrowArgumentNullException_ForNullFunction()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(async () =>
            {
                await _synchronizationService.ExecuteWithLockAsync<object>("test", null);
            });
        }

        [Fact]
        public async Task ExecuteWithLockAsync_ShouldHandleRapidSequentialCalls()
        {
            // Arrange
            const string callId = "rapid-test";
            const int operationCount = 100;
            var completedOperations = 0;

            // Act - Execute many rapid sequential operations
            var tasks = new List<Task>();
            for (int i = 0; i < operationCount; i++)
            {
                tasks.Add(_synchronizationService.ExecuteWithLockAsync(callId, async () =>
                {
                    Interlocked.Increment(ref completedOperations);
                    await Task.Delay(1); // Minimal delay
                }));
            }

            await Task.WhenAll(tasks);

            // Assert
            Assert.Equal(operationCount, completedOperations);
        }

        [Fact]
        public void Dispose_ShouldCleanupAllResources()
        {
            // Arrange
            var service = new CallSynchronizationService(_logger);

            // Execute some operations to create lock entries
            var task = service.ExecuteWithLockAsync("test", async () => await Task.Delay(10));
            task.Wait();

            var initialCount = service.ActiveLockCount;

            // Act
            service.Dispose();

            // Assert - Disposing should clean up resources
            Assert.Equal(0, service.ActiveLockCount);
        }
    }
}