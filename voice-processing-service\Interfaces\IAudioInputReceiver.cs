using System;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro přijímač audio dat (např. RTP).
    /// </summary>
    public interface IAudioInputReceiver : IDisposable
    {
        /// <summary>
        /// Spustí naslouchání na síťových portech a předávání audio dat do bufferu.
        /// </summary>
        /// <param name="buffer">Buffer pro ukládání přijatých audio dat.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken);

        /// <summary>
        /// Lokální endpoint pro RTP data.
        /// </summary>
        IPEndPoint RtpLocalEndPoint { get; }

        /// <summary>
        /// Lokální endpoint pro RTCP data.
        /// </summary>
        IPEndPoint RtcpLocalEndPoint { get; }

        /// <summary>
        /// Aktualizuje konfiguraci přijímače na základě re-INVITE.
        /// Umožňuje změnu parametrů bez restartování posluchačů.
        /// </summary>
        /// <param name="newRemoteEndpoint">Nový vzdálený endpoint pro RTP.</param>
        /// <param name="supportedCodecs">Seznam podporovaných kodeků.</param>
        Task UpdateConfigurationAsync(IPEndPoint newRemoteEndpoint, string[] supportedCodecs);
    }
}
