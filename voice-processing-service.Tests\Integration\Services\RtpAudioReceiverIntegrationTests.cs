using System;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;
using voice_processing_service.Services;
using voice_processing_service.Interfaces;
using Xunit;

namespace voice_processing_service.Tests.Integration.Services
{
    public class RtpAudioReceiverIntegrationTests
    {
        [Fact]
        public void BlockingCollection_AddingAndTakingData()
        {
            var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var data1 = new byte[] { 1, 2, 3 };
            var data2 = new byte[] { 4, 5 };

            buffer.Add(data1);
            buffer.Add(data2);

            Assert.True(buffer.TryTake(out var result1, 1000, CancellationToken.None));
            Assert.Equal(data1, result1);

            Assert.True(buffer.TryTake(out var result2, 1000, CancellationToken.None));
            Assert.Equal(data2, result2);

            buffer.Dispose();
        }

        [Fact]
        public void BlockingCollection_CompleteAddingAndIsCompleted()
        {
            var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());

            buffer.CompleteAdding();

            Assert.True(buffer.IsAddingCompleted);
            Assert.True(buffer.IsCompleted);
            Assert.False(buffer.TryTake(out _, 100, CancellationToken.None));

            buffer.Dispose();
        }

        [Fact]
        public async Task Test_RtpPackets_ArePassedToBuffer()
        {
            // Arrange: create buffer and receiver on ephemeral ports
            var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var rtpClient = new UdpClient(new IPEndPoint(IPAddress.Loopback, 0));
            var rtcpClient = new UdpClient(new IPEndPoint(IPAddress.Loopback, 0));
            var receiver = new RtpAudioReceiver("testcall", rtpClient, rtcpClient, new NullLogger<RtpAudioReceiver>());
            // Subscribe buffer to audio frames so payloads are enqueued
            receiver.AudioFrameReceived += buffer.Add;

            // Act: start listening
            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
            var listenTask = receiver.StartListeningAsync(buffer, cts.Token);

            // Send a simple RTP packet (12-byte header + payload)
            var payload = new byte[] { 1, 2, 3, 4, 5 };
            var header = new byte[12];
            header[0] = 0x80; // version 2
            header[1] = 0x00; // payload type 0
            var rtpPacket = new byte[header.Length + payload.Length];
            Buffer.BlockCopy(header, 0, rtpPacket, 0, header.Length);
            Buffer.BlockCopy(payload, 0, rtpPacket, header.Length, payload.Length);

            await new UdpClient().SendAsync(rtpPacket, rtpPacket.Length, receiver.RtpLocalEndPoint);

            // Assert: buffer receives payload
            Assert.True(buffer.TryTake(out var received, 1000, CancellationToken.None));
            Assert.Equal(payload, received);

            // Cleanup
            cts.Cancel();
            await listenTask;
            receiver.Dispose();
            buffer.Dispose();
        }

        [Fact]
        public async Task Test_StopListening_And_BufferCompletion()
        {
            // Arrange
            var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var rtpClient = new UdpClient(new IPEndPoint(IPAddress.Loopback, 0));
            var rtcpClient = new UdpClient(new IPEndPoint(IPAddress.Loopback, 0));
            var receiver = new RtpAudioReceiver("testcall2", rtpClient, rtcpClient, new NullLogger<RtpAudioReceiver>());

            // Act: start listening then cancel
            var cts = new CancellationTokenSource();
            var listenTask = receiver.StartListeningAsync(buffer, cts.Token);
            cts.Cancel();

            await listenTask;

            // Assert: buffer is marked completed
            Assert.True(buffer.IsAddingCompleted);
            Assert.True(buffer.IsCompleted);

            // Cleanup
            receiver.Dispose();
            buffer.Dispose();
        }
    }
}