using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging.Abstractions;
using voice_processing_service.Configuration;
using voice_processing_service.Services;
using voice_processing_service.Models;
using voice_processing_service.Interfaces;
using Xunit;

namespace voice_processing_service.Tests.Integration.Services
{
    public class SttAudioProcessorIntegrationTests
    {
        private const string SessionId = "session1";

        private class StubHttpMessageHandler : HttpMessageHandler
        {
            private readonly Queue<HttpResponseMessage> _responses;

            public StubHttpMessageHandler(Queue<HttpResponseMessage> responses)
            {
                _responses = responses;
            }

            protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
            {
                if (_responses.Count == 0)
                {
                    throw new InvalidOperationException("No more stubbed responses available.");
                }

                var response = _responses.Dequeue();
                return Task.FromResult(response);
            }
        }

        [Fact]
        public async Task TestProcessingAudioData_CreatesSessionProcessesDataAndStoresResultsInMemory()
        {
            // Arrange
            var responses = new Queue<HttpResponseMessage>();
            // 1. CreateSession
            responses.Enqueue(new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent($"{{\"id\":\"{SessionId}\"}}", Encoding.UTF8, "application/json")
            });
            // 2. Data chunk send
            responses.Enqueue(new HttpResponseMessage(HttpStatusCode.OK));
            // 3. Get results after data chunk
            var resultJson = "{\"result\":{\"transcripts\":[{\"text\":\"hello\",\"start\":0.0,\"end\":1.0,\"confidence\":0.9,\"is_final\":true}]}}";
            responses.Enqueue(new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(resultJson, Encoding.UTF8, "application/json")
            });
            // 4. Finalize session
            responses.Enqueue(new HttpResponseMessage(HttpStatusCode.OK));
            // 5. Get results after finalize (empty)
            var emptyJson = "{\"result\":{\"transcripts\":[]}}";
            responses.Enqueue(new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(emptyJson, Encoding.UTF8, "application/json")
            });

            var stubHandler = new StubHttpMessageHandler(responses);
            var httpClient = new HttpClient(stubHandler);
            var options = Options.Create(new PhonexiaOptions { ChunkSizeBytes = 1 });
            var logger = NullLogger<SttAudioProcessor>.Instance;
            var processor = new SttAudioProcessor("call1", options, httpClient, logger);
            var buffer = new BlockingCollectionAudioBuffer(NullLogger<BlockingCollectionAudioBuffer>.Instance);

            // Simulate audio
            buffer.Add(new byte[] { 1, 2, 3 });
            buffer.CompleteAdding();

            // Act
            await processor.StartProcessingAsync(buffer, CancellationToken.None);

            // Assert
            var results = processor.GetResults();
            Assert.Single(results);
            var result = results.First();
            Assert.Equal("hello", result.Text);
            Assert.Equal(TimeSpan.FromSeconds(0.0), result.StartTime);
            Assert.Equal(TimeSpan.FromSeconds(1.0), result.EndTime);
            Assert.Equal(0.9, result.Confidence);
            Assert.True(result.IsFinal);
        }

        [Fact]
        public async Task TestTermination_FinalizesSessionAndSavesResultsToFile()
        {
            // Arrange
            var responses = new Queue<HttpResponseMessage>();
            // 1. CreateSession
            responses.Enqueue(new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent($"{{\"id\":\"{SessionId}\"}}", Encoding.UTF8, "application/json")
            });
            // 2. Finalize session (no data chunk)
            responses.Enqueue(new HttpResponseMessage(HttpStatusCode.OK));
            // 3. Get results after finalize
            var resultJson2 = "{\"result\":{\"transcripts\":[{\"text\":\"bye\",\"start\":0.0,\"end\":0.0,\"confidence\":1.0,\"is_final\":true}]}}";
            responses.Enqueue(new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(resultJson2, Encoding.UTF8, "application/json")
            });

            var stubHandler2 = new StubHttpMessageHandler(responses);
            var httpClient2 = new HttpClient(stubHandler2);
            var options2 = Options.Create(new PhonexiaOptions { ChunkSizeBytes = 1000 });
            var logger2 = NullLogger<SttAudioProcessor>.Instance;
            var processor2 = new SttAudioProcessor("call2", options2, httpClient2, logger2);
            var buffer2 = new BlockingCollectionAudioBuffer(NullLogger<BlockingCollectionAudioBuffer>.Instance);
            buffer2.CompleteAdding();

            // Act
            await processor2.StartProcessingAsync(buffer2, CancellationToken.None);

            // Assert file is created
            var resultsDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SttResults");
            var file = Directory.GetFiles(resultsDir, "call2_*.json").FirstOrDefault();
            Assert.NotNull(file);
            var content = File.ReadAllText(file);
            Assert.Contains("bye", content);
        }
    }
}