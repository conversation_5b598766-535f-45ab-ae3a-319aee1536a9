# Analýza SIP a RTP implementace

Tento dokument obsahuje analýzu implementace SIP a RTP komunikace v serverovém a simulátorovém projektu, identifikované problémy a doporučení pro jejich <PERSON>.

## 1. Identifikované problémy

### 1.1 Problém s metodou CreateReceiverWithSpecificPorts

Jak již bylo identifikováno v předchozí analýze, metoda `CreateReceiverWithSpecificPorts` ve třídě `RtpAudioReceiverFactory` obsahuje pevně nastavené porty (40002 a 40003) pro vytvoření UDP klientů, což je v rozporu s parametry metody:

```csharp
public IAudioInputReceiver CreateReceiverWithSpecificPorts(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
{
    try
    {
        var aaa = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40002));
        var bbb = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40003));

        // ...

        // Vytvořit UDP klienty
        var rtpClient = aaa;
        var rtcpClient = bbb;

        // ...
    }
    // ...
}
```

Tato implementace ignoruje parametry `rtpPort` a `rtcpPort` a místo toho používá pevně nastavené porty 40002 a 40003. To způsobuje, že RTP komunikace nefunguje správně, protože server naslouchá na jiných portech, než které jsou uvedeny v SDP.

### 1.2 Nekonzistence v nastavení IP adresy

V metodě `CreateReceiverWithSpecificPorts` je použita IP adresa `IPAddress.Any` místo parametru `localAddress`:

```csharp
var aaa = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40002));
var bbb = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40003));
```

Toto může způsobit problémy, pokud je server nakonfigurován tak, aby naslouchal pouze na konkrétní IP adrese.

### 1.3 Pevně nastavené porty v simulátoru

Simulátor má pevně nastavený SIP port (5070) a rozsah RTP portů (25000-25010):

```csharp
// Přidání UDP kanálu pro SIP komunikaci
var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, 5070);

// ...

_rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(25000, 25010),
});
```

Toto znemožňuje spuštění více instancí simulátoru na jedné stanici.

### 1.4 Potenciální problém s RTP porty v SDP

V metodě `ProcessInviteAsync` třídy `SipServerService` je vytvořen `VoIPMediaSession` s rozsahem portů z konfigurace:

```csharp
var _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(_options.RtpPortMin, _options.RtpPortMax),
});
```

Ale později v kódu je vytvořen `RtpAudioReceiver` s konkrétními porty:

```csharp
Func<IAudioInputReceiver> inputReceiverFactory = () => _audioInputReceiverWithPortsFactory(callId, rtpPort, rtcpPort);
```

Pokud porty v `RtpAudioReceiver` neodpovídají portům, které jsou použity v SDP (vytvořeném z `VoIPMediaSession`), RTP komunikace nebude fungovat správně.

## 2. Doporučená řešení

### 2.1 Oprava metody CreateReceiverWithSpecificPorts

Metodu `CreateReceiverWithSpecificPorts` je potřeba upravit tak, aby správně používala parametry `rtpPort` a `rtcpPort` pro vytvoření UDP klientů:

```csharp
public IAudioInputReceiver CreateReceiverWithSpecificPorts(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
{
    try
    {
        _logger.LogInformation($"[{callId}] Attempting to create RTP receiver with specific ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit UDP klienty s požadovanými porty
        var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
        var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));

        // Nastavení většího bufferu pro UDP klienty
        rtpClient.Client.ReceiveBufferSize = 1048576; // 1MB buffer
        rtpClient.Client.SendBufferSize = 1048576;
        rtcpClient.Client.ReceiveBufferSize = 1048576;
        rtcpClient.Client.SendBufferSize = 1048576;

        _logger.LogInformation($"[{callId}] Successfully created RTP receiver with ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit a vrátit RtpAudioReceiver
        return new RtpAudioReceiver(callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());
    }
    catch (SocketException ex)
    {
        // Port je již obsazen nebo nemáme dostatečná oprávnění
        _logger.LogWarning($"[{callId}] Could not bind to RTP port {rtpPort} and RTCP port {rtcpPort}: {ex.Message} (Error code: {ex.SocketErrorCode})");

        // Zkusíme ještě jednou uvolnit porty s jiným přístupem
        _logger.LogInformation($"[{callId}] Attempting to force release ports with different approach");

        try
        {
            // Pokus o vytvoření socketů s nastavením ReuseAddress
            using (var socket1 = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp))
            {
                socket1.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                socket1.Bind(new IPEndPoint(localAddress, rtpPort));
                socket1.Close();
                _logger.LogInformation($"[{callId}] Successfully force-released RTP port {rtpPort}");
            }

            using (var socket2 = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp))
            {
                socket2.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                socket2.Bind(new IPEndPoint(localAddress, rtcpPort));
                socket2.Close();
                _logger.LogInformation($"[{callId}] Successfully force-released RTCP port {rtcpPort}");
            }

            // Zkusíme znovu vytvořit UDP klienty
            var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
            var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));

            // Nastavení většího bufferu pro UDP klienty
            rtpClient.Client.ReceiveBufferSize = 1048576; // 1MB buffer
            rtpClient.Client.SendBufferSize = 1048576;
            rtcpClient.Client.ReceiveBufferSize = 1048576;
            rtcpClient.Client.SendBufferSize = 1048576;

            _logger.LogInformation($"[{callId}] Successfully created RTP receiver after force-releasing ports: RTP={rtpPort}, RTCP={rtcpPort}");

            // Vytvořit a vrátit RtpAudioReceiver
            return new RtpAudioReceiver(callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());
        }
        catch (Exception innerEx)
        {
            _logger.LogWarning($"[{callId}] Force-release of ports failed: {innerEx.Message}");
            throw new InvalidOperationException($"Could not create RTP receiver with ports: RTP={rtpPort}, RTCP={rtcpPort}", innerEx);
        }
    }
}
```

### 2.2 Zajištění konzistence v nastavení IP adresy

Je potřeba zajistit, že všechny metody, které vytvářejí UDP klienty, používají stejnou IP adresu. Doporučujeme používat parametr `localAddress` místo `IPAddress.Any`:

```csharp
var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));
```

### 2.3 Konfigurace portů v simulátoru

Simulátor by měl umožňovat konfiguraci SIP a RTP portů pomocí parametrů příkazové řádky, jak je navrženo v dokumentu `implementation.md`:

```csharp
// Přidání UDP kanálu pro SIP komunikaci s konfigurovatelným portem
var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, localSipPort);

// ...

_rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(rtpPortMin, rtpPortMax),
});
```

### 2.4 Zajištění konzistence RTP portů v SDP a RtpAudioReceiver

Je potřeba zajistit, že porty použité v `RtpAudioReceiver` odpovídají portům, které jsou použity v SDP. Doporučujeme následující postup:

1. Vytvořit `VoIPMediaSession` s rozsahem portů z konfigurace
2. Vytvořit SDP odpověď pomocí `CreateAnswer`
3. Extrahovat RTP port z SDP odpovědi
4. Vytvořit `RtpAudioReceiver` s tímto portem

```csharp
// Vytvoření VoIPMediaSession s rozsahem portů z konfigurace
var _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(_options.RtpPortMin, _options.RtpPortMax),
});

// Vytvoření SDP odpovědi
var answerSdp = _rtpSession.CreateAnswer(offerSdp);

// Extrakce RTP portu z SDP odpovědi
int rtpPort = answerSdp.Media.First(m => m.Media == SDPMediaTypesEnum.audio).Port;
int rtcpPort = rtpPort + 1;

// Vytvoření RtpAudioReceiver s tímto portem
Func<IAudioInputReceiver> inputReceiverFactory = () => _audioInputReceiverWithPortsFactory(callId, rtpPort, rtcpPort);
```

## 3. Další doporučení

### 3.1 Logování RTP portů

Doporučujeme přidat podrobnější logování RTP portů, aby bylo možné lépe diagnostikovat problémy s RTP komunikací:

```csharp
_logger.LogInformation($"[{callId}] SDP RTP port: {rtpPort}, RTCP port: {rtcpPort}");
_logger.LogInformation($"[{callId}] RtpAudioReceiver RTP port: {rtpAudioReceiver.RtpLocalEndPoint.Port}, RTCP port: {rtpAudioReceiver.RtcpLocalEndPoint.Port}");
```

### 3.2 Testování RTP komunikace

Doporučujeme přidat testy, které ověří, že RTP komunikace funguje správně:

1. Test, který ověří, že porty v SDP odpovídají portům, na kterých server skutečně naslouchá
2. Test, který ověří, že server správně přijímá RTP pakety
3. Test, který ověří, že simulátor správně odesílá RTP pakety

### 3.3 Ošetření výjimek

Doporučujeme přidat lepší ošetření výjimek, zejména při vytváření UDP klientů a zpracování RTP paketů:

```csharp
try
{
    // Vytvoření UDP klientů
    var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
    var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));
    
    // ...
}
catch (SocketException ex)
{
    _logger.LogError(ex, $"[{callId}] Error creating UDP clients: {ex.Message} (Error code: {ex.SocketErrorCode})");
    throw;
}
catch (Exception ex)
{
    _logger.LogError(ex, $"[{callId}] Unexpected error creating UDP clients: {ex.Message}");
    throw;
}
```

## 4. Závěr

Implementace SIP a RTP komunikace v serverovém a simulátorovém projektu obsahuje několik problémů, které je potřeba řešit, aby bylo možné spustit server a simulátor na jedné stanici. Hlavním problémem je metoda `CreateReceiverWithSpecificPorts`, která ignoruje parametry `rtpPort` a `rtcpPort` a místo toho používá pevně nastavené porty. Dalším problémem je pevné nastavení portů v simulátoru.

Doporučujeme implementovat navržená řešení, která umožní konfiguraci portů a zajistí konzistenci v nastavení IP adresy a portů. Tato řešení by měla umožnit běh serveru a simulátoru na jedné stanici bez konfliktů portů.
