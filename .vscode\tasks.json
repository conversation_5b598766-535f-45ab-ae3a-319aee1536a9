// This file contains settings for launching the voice processing simulator application.
// It is used to simulate voice processing tasks, such as sending audio files to a SIP server.
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "run-voice-processing-service",
            "type": "shell",
            "command": "dotnet run --project voice-processing-service/voice-processing-service.csproj --urls http://localhost:5045",
            "group": "build",
            "isBackground": true,
            "problemMatcher": []
        }
    ]
}