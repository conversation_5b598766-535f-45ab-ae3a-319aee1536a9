using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Media;
using SIPSorcery.Net;
using SIPSorcery.Sys;
using SIPSorceryMedia.Abstractions;
using Microsoft.VisualBasic;
using NAudio.Wave;
using NAudio.Wave.SampleProviders; // For MediaFoundationResampler if used, though WaveFormatConversionStream is often sufficient for PCM.
using Org.BouncyCastle.Asn1.Ocsp;
using System.Diagnostics;

namespace voice_processing_simulator
{
    /// <summary>
    /// Simulátor pro testování.
    /// </summary>
    partial class Program
    {
        private static ILogger<Program> _logger = null!;
        private static SIPClientUserAgent _userAgent = null!;
        private static UdpClient _rtpClient = null!;
        private static CancellationTokenSource _cts = null!;
        private static string _currentWavFile = null!; // Globální proměnná pro předání cesty k WAV souboru
        private static VoIPMediaSession _rtpSession = null!; // RTP session

        static async Task Main(string[] args)
        {
            if (args.Length == 0)
            {
                ShowUsage();
                return;
            }

            // Zpracování příkazů
            switch (args[0].ToLower())
            {
                case "generate":
                    if (args.Length < 4)
                    {
                        Console.WriteLine("Usage: voice-processing-simulator generate <output_file> <duration_seconds> <tone_frequency>");
                        Console.WriteLine("Example: voice-processing-simulator generate test.wav 10 440");
                        return;
                    }
                    string outputFile = args[1];
                    int durationSeconds = int.Parse(args[2]);
                    int toneFrequency = int.Parse(args[3]);
                    WavFileGenerator.GenerateWavFile(outputFile, durationSeconds, toneFrequency);
                    break;

                case "simulate":
                    if (args.Length < 4)
                    {
                        Console.WriteLine("Usage:");
                        Console.WriteLine("  voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds] [client_sip_port] [rtp_port_start]");
                        Console.WriteLine("  voice-processing-simulator simulate microphone <server_ip> <server_port> [client_sip_port] [rtp_port_start]");
                        Console.WriteLine("Examples:");
                        Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5090 30");
                        Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5090 30 5071 25020");
                        Console.WriteLine("  voice-processing-simulator simulate microphone 127.0.0.1 5090");
                        Console.WriteLine("  voice-processing-simulator simulate microphone 127.0.0.1 5090 5072 25030");
                        return;
                    }

                    string serverIp = args[2];
                    int serverPort = int.Parse(args[3]);

                    // Parse optional parameters
                    int callDurationSeconds = 0;
                    int clientSipPort = 5070; // Default SIP client port
                    int rtpPortStart = 25000; // Default RTP port range start

                    if (IsRecording(args[1]))
                    {
                        // For microphone: args[4] = clientSipPort, args[5] = rtpPortStart
                        if (args.Length > 4) clientSipPort = int.Parse(args[4]);
                        if (args.Length > 5) rtpPortStart = int.Parse(args[5]);

                        var (wavStream, recordedSeconds) = StartRecording();
                        await SimulateCallAsync(wavStream, serverIp, serverPort, recordedSeconds, clientSipPort, rtpPortStart);
                    }
                    else
                    {
                        // For file: args[4] = callDurationSeconds, args[5] = clientSipPort, args[6] = rtpPortStart
                        if (args.Length > 4) callDurationSeconds = int.Parse(args[4]);
                        if (args.Length > 5) clientSipPort = int.Parse(args[5]);
                        if (args.Length > 6) rtpPortStart = int.Parse(args[6]);

                        string wavFile = args[1];
                        await SimulateCallAsync(wavFile, serverIp, serverPort, callDurationSeconds, clientSipPort, rtpPortStart);
                    }
                    break;

                case "concurrent":
                    if (args.Length < 5)
                    {
                        Console.WriteLine("Usage:");
                        Console.WriteLine("  voice-processing-simulator concurrent <wav_file> <server_ip> <server_port> <call_count> [call_duration_seconds] [base_sip_port] [base_rtp_port]");
                        Console.WriteLine("Examples:");
                        Console.WriteLine("  voice-processing-simulator concurrent test.wav 127.0.0.1 5090 2 30");
                        Console.WriteLine("  voice-processing-simulator concurrent test.wav 127.0.0.1 5090 2 30 5070 25000");
                        return;
                    }

                    string concurrentWavFile = args[1];
                    string concurrentServerIp = args[2];
                    int concurrentServerPort = int.Parse(args[3]);
                    int callCount = int.Parse(args[4]);

                    // Parse optional parameters for concurrent calls
                    int concurrentCallDuration = args.Length > 5 ? int.Parse(args[5]) : 30;
                    int baseSipPort = args.Length > 6 ? int.Parse(args[6]) : 5070;
                    int baseRtpPort = args.Length > 7 ? int.Parse(args[7]) : 25000;

                    await SimulateConcurrentCallsAsync(concurrentWavFile, concurrentServerIp, concurrentServerPort, callCount, concurrentCallDuration, baseSipPort, baseRtpPort);
                    break;

                default:
                    ShowUsage();
                    break;
            }
            Console.WriteLine("Press any key to exit.");
            try
            {
                Console.ReadKey();
            }
            catch (InvalidOperationException)
            {
                // Handle case when running in background job or without console
                Console.WriteLine("Running in background mode, exiting automatically.");
            }
        }

        /// <summary>
        /// Zobrazí nápovědu k použití.
        /// </summary>
        private static void ShowUsage()
        {
            Console.WriteLine("Usage:");
            Console.WriteLine("  voice-processing-simulator generate <output_file> <duration_seconds> <tone_frequency>");
            Console.WriteLine("  voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds]");
            Console.WriteLine("  voice-processing-simulator simulate microphone <server_ip> <server_port>");
            Console.WriteLine("  voice-processing-simulator concurrent <wav_file> <server_ip> <server_port> <call_count> [call_duration_seconds]");
            Console.WriteLine("Examples:");
            Console.WriteLine("  voice-processing-simulator generate test.wav 10 440");
            Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5090 30");
            Console.WriteLine("  voice-processing-simulator simulate microphone 127.0.0.1 5090");
            Console.WriteLine("  voice-processing-simulator concurrent test.wav 127.0.0.1 5090 2 30");
        }

        /// <summary>
        /// Checks if the argument is for recording from microphone.
        /// </summary>
        private static bool IsRecording(string arg1)
        {
            return arg1 == "microphone" || arg1 == "mic";
        }

        /// <summary>
        /// Records audio from the default microphone into memory and returns the WAV stream and duration.
        /// </summary>
        private static (MemoryStream wavStream, int recordedSeconds) StartRecording()
        {
            return StartRecordingMemory();
            // var memoryStream = new MemoryStream();
            // using var waveIn = new WaveInEvent()
            // {
            //     DeviceNumber = 0,
            //     WaveFormat = new WaveFormat(8000, 16, 1)
            // };
            // using var writer = new WaveFileWriter(memoryStream, waveIn.WaveFormat);

            // Console.WriteLine("Press any key to start recording...");
            // Console.ReadKey();
            // Console.WriteLine("Recording Started. Press any key to stop...");

            // var stopwatch = Stopwatch.StartNew();
            // waveIn.DataAvailable += (s, e) =>
            // {
            //     writer.Write(e.Buffer, 0, e.BytesRecorded);
            // };

            // waveIn.StartRecording();
            // Console.ReadKey();
            // waveIn.StopRecording();
            // stopwatch.Stop();
            // waveIn.Dispose();
            // Console.WriteLine("Recording Stopped.");
            // writer.Flush();
            // memoryStream.Position = 0;
            // int recordedSeconds = (int)Math.Round(stopwatch.Elapsed.TotalSeconds);
            // return (memoryStream, recordedSeconds);
        }

        /// <summary>
        /// Simuluje hovor na SIP server.
        /// </summary>
        private static async Task SimulateCallAsync(string wavFile, string serverIp, int serverPort, int callDurationSeconds, int clientSipPort = 5070, int rtpPortStart = 25000)
        {
            // Nastavení globální proměnné pro předání cesty k WAV souboru
            _currentWavFile = wavFile;

            // Vytvoření loggeru
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddFilter("Microsoft", LogLevel.Warning)
                    .AddFilter("System", LogLevel.Warning)
                    .AddFilter("SIPSorcery", LogLevel.Information)
                    .AddFilter("voice_processing_simulator", LogLevel.Information)    // Nastavení logování pro naši aplikaci
                    .AddConsole();
            });
            _logger = loggerFactory.CreateLogger<Program>();

            // Nastavení loggeru pro SIPSorcery
            SIPSorcery.LogFactory.Set(loggerFactory);

            try
            {
                // Kontrola existence WAV souboru
                if (!File.Exists(wavFile))
                {
                    _logger.LogError($"WAV file {wavFile} not found.");
                    return;
                }
                else
                {                    
                    using (var reader = new MediaFoundationReader(wavFile))
                    {
                        TimeSpan wavDuration = reader?.TotalTime ?? TimeSpan.Zero;
                        if (callDurationSeconds == 0 && wavDuration > TimeSpan.Zero)
                            callDurationSeconds = (int)wavDuration.TotalSeconds;
                    }
                }

                _logger.LogInformation($"Starting SIP simulator. Server: {serverIp}:{serverPort}, WAV file: {wavFile}, Duration: {callDurationSeconds}s");

                // Vytvoření CancellationTokenSource pro ukončení simulace
                _cts = new CancellationTokenSource();
                _cts.CancelAfter(TimeSpan.FromSeconds(callDurationSeconds + 15)); // Přidáme 15 sekund rezervu

                // Vytvoření SIP transportu
                var sipTransport = new SIPSorcery.SIP.SIPTransport();

                // Přidání UDP kanálu pro SIP komunikaci
                var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, clientSipPort);  // Port pro SIP komunikaci
                sipTransport.AddSIPChannel(sipChannel);
                _logger.LogInformation($"SIP UDP channel created on {sipChannel.ListeningEndPoint}");

                // Vytvoření VoIP media session s explicitním nastavením rozsahu portů
                // Použijeme jiný rozsah portů než server (10000-10999)
                List<AudioCodecsEnum> codecs = new List<AudioCodecsEnum> { AudioCodecsEnum.PCMU, AudioCodecsEnum.PCMA, AudioCodecsEnum.G722 };

                // MODIFIED: Change the default audio source to None if SendAudioFromStream is the primary mechanism
                // var audioSource = AudioSourcesEnum.SineWave; // Old line
                var audioSourceOptions = new AudioSourceOptions { AudioSource = AudioSourcesEnum.None }; // Use None or Silence
                AudioExtrasSource audioExtrasSource = new AudioExtrasSource(new AudioEncoder(), audioSourceOptions);

                audioExtrasSource.RestrictFormats(formats => codecs.Contains(formats.Codec));
                _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
                {
                    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
                    RtpPortRange = new PortRange(rtpPortStart, rtpPortStart + 10),
                });
                _rtpSession.AcceptRtpFromAny = true;

                // Log RTP settings
                _logger.LogInformation("Created VoIP media session");

                // Registrace handlerů pro RTP události
                _rtpSession.OnRtpPacketReceived += OnRtpPacketReceived;

                // Vytvoření SDP nabídky
                var offerSDP = _rtpSession.CreateOffer(IPAddress.Any);

                // Cíl volání
                string destination = $"sip:server@{serverIp}:{serverPort}";

                // Vytvoření SIP Client User Agenta
                _userAgent = new SIPClientUserAgent(sipTransport);

                // Registrace handlerů pro události
                _userAgent.CallTrying += (uac, resp) => _logger.LogInformation($"Call trying: {resp.StatusCode} {resp.ReasonPhrase}");
                _userAgent.CallRinging += async (uac, resp) =>
                {
                    _logger.LogInformation($"Call ringing: {resp.StatusCode} {resp.ReasonPhrase}");
                    if (resp.Status == SIPResponseStatusCodesEnum.SessionProgress && resp.Body != null)
                    {
                        var result = _rtpSession.SetRemoteDescription(SdpType.answer, SDP.ParseSDPDescription(resp.Body));
                        if (result == SetDescriptionResultEnum.OK)
                        {
                            await _rtpSession.Start();
                            _logger.LogInformation("Remote SDP set from in progress response. RTP session started.");
                        }
                    }
                };
                _userAgent.CallFailed += (uac, err, resp) =>
                {
                    _logger.LogWarning($"Call failed: {err}");
                    try
                    {
                        // Attempt to cancel. If _cts is disposed, this might throw an ObjectDisposedException.
                        _cts?.Cancel();
                    }
                    catch (ObjectDisposedException)
                    {
                        // Log that we tried to cancel a disposed CancellationTokenSource, which is expected
                        // if the call failed after the main simulation logic/cleanup has already run.
                        _logger.LogDebug("CancellationTokenSource was already disposed when CallFailed event tried to cancel.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error cancelling call.");
                    }
                };
                _userAgent.CallAnswered += async (uac, resp) =>
                {
                    if (resp.Status == SIPResponseStatusCodesEnum.Ok)
                    {
                        _logger.LogInformation($"Call answered: {resp.StatusCode} {resp.ReasonPhrase}");
                        if (resp.Body != null)
                        {
                            var result = _rtpSession.SetRemoteDescription(SdpType.answer, SDP.ParseSDPDescription(resp.Body));
                            if (result == SetDescriptionResultEnum.OK)
                            {
                                try
                                {
                                    // Get RTP endpoints from the session - important for debugging
                                    var destinationEndPoint = _rtpSession.AudioDestinationEndPoint;
                                    if (destinationEndPoint != null)
                                    {
                                        _logger.LogInformation($"RTP destination endpoint: {destinationEndPoint}");
                                        var remoteHost = destinationEndPoint.Address;
                                        var remoteRtpPort = destinationEndPoint.Port;
                                        Console.WriteLine($"Sending RTP packets to {remoteHost}:{remoteRtpPort}");
                                    }

                                    // Log SDP information
                                    var sdp = SDP.ParseSDPDescription(resp.Body);
                                    _logger.LogInformation($"SDP connection: {sdp.Connection}");
                                    foreach (var media in sdp.Media)
                                    {
                                        _logger.LogInformation($"SDP media: {media.Media} port {media.Port}");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogWarning($"Failed to get RTP endpoints: {ex.Message}");
                                }

                                // Spustíme RTP session
                                await _rtpSession.Start();
                                _logger.LogInformation("Call established successfully.");

                                // Spustíme odesílání RTP dat
                                _ = StartSendingRtpAsync();
                            }
                            else
                            {
                                _logger.LogWarning($"Failed to set remote description: {result}");
                                _userAgent.Hangup();
                            }
                        }
                        else if (!_rtpSession.IsAudioStarted)
                        {
                            _logger.LogWarning("Failed to get remote description in session progress or final response.");
                            _userAgent.Hangup();
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"Call answered with non-OK status: {resp.StatusCode} {resp.ReasonPhrase}");
                    }
                };

                // Generate unique identifiers for each call to avoid conflicts
                var uniqueId = Guid.NewGuid().ToString("N")[..8]; // Use first 8 chars of GUID
                var uniqueUsername = $"simulator_{uniqueId}";
                var uniqueFromUri = $"sip:simulator_{uniqueId}@localhost";

                _logger.LogInformation($"Creating call with unique ID: {uniqueId}");

                // Vytvoření SIP call descriptoru
                SIPCallDescriptor callDescriptor = new SIPCallDescriptor(
                    uniqueUsername,
                    null,
                    destination,
                    uniqueFromUri,
                    destination,
                    null, null, null,
                    SIPCallDirection.Out,
                    SDP.SDP_MIME_CONTENTTYPE,
                    offerSDP.ToString(),
                    null);

                // Odeslání INVITE požadavku
                _logger.LogInformation($"Sending INVITE to {destination}");
                _userAgent.Call(callDescriptor);

                // Čekání na dokončení hovoru nebo uplynutí času
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(callDurationSeconds + 10), _cts.Token);   // Přidáme 10 sekund rezervu na dokončení RTP streamu

                    // Ukončení hovoru
                    _logger.LogInformation("Call duration elapsed. Hanging up.");
                    _userAgent.Hangup();
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Call was cancelled before duration elapsed.");
                }

                // Čekání na dokončení RTP streamu
                if (_rtpSession != null)
                {
                    try
                    {
                        _rtpSession.Close("Simulation completed");
                        _logger.LogInformation("RTP session closed.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error closing RTP session.");
                    }
                }

                _logger.LogInformation("Simulation completed.");
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Simulation was cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SIP simulator.");
            }
            finally
            {
                // Úklid
                _cts?.Dispose();
                _rtpSession?.Close("Cleanup");
                _rtpClient?.Close();
                _rtpClient?.Dispose();
            }
        }

        /// <summary>
        /// Handler pro příjem RTP paketů.
        /// </summary>
        private static void OnRtpPacketReceived(IPEndPoint remoteEndPoint, SDPMediaTypesEnum mediaType, RTPPacket rtpPacket)
        {
            _logger.LogDebug($"RTP packet received from {remoteEndPoint}, payload type: {rtpPacket.Header.PayloadType}, sequence: {rtpPacket.Header.SequenceNumber}");
        }

        /// <summary>
        /// Spustí odesílání RTP streamu.
        /// </summary>
        private static async Task StartSendingRtpAsync()
        {
            try
            {
                _logger.LogInformation($"Starting to send RTP stream from {_currentWavFile}");

                // --- Define the TARGET PCM format for SIPSorcery ---
                // This should match what SIPSorcery's AudioEncoder expects as input
                // for the chosen AudioSamplingRatesEnum (e.g., PCMU/PCMA often use 8kHz, 16-bit mono PCM).
                const int targetSampleRateHz = 8000;
                const int targetChannels = 1;       // Mono
                const int targetBitsPerSample = 16;  // SIPSorcery typically expects 16-bit linear PCM
                var targetWaveFormat = new WaveFormat(targetSampleRateHz, targetBitsPerSample, targetChannels);
                var targetRtpSampleRateEnum = AudioSamplingRatesEnum.Rate8KHz; // This MUST match targetSampleRateHz

                _logger.LogInformation($"Target PCM format for SIP Server: {targetWaveFormat.Encoding}, {targetWaveFormat.SampleRate}Hz, {targetWaveFormat.Channels} channels, {targetWaveFormat.BitsPerSample} bits/sample");

                using var pcmStream = new MemoryStream(); // This will hold the final PCM data for SIPSorcery

                // NAudio processing chain:
                // 1. WaveFileReader (reads original WAV)
                // 2. (Optional) WaveFormatConversionStream (if original is not PCM, or to change bit depth/channels before resampling)
                // 3. (Optional) MediaFoundationResampler (to change sample rate)
                // The output of the final stage is written to pcmStream.
                
                using (var reader = new WaveFileReader(_currentWavFile))
                {
                    _logger.LogInformation($"Source WAV file details: Format {reader.WaveFormat.Encoding}, {reader.WaveFormat.SampleRate}Hz, {reader.WaveFormat.Channels} channels, {reader.WaveFormat.BitsPerSample} bits/sample");

                    if (reader.Length == uint.MaxValue)
                    {
                        _logger.LogWarning($"Source WAV file header for '{_currentWavFile}' reports a data length of 0x{uint.MaxValue:X}. This is likely incorrect. NAudio will read available data.");
                    }

                    // Step 1: Ensure the stream is PCM and matches target bit depth and channels *before* resampling sample rate.
                    // MediaFoundationResampler works best if the input is already close in bit depth/channels.
                    if (reader.WaveFormat.Encoding != WaveFormatEncoding.Pcm ||
                        reader.WaveFormat.BitsPerSample != targetWaveFormat.BitsPerSample ||
                        reader.WaveFormat.Channels != targetWaveFormat.Channels)
                    {
                        _logger.LogInformation($"Converting source WAV ({reader.WaveFormat.Encoding}, {reader.WaveFormat.BitsPerSample}bit, {reader.WaveFormat.Channels}ch) to target PCM format ({targetWaveFormat.Encoding}, {targetWaveFormat.BitsPerSample}bit, {targetWaveFormat.Channels}ch) before potential sample rate resampling.");
                        // Create a format for an intermediate PCM stream that matches target channels and bit depth, but original sample rate
                        var intermediatePcmFormat = new WaveFormat(reader.WaveFormat.SampleRate, targetWaveFormat.BitsPerSample, targetWaveFormat.Channels);
                        using (var conversionStream = new WaveFormatConversionStream(intermediatePcmFormat, reader))
                        {
                            // If only sample rate needs changing now
                            if (conversionStream.WaveFormat.SampleRate != targetWaveFormat.SampleRate)
                            {
                                _logger.LogInformation($"Resampling from {conversionStream.WaveFormat.SampleRate}Hz to {targetWaveFormat.SampleRate}Hz.");
                                using (var resampler = new MediaFoundationResampler(conversionStream, targetWaveFormat) { ResamplerQuality = 60 })
                                {
                                    WaveFileWriter.WriteWavFileToStream(pcmStream, resampler); // Write all resampled data
                                }
                            }
                            else
                            {
                                _logger.LogInformation("Audio is now in target format after initial conversion (no sample rate change needed).");
                                WaveFileWriter.WriteWavFileToStream(pcmStream, conversionStream); // Write all converted data
                            }
                        }
                    }
                    // Step 2: If already PCM and correct bit depth/channels, just check sample rate
                    else if (reader.WaveFormat.SampleRate != targetWaveFormat.SampleRate)
                    {
                        _logger.LogInformation($"Source is {reader.WaveFormat.Encoding.ToString().ToUpper()} with correct channels/bit depth, but needs resampling from {reader.WaveFormat.SampleRate}Hz to {targetWaveFormat.SampleRate}Hz.");
                        using (var resampler = new MediaFoundationResampler(reader, targetWaveFormat) { ResamplerQuality = 60 })
                        {
                            WaveFileWriter.WriteWavFileToStream(pcmStream, resampler); // Write all resampled data
                        }
                    }
                    // Step 3: Already in the perfect target format
                    else
                    {
                        _logger.LogInformation("Source audio is already in the exact target PCM format. Copying directly.");
                        WaveFileWriter.WriteWavFileToStream(pcmStream, reader); // Write all original data
                    }
                } // reader is disposed here

                pcmStream.Position = 0; // Reset stream position for SendAudioFromStream

                if (pcmStream.Length == 0)
                {
                    _logger.LogWarning("PCM stream is empty after processing. Nothing to send.");
                    return;
                }

                double pcmStreamDurationSec = (double)pcmStream.Length / targetWaveFormat.AverageBytesPerSecond;
                _logger.LogInformation($"Prepared PCM stream: Length {pcmStream.Length} bytes, Calculated Duration {pcmStreamDurationSec:F2}s.");

                // Explicitly confirm we're sending with the right sampling rate
                //_logger.LogInformation($"Sending WAV file via RTP using 8KHz sampling rate...");

                // Získání cílového endpointu pro RTP
                // var destinationEndPoint = _rtpSession.AudioDestinationEndPoint;
                // if (destinationEndPoint == null)
                // {
                //     _logger.LogError("Cannot send RTP stream: AudioDestinationEndPoint is null");
                //     return;
                // }

                //_logger.LogInformation($"Sending RTP stream to {destinationEndPoint}");

                // Odeslání audio dat přes RTP session
                //await _rtpSession.AudioExtrasSource.SendAudioFromStream(fileStream, AudioSamplingRatesEnum.Rate8KHz);

                _logger.LogInformation($"Sending {pcmStream.Length} bytes of PCM data ({targetWaveFormat.SampleRate}Hz, {targetWaveFormat.Channels}ch, {targetWaveFormat.BitsPerSample}bit) via RTP using sample rate {(int)targetRtpSampleRateEnum} Hz.");

                // Pass the resampled PCM stream and the correct sample rate enum
                await _rtpSession.AudioExtrasSource.SendAudioFromStream(pcmStream, targetRtpSampleRateEnum);

                _logger.LogInformation("RTP stream sending completed.");
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("RTP stream sending was cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending RTP stream.");
            }
        }

        /// <summary>
        /// Simuluje více současných hovorů na SIP server pro testování session sharing.
        /// Všechny hovory používají stejný SIP endpoint (IP:port) ale různé Call-ID a RTP porty.
        /// </summary>
        private static async Task SimulateConcurrentCallsAsync(string wavFile, string serverIp, int serverPort, int callCount, int callDurationSeconds, int baseSipPort, int baseRtpPort)
        {
            var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<Program>();
            logger.LogInformation($"Starting {callCount} concurrent calls from SAME endpoint to {serverIp}:{serverPort} using {wavFile}");

            // Create single SIP transport that will be shared by all calls
            var sipTransport = new SIPSorcery.SIP.SIPTransport();
            var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, baseSipPort);
            sipTransport.AddSIPChannel(sipChannel);
            logger.LogInformation($"Created shared SIP transport on {sipChannel.ListeningEndPoint}");

            var callTasks = new List<Task>();
            var cancellationTokenSource = new CancellationTokenSource();
            cancellationTokenSource.CancelAfter(TimeSpan.FromSeconds(callDurationSeconds + 30)); // Safety timeout

            for (int i = 0; i < callCount; i++)
            {
                int callIndex = i;
                int rtpPort = baseRtpPort + (callIndex * 20); // Different RTP ports for each call
                string callerId = $"simulator_{Guid.NewGuid().ToString("N")[..8]}"; // Unique caller ID

                var task = Task.Run(async () =>
                {
                    try
                    {
                        logger.LogInformation($"Starting concurrent call #{callIndex + 1} with caller {callerId}, RTP port {rtpPort}");
                        await SimulateSingleCallFromSharedTransportAsync(wavFile, serverIp, serverPort, callDurationSeconds,
                            sipTransport, callerId, rtpPort, cancellationTokenSource.Token, logger);
                        logger.LogInformation($"Completed concurrent call #{callIndex + 1}");
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, $"Error in concurrent call #{callIndex + 1}: {ex.Message}");
                    }
                });

                callTasks.Add(task);

                // Small delay between starting calls to avoid race conditions
                if (i < callCount - 1)
                {
                    await Task.Delay(1000); // 1 second delay
                }
            }

            logger.LogInformation($"All {callCount} concurrent calls started from same endpoint. Waiting for completion...");

            try
            {
                await Task.WhenAll(callTasks);
                logger.LogInformation($"All {callCount} concurrent calls completed successfully.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error waiting for concurrent calls: {ex.Message}");
            }
            finally
            {
                // Cleanup
                sipTransport?.Shutdown();
                cancellationTokenSource?.Dispose();
            }
        }

        /// <summary>
        /// Simuluje jeden hovor pomocí sdíleného SIP transportu.
        /// </summary>
        private static async Task SimulateSingleCallFromSharedTransportAsync(
            string wavFile,
            string serverIp,
            int serverPort,
            int callDurationSeconds,
            SIPSorcery.SIP.SIPTransport sipTransport,
            string callerId,
            int rtpPortStart,
            CancellationToken cancellationToken,
            ILogger logger)
        {
            SIPClientUserAgent? userAgent = null;
            VoIPMediaSession? rtpSession = null;

            try
            {
                // Create unique call ID
                var callId = Guid.NewGuid().ToString();
                logger.LogInformation($"[{callerId}] Starting call with ID: {callId}");

                // Create VoIP media session with unique RTP ports
                List<AudioCodecsEnum> codecs = new List<AudioCodecsEnum> { AudioCodecsEnum.PCMU, AudioCodecsEnum.PCMA, AudioCodecsEnum.G722 };
                var audioSourceOptions = new AudioSourceOptions { AudioSource = AudioSourcesEnum.None };
                AudioExtrasSource audioExtrasSource = new AudioExtrasSource(new AudioEncoder(), audioSourceOptions);
                audioExtrasSource.RestrictFormats(formats => codecs.Contains(formats.Codec));

                rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
                {
                    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
                    RtpPortRange = new PortRange(rtpPortStart, rtpPortStart + 10),
                });

                logger.LogInformation($"[{callerId}] Created VoIP media session with RTP ports {rtpPortStart}-{rtpPortStart + 10}");

                // Create SIP URI for the call
                var sipUri = SIPURI.ParseSIPURI($"sip:server@{serverIp}:{serverPort}");
                userAgent = new SIPClientUserAgent(sipTransport);

                // Set up call events
                userAgent.CallTrying += (uac, resp) => logger.LogInformation($"[{callerId}] Call trying.");
                userAgent.CallRinging += async (uac, resp) =>
                {
                    logger.LogInformation($"[{callerId}] Call ringing.");
                    if (resp.Status == SIPResponseStatusCodesEnum.SessionProgress && resp.Body != null)
                    {
                        var result = rtpSession.SetRemoteDescription(SdpType.answer, SDP.ParseSDPDescription(resp.Body));
                        if (result == SetDescriptionResultEnum.OK)
                        {
                            await rtpSession.Start();
                            logger.LogInformation($"[{callerId}] RTP session started from progress response.");
                        }
                    }
                };
                userAgent.CallAnswered += async (uac, resp) =>
                {
                    if (resp.Status == SIPResponseStatusCodesEnum.Ok)
                    {
                        logger.LogInformation($"[{callerId}] Call answered.");
                        if (resp.Body != null)
                        {
                            var result = rtpSession.SetRemoteDescription(SdpType.answer, SDP.ParseSDPDescription(resp.Body));
                            if (result == SetDescriptionResultEnum.OK)
                            {
                                await rtpSession.Start();
                                logger.LogInformation($"[{callerId}] RTP session started.");
                            }
                        }
                    }
                };
                userAgent.CallFailed += (uac, err, resp) => logger.LogError($"[{callerId}] Call failed: {err}");

                // Create SDP offer
                var offerSDP = rtpSession.CreateOffer(IPAddress.Any);

                // Create call descriptor with unique caller ID
                var destination = $"sip:server@{serverIp}:{serverPort}";
                var uniqueFromUri = $"sip:{callerId}@localhost";

                SIPCallDescriptor callDescriptor = new SIPCallDescriptor(
                    callerId,
                    null,
                    destination,
                    uniqueFromUri,
                    destination,
                    null, null, null,
                    SIPCallDirection.Out,
                    SDP.SDP_MIME_CONTENTTYPE,
                    offerSDP.ToString(),
                    null);

                // Start the call
                logger.LogInformation($"[{callerId}] Sending INVITE to {destination}");
                userAgent.Call(callDescriptor);

                // Start sending audio task (will wait for RTP session to start)
                var audioTask = Task.Run(async () =>
                {
                    try
                    {
                        // Wait for RTP session to start
                        var timeout = DateTime.Now.AddSeconds(10);
                        while (!rtpSession.IsAudioStarted && DateTime.Now < timeout && !cancellationToken.IsCancellationRequested)
                        {
                            await Task.Delay(100, cancellationToken);
                        }

                        if (rtpSession.IsAudioStarted)
                        {
                            logger.LogInformation($"[{callerId}] RTP session started, beginning audio transmission");
                            await SendAudioFromWavFileAsync(wavFile, rtpSession, callDurationSeconds, cancellationToken, logger, callerId);
                        }
                        else
                        {
                            logger.LogWarning($"[{callerId}] RTP session failed to start within timeout");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, $"[{callerId}] Error sending audio: {ex.Message}");
                    }
                });

                // Wait for call duration or cancellation
                var delayTask = Task.Delay(TimeSpan.FromSeconds(callDurationSeconds + 5), cancellationToken);
                await Task.WhenAny(audioTask, delayTask);

                logger.LogInformation($"[{callerId}] Call duration completed, hanging up");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"[{callerId}] Error in call simulation: {ex.Message}");
            }
            finally
            {
                // Cleanup
                try
                {
                    userAgent?.Hangup();
                    rtpSession?.Close("Call completed");
                    logger.LogInformation($"[{callerId}] Call cleanup completed");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"[{callerId}] Error during cleanup: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Sends audio from WAV file to RTP session with caller ID logging.
        /// </summary>
        private static async Task SendAudioFromWavFileAsync(string wavFile, VoIPMediaSession rtpSession, int durationSeconds, CancellationToken cancellationToken, ILogger logger, string callerId)
        {
            try
            {
                logger.LogInformation($"[{callerId}] Starting to send audio from {wavFile}");

                // Create a memory stream with the audio data
                using var pcmStream = new MemoryStream();

                // Read and convert the WAV file to the target format
                using (var reader = new AudioFileReader(wavFile))
                {
                    var targetWaveFormat = new WaveFormat(8000, 16, 1); // 8kHz, 16-bit, mono

                    if (reader.WaveFormat.Equals(targetWaveFormat))
                    {
                        // Already in target format
                        WaveFileWriter.WriteWavFileToStream(pcmStream, reader);
                    }
                    else
                    {
                        // Resample to target format
                        using var resampler = new MediaFoundationResampler(reader, targetWaveFormat);
                        WaveFileWriter.WriteWavFileToStream(pcmStream, resampler);
                    }
                }

                pcmStream.Position = 0; // Reset for reading

                if (pcmStream.Length == 0)
                {
                    logger.LogWarning($"[{callerId}] PCM stream is empty. Nothing to send.");
                    return;
                }

                logger.LogInformation($"[{callerId}] Sending {pcmStream.Length} bytes of audio data via RTP");

                // Send the audio stream
                await rtpSession.AudioExtrasSource.SendAudioFromStream(pcmStream, AudioSamplingRatesEnum.Rate8KHz);

                logger.LogInformation($"[{callerId}] Audio sending completed.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"[{callerId}] Error sending audio: {ex.Message}");
            }
        }
    }
}
