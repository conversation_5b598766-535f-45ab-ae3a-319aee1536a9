version: '3.8'

services:
  phonexia-api:
    image: phonexia-linux-demo:1.1
    container_name: phonexia-test
    ports:
      - "8600:8600"
    command: "./phxspe"
    restart: unless-stopped

  voice-processing-service:
    image: voice-processing-service:1.2.3
    container_name: voice-processing-service
    depends_on:
      - phonexia-api
    restart: unless-stopped
    ports:
      # SIP signaling port (updated default)
      - "5090:5090/udp"
      # RTP media ports range (adjust as needed for your environment)
      - "10000-10100:10000-10100/udp"
      # HTTP API port (updated default)
      - "8081:8081"
    volumes:
      # Persistent recordings
      - voice-recordings:/app/recordings
      # Optional: mount configuration from host
      - ./voice-processing-service/appsettings.Production.json:/app/appsettings.Production.json:ro
    environment:
      # Application environment
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8081

      # Application configuration
      - RECORDINGS_PATH=/app/recordings
      # Phonexia API configuration for container-to-container communication (standard ASP.NET Core format)
      - Phonexia__ApiUrl=http://phonexia-api:8600

      # SIP Server configuration (using new defaults - standard ASP.NET Core format)
      - SipServer__ListenIpAddress=Any
      - SipServer__ListenPort=5090
      - SipServer__RtpPortMin=10000
      - SipServer__RtpPortMax=10100

      # Phonexia API configuration (local container - standard ASP.NET Core format)
      - Phonexia__Username=admin
      - Phonexia__Password=phonexia
      - Phonexia__Language=cs-CZ
      - Phonexia__Model=CS_CZ_O2_6
      - Phonexia__ChunkSizeBytes=8000
    networks:
      - voice-network
    depends_on:
      - phonexia
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Local Phonexia container for development
  phonexia:
    image: phonexia/speech-platform:latest
    container_name: phonexia-server
    restart: unless-stopped
    ports:
      - "8600:8600"
    environment:
      - PHONEXIA_LICENSE_KEY=${PHONEXIA_LICENSE_KEY:-}
      - PHONEXIA_USERNAME=admin
      - PHONEXIA_PASSWORD=phonexia
    volumes:
      - phonexia-data:/opt/phonexia/data
      - phonexia-models:/opt/phonexia/models
    networks:
      - voice-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8600/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  voice-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  # Persistent storage for recordings
  voice-recordings:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/recordings
  
  # Phonexia data volumes
  phonexia-data:
    driver: local
  
  phonexia-models:
    driver: local
