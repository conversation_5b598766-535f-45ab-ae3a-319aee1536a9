using Microsoft.Extensions.Logging;
using NAudio.Wave;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;

namespace voice_processing_service.Tests.Simulators.PhonexiaLoadSimulator;

public class Program
{
    private static readonly HttpClient _httpClient = new();
    private static ILogger<Program> _logger = null!;

    public static async Task Main(string[] args)
    {
        // Setup logging with timestamp format matching middleware
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddSimpleConsole(options =>
            {
                options.TimestampFormat = "[HH:mm:ss] ";
            }).SetMinimumLevel(LogLevel.Information));
        _logger = loggerFactory.CreateLogger<Program>();

        // Parse command line arguments
        var config = ParseArguments(args);
        
        _logger.LogInformation("=== Phonexia Load Simulator Starting ===");
        _logger.LogInformation($"Target: http://{config.ServerIp}:{config.ServerPort}");
        _logger.LogInformation($"Concurrent calls: {config.Count}");
        _logger.LogInformation($"Duration: {config.Duration}s");
        _logger.LogInformation($"Audio source: {config.WavFile}");

        // Validate WAV file exists - try multiple paths
        var wavFilePath = config.WavFile;
        _logger.LogInformation($"Looking for WAV file: {config.WavFile}");
        _logger.LogInformation($"Current directory: {Directory.GetCurrentDirectory()}");

        if (!File.Exists(wavFilePath))
        {
            _logger.LogInformation($"WAV file not found at: {Path.GetFullPath(wavFilePath)}");

            // Find the solution root by looking for voice-processing-service.sln
            var currentDir = Directory.GetCurrentDirectory();
            var solutionRoot = currentDir;

            // Go up directories until we find the solution file
            while (!File.Exists(Path.Combine(solutionRoot, "voice-processing-service.sln")) &&
                   Directory.GetParent(solutionRoot) != null)
            {
                solutionRoot = Directory.GetParent(solutionRoot)!.FullName;
            }

            wavFilePath = Path.Combine(solutionRoot, config.WavFile);
            _logger.LogInformation($"Trying solution root: {Path.GetFullPath(wavFilePath)}");

            if (!File.Exists(wavFilePath))
            {
                _logger.LogError($"WAV file not found in solution root: {config.WavFile}");
                return;
            }
        }

        _logger.LogInformation($"Using WAV file: {Path.GetFullPath(wavFilePath)}");

        // Create a shared session for all concurrent calls (as per Phonexia documentation)
        var cts = new CancellationTokenSource();

        _logger.LogInformation("Creating shared Phonexia session for all concurrent calls...");
        var sharedSessionId = await CreateSharedSession(config, cts.Token);
        if (sharedSessionId == null)
        {
            _logger.LogError("Failed to create shared session. Exiting.");
            return;
        }

        _logger.LogInformation($"Shared session created: {sharedSessionId}");
        _logger.LogInformation("Starting concurrent calls with shared session...");

        // Start concurrent calls using the shared session
        var tasks = new List<Task>();
        for (int i = 1; i <= config.Count; i++)
        {
            var callId = $"Call-{i}";
            tasks.Add(Task.Run(() => ExecutePhonexiaCallWithSharedSession(callId, config, sharedSessionId, wavFilePath, cts.Token)));
        }

        // Wait for specified duration
        await Task.Delay(TimeSpan.FromSeconds(config.Duration), cts.Token);
        
        _logger.LogInformation("Duration elapsed. Stopping all calls...");
        cts.Cancel();

        // Wait for all tasks to complete
        await Task.WhenAll(tasks);
        
        _logger.LogInformation("=== Phonexia Load Simulator Completed ===");
    }

    private static SimulatorConfig ParseArguments(string[] args)
    {
        var config = new SimulatorConfig();
        
        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i])
            {
                case "--server_ip" when i + 1 < args.Length:
                    config.ServerIp = args[++i];
                    break;
                case "--server_port" when i + 1 < args.Length:
                    config.ServerPort = int.Parse(args[++i]);
                    break;
                case "--wav_file" when i + 1 < args.Length:
                    config.WavFile = args[++i];
                    break;
                case "--count" when i + 1 < args.Length:
                    config.Count = int.Parse(args[++i]);
                    break;
                case "--duration" when i + 1 < args.Length:
                    config.Duration = int.Parse(args[++i]);
                    break;
            }
        }
        
        return config;
    }

    private static async Task<string?> CreateSharedSession(SimulatorConfig config, CancellationToken cancellationToken)
    {
        try
        {
            var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes("admin:phonexia"));
            var loginRequest = new HttpRequestMessage(HttpMethod.Post, $"http://{config.ServerIp}:{config.ServerPort}/login");
            loginRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authHeader);

            _logger.LogInformation("Sending shared session login request to Phonexia API...");
            var response = await _httpClient.SendAsync(loginRequest, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogInformation($"Shared session login response: {responseJson}");

            using var doc = JsonDocument.Parse(responseJson);

            if (doc.RootElement.TryGetProperty("result", out var result) &&
                result.TryGetProperty("session", out var session) &&
                session.TryGetProperty("id", out var sessionId))
            {
                var extractedSessionId = sessionId.GetString();
                _logger.LogInformation($"Extracted shared session ID: '{extractedSessionId}' (length: {extractedSessionId?.Length})");
                return extractedSessionId;
            }

            _logger.LogError("Failed to extract session ID from response");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Shared session creation failed");
            return null;
        }
    }

    private static async Task ExecutePhonexiaCallWithSharedSession(string callId, SimulatorConfig config, string sharedSessionId, string wavFilePath, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation($"[{callId}] Starting with shared session: {sharedSessionId}");

            // Step 1: Create audio stream using shared session
            var (audioWebSocket, streamId) = await CreateAudioStreamWithWebSocket(callId, config, sharedSessionId, cancellationToken);
            if (audioWebSocket == null || streamId == null)
            {
                _logger.LogError($"[{callId}] Failed to create audio stream");
                return;
            }

            _logger.LogInformation($"[{callId}] Audio WebSocket connected. Stream: {streamId}");

            // Step 2: Bind STT to stream using shared session
            var taskId = await BindSttToStream(callId, config, sharedSessionId, streamId, cancellationToken);
            if (taskId == null)
            {
                _logger.LogError($"[{callId}] Failed to bind STT to stream");
                audioWebSocket.Dispose();
                return;
            }

            _logger.LogInformation($"[{callId}] STT bound to stream. Task: {taskId}");

            // Step 3: Connect to STT results WebSocket using shared session
            var resultsWebSocket = await ConnectResultsWebSocket(callId, config, sharedSessionId, taskId, cancellationToken);
            if (resultsWebSocket == null)
            {
                _logger.LogError($"[{callId}] Failed to connect to STT results WebSocket");
                audioWebSocket.Dispose();
                return;
            }

            _logger.LogInformation($"[{callId}] STT results WebSocket handshake complete.");

            // Step 4: Start listening for STT results
            var resultsTask = Task.Run(() => ReceiveResultsWithWebSocket(callId, resultsWebSocket, cancellationToken));

            // Step 5: Send audio data
            await SendAudioDataWithWebSocket(callId, config, audioWebSocket, wavFilePath, cancellationToken);

            // Step 6: Wait for processing to complete or timeout
            await resultsTask;

            // Step 7: Cleanup
            await CleanupPhonexiaSession(callId, config, sharedSessionId, taskId, audioWebSocket, resultsWebSocket);

            _logger.LogInformation($"[{callId}] Call completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] Call failed: {ex.Message}");
        }
    }

    private static async Task ExecutePhonexiaCall(string callId, SimulatorConfig config, string wavFilePath, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation($"[{callId}] Starting Phonexia session...");

            // Step 1: Login to Phonexia
            var sessionId = await LoginToPhonexia(callId, config, cancellationToken);
            if (sessionId == null)
            {
                _logger.LogError($"[{callId}] Failed to login to Phonexia");
                return;
            }

            _logger.LogInformation($"[{callId}] Login successful: {sessionId}");

            // Step 2: Create audio WebSocket stream and keep it open
            var (audioWebSocket, streamId) = await CreateAudioStreamWithWebSocket(callId, config, sessionId, cancellationToken);
            if (audioWebSocket == null || streamId == null)
            {
                _logger.LogError($"[{callId}] Failed to create audio stream");
                return;
            }

            _logger.LogInformation($"[{callId}] Audio WebSocket connected. Stream: {streamId}");

            ClientWebSocket? resultsWebSocket = null;
            string? taskId = null;

            try
            {
                // Step 3: Bind STT to stream (while WebSocket is still open)
                taskId = await BindSttToStream(callId, config, sessionId, streamId, cancellationToken);
                if (taskId == null)
                {
                    _logger.LogError($"[{callId}] Failed to bind STT to stream");
                    return;
                }

                _logger.LogInformation($"[{callId}] STT bound to stream. Task: {taskId}");

                // Step 4: Start results WebSocket and keep reference
                resultsWebSocket = await ConnectResultsWebSocket(callId, config, sessionId, taskId, cancellationToken);
                if (resultsWebSocket == null)
                {
                    _logger.LogError($"[{callId}] Failed to connect results WebSocket");
                    return;
                }

                // Step 5: Start results processing task
                var resultsTask = Task.Run(() => ReceiveResultsWithWebSocket(callId, resultsWebSocket, cancellationToken));

                // Step 6: Send audio data using the existing WebSocket
                await SendAudioDataWithWebSocket(callId, config, audioWebSocket, wavFilePath, cancellationToken);

                // Step 7: Wait for results task to complete or timeout
                try
                {
                    await resultsTask;
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation($"[{callId}] Results processing cancelled");
                }
            }
            finally
            {
                // Step 8: Comprehensive cleanup following PhonexiaSttProcessor pattern
                await CleanupPhonexiaSession(callId, config, sessionId, taskId, audioWebSocket, resultsWebSocket);
            }

            _logger.LogInformation($"[{callId}] Call completed successfully");
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation($"[{callId}] Call cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] Call failed");
        }
    }

    private static async Task<string?> LoginToPhonexia(string callId, SimulatorConfig config, CancellationToken cancellationToken)
    {
        try
        {
            var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes("admin:phonexia"));
            var loginRequest = new HttpRequestMessage(HttpMethod.Post, $"http://{config.ServerIp}:{config.ServerPort}/login");
            loginRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authHeader);

            _logger.LogInformation($"[{callId}] Sending login request to Phonexia API...");
            var response = await _httpClient.SendAsync(loginRequest, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogInformation($"[{callId}] Login response: {responseJson}");

            using var doc = JsonDocument.Parse(responseJson);

            if (doc.RootElement.TryGetProperty("result", out var result) &&
                result.TryGetProperty("session", out var session) &&
                session.TryGetProperty("id", out var sessionId))
            {
                var sessionIdValue = sessionId.GetString();
                _logger.LogInformation($"[{callId}] Extracted session ID: '{sessionIdValue}' (length: {sessionIdValue?.Length})");
                return sessionIdValue;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] Login failed");
            return null;
        }
    }

    private static async Task<(ClientWebSocket?, string?)> CreateAudioStreamWithWebSocket(string callId, SimulatorConfig config, string sessionId, CancellationToken cancellationToken)
    {
        ClientWebSocket? ws = null;
        try
        {
            var wsUri = new Uri($"ws://{config.ServerIp}:{config.ServerPort}/input_stream/websocket?frequency=8000&n_channels=1");
            ws = new ClientWebSocket();

            // Set headers matching PhonexiaSttProcessor
            var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes("admin:phonexia"));
            ws.Options.SetRequestHeader("Accept", "application/json");
            ws.Options.SetRequestHeader("X-SessionID", sessionId);
            ws.Options.SetRequestHeader("Authorization", "Basic " + authHeader);

            _logger.LogInformation($"[{callId}] Connecting to audio WebSocket with session ID: {sessionId}");
            await ws.ConnectAsync(wsUri, cancellationToken);

            // Read handshake message
            var buffer = new byte[8192];
            var result = await ws.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);
            var handshakeMessage = Encoding.UTF8.GetString(buffer, 0, result.Count);
            _logger.LogInformation($"[{callId}] Audio WebSocket handshake message: {handshakeMessage}");

            using var doc = JsonDocument.Parse(handshakeMessage);
            if (doc.RootElement.TryGetProperty("result", out var resultElement) &&
                resultElement.TryGetProperty("input_stream", out var streamIdElement))
            {
                var streamId = streamIdElement.GetString();

                // Return both WebSocket and stream ID - keep WebSocket open!
                return (ws, streamId);
            }

            return (null, null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] Create audio stream failed");
            ws?.Dispose();
            return (null, null);
        }
    }

    private static async Task<string?> BindSttToStream(string callId, SimulatorConfig config, string sessionId, string streamId, CancellationToken cancellationToken)
    {
        try
        {
            var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes("admin:phonexia"));
            var request = new HttpRequestMessage(HttpMethod.Post,
                $"http://{config.ServerIp}:{config.ServerPort}/technologies/stt/input_stream?input_stream={streamId}&model=CS_CZ_O2_6");
            request.Headers.Add("X-SessionID", sessionId);
            request.Headers.Add("Authorization", "Basic " + authHeader);

            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
            using var doc = JsonDocument.Parse(responseJson);

            if (doc.RootElement.TryGetProperty("result", out var result) &&
                result.TryGetProperty("stream_task_info", out var taskInfo) &&
                taskInfo.TryGetProperty("id", out var taskId))
            {
                return taskId.GetString();
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] STT binding failed");
            return null;
        }
    }



    private static async Task SendAudioDataWithWebSocket(string callId, SimulatorConfig config, ClientWebSocket audioWebSocket, string wavFilePath, CancellationToken cancellationToken)
    {
        try
        {
            // Read and convert WAV file
            var audioData = ConvertWavToPcm(wavFilePath);
            if (audioData == null)
            {
                _logger.LogError($"[{callId}] Failed to convert WAV file");
                return;
            }

            _logger.LogInformation($"[{callId}] Converted audio: {audioData.Length} bytes, sending via existing WebSocket...");

            // Send audio data in chunks using the existing WebSocket
            const int chunkSize = 1600; // 100ms at 8kHz 16-bit mono
            for (int i = 0; i < audioData.Length && !cancellationToken.IsCancellationRequested; i += chunkSize)
            {
                var chunk = new ArraySegment<byte>(audioData, i, Math.Min(chunkSize, audioData.Length - i));
                await audioWebSocket.SendAsync(chunk, WebSocketMessageType.Binary, true, cancellationToken);

                // Simulate real-time sending (100ms chunks)
                await Task.Delay(100, cancellationToken);
            }

            _logger.LogInformation($"[{callId}] Audio data sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] Send audio data failed");
        }
    }

    private static byte[]? ConvertWavToPcm(string wavFilePath)
    {
        try
        {
            _logger.LogInformation($"Converting WAV file: {wavFilePath}");
            _logger.LogInformation($"File exists: {File.Exists(wavFilePath)}");
            if (File.Exists(wavFilePath))
            {
                var fileInfo = new FileInfo(wavFilePath);
                _logger.LogInformation($"File size: {fileInfo.Length} bytes");
            }

            using var reader = new WaveFileReader(wavFilePath);
            _logger.LogInformation($"WAV format: {reader.WaveFormat.SampleRate}Hz, {reader.WaveFormat.Channels} channels, {reader.WaveFormat.BitsPerSample} bits");

            // Convert to PCM 8kHz 16-bit mono if needed
            WaveStream waveStream = reader;

            if (reader.WaveFormat.SampleRate != 8000 || reader.WaveFormat.Channels != 1 || reader.WaveFormat.BitsPerSample != 16)
            {
                var targetFormat = new WaveFormat(8000, 16, 1);
                waveStream = new WaveFormatConversionStream(targetFormat, reader);
            }

            // Read all audio data
            using var memoryStream = new MemoryStream();
            waveStream.CopyTo(memoryStream);
            var buffer = memoryStream.ToArray();

            _logger.LogInformation($"Converted audio data: {buffer.Length} bytes");
            return buffer;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to convert WAV file: {wavFilePath}");
            return null;
        }
    }

    private static async Task CleanupPhonexiaSession(string callId, SimulatorConfig config, string? sessionId, string? taskId,
        ClientWebSocket? audioWebSocket, ClientWebSocket? resultsWebSocket)
    {
        _logger.LogInformation($"[{callId}] Starting comprehensive cleanup...");

        try
        {
            // Step 1: Stop STT task (following PhonexiaSttProcessor.StopStreamAsync pattern)
            if (!string.IsNullOrEmpty(taskId) && !string.IsNullOrEmpty(sessionId))
            {
                try
                {
                    _logger.LogInformation($"[{callId}] Stopping STT task: {taskId}");
                    using var httpClient = new HttpClient();
                    var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes("admin:phonexia"));
                    var deleteRequest = new HttpRequestMessage(HttpMethod.Delete,
                        $"http://{config.ServerIp}:{config.ServerPort}/technologies/stt/input_stream?task={taskId}");
                    deleteRequest.Headers.Add("X-SessionID", sessionId);
                    deleteRequest.Headers.Add("Authorization", "Basic " + authHeader);

                    var response = await httpClient.SendAsync(deleteRequest, CancellationToken.None);
                    _logger.LogInformation($"[{callId}] STT task deletion response: {response.StatusCode}");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"[{callId}] Error stopping STT task: {ex.Message}");
                }
            }

            // Step 2: Cleanup WebSockets (following PhonexiaSttProcessor.CleanupWebSocketsAsync pattern)
            await CleanupWebSocketsAsync(callId, audioWebSocket, resultsWebSocket);

            _logger.LogInformation($"[{callId}] ✅ Comprehensive cleanup completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] Error during comprehensive cleanup");
        }
    }

    private static async Task CleanupWebSocketsAsync(string callId, ClientWebSocket? audioWebSocket, ClientWebSocket? resultsWebSocket)
    {
        try
        {
            // Cleanup audio WebSocket
            if (audioWebSocket != null)
            {
                if (audioWebSocket.State == WebSocketState.Open)
                {
                    try
                    {
                        _logger.LogInformation($"[{callId}] Closing audio WebSocket...");
                        await audioWebSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing audio stream", CancellationToken.None);
                    }
                    catch (WebSocketException wex)
                    {
                        _logger.LogDebug(wex, $"[{callId}] Audio WebSocket closed with exception.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"[{callId}] Error closing audio WebSocket: {ex.Message}");
                    }
                }
                audioWebSocket.Dispose();
                _logger.LogInformation($"[{callId}] Audio WebSocket disposed");
            }

            // Cleanup results WebSocket
            if (resultsWebSocket != null)
            {
                if (resultsWebSocket.State == WebSocketState.Open)
                {
                    try
                    {
                        _logger.LogInformation($"[{callId}] Closing results WebSocket...");
                        await resultsWebSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing results stream", CancellationToken.None);
                    }
                    catch (WebSocketException wex)
                    {
                        _logger.LogDebug(wex, $"[{callId}] Results WebSocket closed with exception.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"[{callId}] Error closing results WebSocket: {ex.Message}");
                    }
                }
                resultsWebSocket.Dispose();
                _logger.LogInformation($"[{callId}] Results WebSocket disposed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] Error cleaning up WebSockets.");
        }
    }

    private static async Task<ClientWebSocket?> ConnectResultsWebSocket(string callId, SimulatorConfig config, string sessionId, string taskId, CancellationToken cancellationToken)
    {
        try
        {
            var resultsWebSocket = new ClientWebSocket();
            var uri = new Uri($"ws://{config.ServerIp}:{config.ServerPort}/technologies/stt/input_stream?task={taskId}&interval=0.33&trigger_events=transcription");

            var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes("admin:phonexia"));
            resultsWebSocket.Options.SetRequestHeader("Accept", "application/json");
            resultsWebSocket.Options.SetRequestHeader("X-SessionID", sessionId);
            resultsWebSocket.Options.SetRequestHeader("Authorization", "Basic " + authHeader);

            await resultsWebSocket.ConnectAsync(uri, cancellationToken);
            _logger.LogInformation($"[{callId}] STT results WebSocket handshake complete.");

            return resultsWebSocket;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] Failed to connect results WebSocket");
            return null;
        }
    }

    private static async Task ReceiveResultsWithWebSocket(string callId, ClientWebSocket resultsWebSocket, CancellationToken cancellationToken)
    {
        try
        {
            var buffer = new byte[8192];

            while (resultsWebSocket.State == WebSocketState.Open && !cancellationToken.IsCancellationRequested)
            {
                var result = await resultsWebSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);

                if (result.MessageType == WebSocketMessageType.Text)
                {
                    var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    _logger.LogInformation($"[{callId}] STT Result: {message}");
                }
                else if (result.MessageType == WebSocketMessageType.Close)
                {
                    _logger.LogInformation($"[{callId}] Results WebSocket closed by server");
                    break;
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation($"[{callId}] Results WebSocket processing cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{callId}] Results WebSocket failed");
        }
    }
}

public class SimulatorConfig
{
    public string ServerIp { get; set; } = "127.0.0.1";
    public int ServerPort { get; set; } = 8600;
    public string WavFile { get; set; } = "sage.wav";
    public int Count { get; set; } = 2;
    public int Duration { get; set; } = 30;
}
