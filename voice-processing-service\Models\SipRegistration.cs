using System.Net;

namespace voice_processing_service.Models
{
    /// <summary>
    /// Represents a SIP registration entry.
    /// </summary>
    public class SipRegistration
    {
        /// <summary>
        /// The SIP URI of the registered user (e.g., sip:<EMAIL>).
        /// </summary>
        public string UserUri { get; set; } = string.Empty;

        /// <summary>
        /// The contact URI where the user can be reached.
        /// </summary>
        public string ContactUri { get; set; } = string.Empty;

        /// <summary>
        /// The IP endpoint of the registered client.
        /// </summary>
        public IPEndPoint RemoteEndPoint { get; set; } = new IPEndPoint(IPAddress.Any, 0);

        /// <summary>
        /// When the registration was created.
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When the registration expires.
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// The Call-ID of the registration request.
        /// </summary>
        public string CallId { get; set; } = string.Empty;

        /// <summary>
        /// The CSeq number of the registration request.
        /// </summary>
        public uint CSeq { get; set; }

        /// <summary>
        /// User-Agent string from the registration request.
        /// </summary>
        public string UserAgent { get; set; } = string.Empty;

        /// <summary>
        /// Whether this registration is currently active.
        /// </summary>
        public bool IsActive => DateTime.UtcNow < ExpiresAt;

        /// <summary>
        /// Time remaining until expiration.
        /// </summary>
        public TimeSpan TimeToExpiry => ExpiresAt - DateTime.UtcNow;
    }
}
