using System;
using System.IO;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using NAudio.Wave;
using SIPSorceryMedia.Abstractions; // Required for AudioSourceOptions, AudioExtrasSource, AudioEncoder, AudioCodecsEnum, MediaEndPoints
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Media;
using SIPSorcery.Net;
using SIPSorcery.Sys;

namespace voice_processing_simulator
{
    partial class Program
    {
        /// <summary>
        /// Records audio from the default microphone into memory and returns the WAV stream and duration.
        /// </summary>
        private static (MemoryStream wavStream, int recordedSeconds) StartRecordingMemory()
        {
            Console.WriteLine("Press any key to start recording...");
            Console.ReadKey();
            Console.WriteLine("Recording started. Press any key to stop...");

            // Capture raw PCM data into memory
            var pcmStream = new MemoryStream();
            using var waveIn = new WaveInEvent
            {
                DeviceNumber = 0,
                WaveFormat = new WaveFormat(8000, 16, 1)
            };
            waveIn.DataAvailable += (s, e) => pcmStream.Write(e.Buffer, 0, e.BytesRecorded);

            var stopwatch = Stopwatch.StartNew();
            waveIn.StartRecording();
            Console.ReadKey();
            waveIn.StopRecording();
            stopwatch.Stop();

            // Wrap raw PCM with WAV header into new stream
            pcmStream.Position = 0;
            var wavStream = new MemoryStream();
            using (var rawProvider = new RawSourceWaveStream(pcmStream, waveIn.WaveFormat))
            {
                WaveFileWriter.WriteWavFileToStream(wavStream, rawProvider);
            }
            wavStream.Position = 0;

            int recordedSeconds = (int)Math.Round(stopwatch.Elapsed.TotalSeconds);
            return (wavStream, recordedSeconds);
        }

        /// <summary>
        /// Simulates a SIP call using in-memory WAV data from microphone.
        /// </summary>
        private static async Task SimulateCallAsync(MemoryStream wavStream, string serverIp, int serverPort, int callDurationSeconds, int clientSipPort = 5070, int rtpPortStart = 25000)
        {
            // Set identifier for logging
            _currentWavFile = "mic";

            // Initialize logger
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddFilter("Microsoft", LogLevel.Warning)
                    .AddFilter("System", LogLevel.Warning)
                    .AddFilter("SIPSorcery", LogLevel.Information)
                    .AddFilter("voice_processing_simulator", LogLevel.Information)
                    .AddConsole();
            });
            _logger = loggerFactory.CreateLogger<Program>();
            SIPSorcery.LogFactory.Set(loggerFactory);

            _logger.LogInformation($"Starting in-memory SIP simulator. Server: {serverIp}:{serverPort}, Duration: {callDurationSeconds}s");

            try
            {
                // Prepare cancellation
                _cts = new CancellationTokenSource();
                _cts.CancelAfter(TimeSpan.FromSeconds(callDurationSeconds + 10));

                // SIP transport and channel
                var sipTransport = new SIPTransport();
                var sipChannel = new SIPUDPChannel(System.Net.IPAddress.Any, clientSipPort);
                sipTransport.AddSIPChannel(sipChannel);
                _logger.LogInformation($"SIP UDP channel listening on {sipChannel.ListeningEndPoint}");

                // RTP media session
                var audioSourceOptions = new AudioSourceOptions { AudioSource = AudioSourcesEnum.None };
                var audioExtrasSource = new AudioExtrasSource(new AudioEncoder(), audioSourceOptions);
                audioExtrasSource.RestrictFormats(f => f.Codec == AudioCodecsEnum.PCMU || f.Codec == AudioCodecsEnum.PCMA || f.Codec == AudioCodecsEnum.G722);
                _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
                {
                    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
                    RtpPortRange = new PortRange(rtpPortStart, rtpPortStart + 10)
                })
                { AcceptRtpFromAny = true };
                _rtpSession.OnRtpPacketReceived += OnRtpPacketReceived;
                _logger.LogInformation("RTP media session created");

                // SDP offer
                var offerSdp = _rtpSession.CreateOffer(System.Net.IPAddress.Any);

                // SIP user agent
                var destination = $"sip:server@{serverIp}:{serverPort}";
                _userAgent = new SIPClientUserAgent(sipTransport);
                _userAgent.CallTrying += (uac, resp) => _logger.LogInformation($"Call trying: {resp.StatusCode} {resp.ReasonPhrase}");
                _userAgent.CallRinging += async (uac, resp) =>
                {
                    _logger.LogInformation($"Call ringing: {resp.StatusCode} {resp.ReasonPhrase}");
                    if (resp.Status == SIPSorcery.SIP.SIPResponseStatusCodesEnum.SessionProgress && resp.Body != null)
                    {
                        var result = _rtpSession.SetRemoteDescription(SdpType.answer, SDP.ParseSDPDescription(resp.Body));
                        if (result == SetDescriptionResultEnum.OK)
                        {
                            await _rtpSession.Start();
                            _logger.LogInformation("RTP session started (in progress).");
                        }
                    }
                };
                _userAgent.CallAnswered += async (uac, resp) =>
                {
                    if (resp.Status == SIPResponseStatusCodesEnum.Ok && resp.Body != null)
                    {
                        var result = _rtpSession.SetRemoteDescription(SdpType.answer, SDP.ParseSDPDescription(resp.Body));
                        if (result == SetDescriptionResultEnum.OK)
                        {
                            await _rtpSession.Start();
                            _logger.LogInformation("Call answered, RTP session started.");
                            // Send in-memory audio
                            await InMemoryRtpStreamer.StartSendingRtpMemoryAsync(wavStream, _rtpSession, _logger);
                        }
                        else
                        {
                            _logger.LogWarning($"Failed to set remote SDP: {result}");
                            _userAgent.Hangup();
                        }
                    }
                };
                _userAgent.CallFailed += (uac, err, resp) =>
                {
                    _logger.LogWarning($"Call failed: {err}");
                    _cts.Cancel();
                };

                // Send INVITE
                var callDescriptor = new SIPCallDescriptor(
                    "simulator", null, destination,
                    "sip:simulator@localhost", destination,
                    null, null, null, SIPCallDirection.Out,
                    SDP.SDP_MIME_CONTENTTYPE, offerSdp.ToString(), null);
                _logger.LogInformation($"Sending INVITE to {destination}");
                _userAgent.Call(callDescriptor);

                // Wait call duration
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(callDurationSeconds + 5), _cts.Token);
                    _logger.LogInformation("Call duration elapsed, hanging up.");
                    _userAgent.Hangup();
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Call cancelled or timed out.");
                }

                // Cleanup RTP session
                _rtpSession.Close("completed");
                _logger.LogInformation("RTP session closed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in in-memory SIP simulation.");
            }
            finally
            {
                _cts?.Dispose();
                _rtpSession?.Close("cleanup");
                _rtpClient?.Close();
                _rtpClient?.Dispose();
            }
        }
    }
}