using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Models;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class SttAudioProcessorTests
    {
        private PhonexiaOptions GetDummyOptions(int chunkSize = 1024)
        {
            return new PhonexiaOptions
            {
                ApiUrl = "https://api.test",
                ApiKey = "key",
                Language = "en",
                Model = "model",
                ChunkSizeBytes = chunkSize
            };
        }

        private HttpClient CreateHttpClient(Func<HttpRequestMessage, HttpResponseMessage> handlerFunc)
        {
            var handlerMock = new Mock<HttpMessageHandler>();
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .Returns((HttpRequestMessage request, CancellationToken token) =>
                    Task.FromResult(handlerFunc(request)));
            return new HttpClient(handlerMock.Object);
        }

        private ILogger<SttAudioProcessor> GetNullLogger() => NullLogger<SttAudioProcessor>.Instance;

        [Fact]
        public void When_Constructing_WithValidDependencies_CreatesInstance()
        {
            // Arrange
            var optionsMockObj = new Mock<IOptions<PhonexiaOptions>>();
            optionsMockObj.Setup(o => o.Value).Returns(GetDummyOptions());
            var optionsMock = optionsMockObj.Object;
            var httpClient = new HttpClient();
            var logger = GetNullLogger();

            // Act
            var processor = new SttAudioProcessor("call1", optionsMock, httpClient, logger);

            // Assert
            Assert.Equal("STT_call1", processor.ProcessorId);
        }

        [Fact]
        public void When_Constructing_WithNullDependency_ThrowsArgumentNullException()
        {
            var optionsMockObj = new Mock<IOptions<PhonexiaOptions>>();
            optionsMockObj.Setup(o => o.Value).Returns(GetDummyOptions());
            var optionsMock = optionsMockObj.Object;
            var httpClient = new HttpClient();
            var logger = GetNullLogger();

            Assert.Throws<ArgumentNullException>(() => new SttAudioProcessor(null, optionsMock, httpClient, logger));
            Assert.Throws<ArgumentNullException>(() => new SttAudioProcessor("call", null, httpClient, logger));
            Assert.Throws<ArgumentNullException>(() => new SttAudioProcessor("call", optionsMock, null, logger));
            Assert.Throws<ArgumentNullException>(() => new SttAudioProcessor("call", optionsMock, httpClient, null));
        }

        [Fact]
        public async Task When_StartProcessingAsync_BufferIsEmpty_FinalizesSessionWithoutError()
        {
            // Arrange
            var optionsMockObj = new Mock<IOptions<PhonexiaOptions>>();
            optionsMockObj.Setup(o => o.Value).Returns(GetDummyOptions());
            var optionsMock = optionsMockObj.Object;
            var calls = new List<string>();
            var httpClient = CreateHttpClient(req =>
            {
                calls.Add(req.RequestUri.AbsolutePath);
                var json = req.Method == HttpMethod.Post && req.RequestUri.AbsolutePath.EndsWith("/sessions")
                    ? "{\"Id\":\"sess1\"}"
                    : "{\"Result\":{\"Transcripts\":[]}}";
                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent(json)
                };
            });
            var logger = GetNullLogger();
            var processor = new SttAudioProcessor("call", optionsMock, httpClient, logger);

            var bufferMock = new Mock<IAudioBuffer>();
            bufferMock.SetupGet(b => b.IsCompleted).Returns(true);
            bufferMock.Setup(b => b.TryTake(out It.Ref<byte[]>.IsAny, It.IsAny<int>(), It.IsAny<CancellationToken>()))
                      .Returns(false);

            // Act
            await processor.StartProcessingAsync(bufferMock.Object, CancellationToken.None);

            // Assert
            Assert.Contains("/sessions", calls);
            Assert.Contains(calls, path => path.Contains("/finalize"));
            Assert.DoesNotContain("/sessions/sess1/data", calls);
        }

        [Fact]
        public async Task When_ProcessAudioDataAsync_WithValidAudio_AddsToInternalBuffer()
        {
            // Arrange
            var options = GetDummyOptions(chunkSize: 1024);
            var optionsMockObj = new Mock<IOptions<PhonexiaOptions>>();
            optionsMockObj.Setup(o => o.Value).Returns(options);
            var optionsMock = optionsMockObj.Object;
            var httpClient = new HttpClient();
            var logger = GetNullLogger();
            var processor = new SttAudioProcessor("call", optionsMock, httpClient, logger);

            // Use reflection to set private sessionId
            typeof(SttAudioProcessor)
                .GetField("_sessionId", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .SetValue(processor, "sess");

            // Act
            var audio = new byte[10];
            await (Task)typeof(SttAudioProcessor)
                .GetMethod("ProcessAudioDataAsync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .Invoke(processor, new object[] { audio, CancellationToken.None });

            // Assert private audioBuffer length increased
            var buffer = (MemoryStream)typeof(SttAudioProcessor)
                .GetField("_audioBuffer", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .GetValue(processor);
            Assert.Equal(10, buffer.Length);
        }

        [Fact]
        public async Task When_ProcessAudioDataAsync_BufferExceedsChunkSize_InvokesSendAudioChunkAsync()
        {
            // Arrange
            var options = GetDummyOptions(chunkSize: 1);
            var optionsMockObj = new Mock<IOptions<PhonexiaOptions>>();
            optionsMockObj.Setup(o => o.Value).Returns(options);
            var optionsMock = optionsMockObj.Object;
            var calls = new List<string>();
            var httpClient = CreateHttpClient(req =>
            {
                calls.Add(req.RequestUri.AbsolutePath);
                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent("{\"Result\":{\"Transcripts\":[]}}")
                };
            });
            var logger = GetNullLogger();
            var processor = new SttAudioProcessor("call", optionsMock, httpClient, logger);

            // Set sessionId for sending
            typeof(SttAudioProcessor)
                .GetField("_sessionId", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .SetValue(processor, "sess");
            var audio = new byte[1];

            // Act
            await (Task)typeof(SttAudioProcessor)
                .GetMethod("ProcessAudioDataAsync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .Invoke(processor, new object[] { audio, CancellationToken.None });

            // Assert
            Assert.Contains("/sessions/sess/data", calls);
        }

        [Fact]
        public async Task When_SendAudioChunkAsync_IsCalled_SendsDataAndFetchesResults()
        {
            // Arrange
            var options = GetDummyOptions();
            var optionsMockObj = new Mock<IOptions<PhonexiaOptions>>();
            optionsMockObj.Setup(o => o.Value).Returns(options);
            var optionsMock = optionsMockObj.Object;
            var sequence = new List<string>();
            var httpClient = CreateHttpClient(req =>
            {
                sequence.Add($"{req.Method}:{req.RequestUri.AbsolutePath}");
                if (req.Method == HttpMethod.Post && req.RequestUri.AbsolutePath.EndsWith("/data"))
                    return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent("") };
                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent("{\"Result\":{\"Transcripts\":[{\"Text\":\"hi\",\"Start\":0,\"End\":1,\"Confidence\":0.5,\"IsFinal\":true}]}}")
                };
            });
            var logger = GetNullLogger();
            var processor = new SttAudioProcessor("call", optionsMock, httpClient, logger);

            // Set session and fill buffer
            typeof(SttAudioProcessor)
                .GetField("_sessionId", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .SetValue(processor, "sess");
            var buffer = (MemoryStream)typeof(SttAudioProcessor)
                .GetField("_audioBuffer", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .GetValue(processor);
            buffer.Write(new byte[10], 0, 10);

            // Act
            await (Task)typeof(SttAudioProcessor)
                .GetMethod("SendAudioChunkAsync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .Invoke(processor, new object[] { CancellationToken.None });

            // Assert
            Assert.Equal(2, sequence.Count);
            Assert.Equal($"POST:/sessions/sess/data", sequence[0]);
            Assert.Equal($"GET:/sessions/sess/result", sequence[1]);
        }

        [Fact]
        public void When_GetResults_IsCalled_ReturnsReadOnlyList()
        {
            // Arrange
            var optionsMockObj = new Mock<IOptions<PhonexiaOptions>>();
            optionsMockObj.Setup(o => o.Value).Returns(GetDummyOptions());
            var optionsMock = optionsMockObj.Object;
            var httpClient = new HttpClient();
            var logger = GetNullLogger();
            var processor = new SttAudioProcessor("call", optionsMock, httpClient, logger);

            // Add result via reflection
            var resultsList = (List<SttResult>)typeof(SttAudioProcessor)
                .GetField("_sttResults", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .GetValue(processor);
            resultsList.Add(new SttResult { Text = "test" });

            // Act
            var readOnly = processor.GetResults();

            // Assert
            Assert.IsAssignableFrom<IReadOnlyList<SttResult>>(readOnly);
            Assert.Single(readOnly);
            Assert.Equal("test", readOnly[0].Text);
        }
    }
}