namespace voice_processing_service.Configuration
{
    /// <summary>
    /// Konfigurační třída pro SIP server.
    /// </summary>
    public class SipServerOptions
    {
        /// <summary>
        /// IP adresa, na které bude SIP server naslouchat. "Any" pro všechny dostupné adresy.
        /// </summary>
        public string ListenIpAddress { get; set; } = "Any";

        /// <summary>
        /// Port, na kterém bude SIP server naslouchat.
        /// </summary>
        public int ListenPort { get; set; } = 5060;

        /// <summary>
        /// Minimální port pro RTP komunikaci.
        /// </summary>
        public int RtpPortMin { get; set; } = 30005;

        /// <summary>
        /// Maximální port pro RTP komunikaci.
        /// </summary>
        public int RtpPortMax { get; set; } = 30010;

        /// <summary>
        /// Výchozí doba expirace registrace v sekundách.
        /// </summary>
        public int DefaultRegistrationExpiry { get; set; } = 3600;

        /// <summary>
        /// Maximální doba expirace registrace v sekundách.
        /// </summary>
        public int MaxRegistrationExpiry { get; set; } = 7200;

        /// <summary>
        /// Minimální doba expirace registrace v sekundách.
        /// </summary>
        public int MinRegistrationExpiry { get; set; } = 60;

        /// <summary>
        /// Povolené SIP endpointy pro registraci (např. Genesys endpoint 17999).
        /// </summary>
        public List<string> AllowedRegistrationEndpoints { get; set; } = new List<string> { "17999" };

        /// <summary>
        /// Adresář pro ukládání WAV nahrávek.
        /// </summary>
        public string WavRecordingDirectory { get; set; } = "RecordedCalls";

        /// <summary>
        /// Override IP address for SDP connection field (c=IN IP4) in server's SDP answer.
        /// If null or empty, uses the remote client's IP address automatically (SIPSorcery default behavior).
        /// This parameter controls what IP address appears in the SDP connection line sent to the client.
        /// Used for debugging or when specific IP address is needed for SIPSorcery RTP session management.
        /// </summary>
        public string? OverrideSdpConnectionAddress { get; set; } = null;
    }
}
