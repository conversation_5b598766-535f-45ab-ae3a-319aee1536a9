using System;
using System.Threading;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro buffer audio dat, k<PERSON><PERSON> slouž<PERSON> jako prostředník mezi přij<PERSON>em a procesorem audio dat.
    /// </summary>
    public interface IAudioBuffer : IDisposable
    {
        /// <summary>
        /// Přidá audio data do bufferu.
        /// </summary>
        /// <param name="audioData">Audio data k přidání.</param>
        void Add(byte[] audioData);

        /// <summary>
        /// Takes audio data from buffer, blocking until data is available or operation is cancelled.
        /// </summary>
        /// <param name="cancellationToken">Token to cancel take operation.</param>
        /// <returns>Audio data array.</returns>
        byte[] Take(CancellationToken cancellationToken);

        /// <summary>
        /// Pokusí se získat audio data z bufferu.
        /// </summary>
        /// <param name="audioData">Výstupní audio data, pokud jsou k dispozici.</param>
        /// <param name="millisecondsTimeout">Timeout v milisekundách.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>True, pokud byla data získána, jinak false.</returns>
        bool TryTake(out byte[] audioData, int millisecondsTimeout, CancellationToken cancellationToken);

        /// <summary>
        /// Signalizuje, že už žádná data nebudou přidána do bufferu.
        /// </summary>
        void CompleteAdding();

        /// <summary>
        /// Indikuje, zda bylo voláno CompleteAdding.
        /// </summary>
        bool IsAddingCompleted { get; }

        /// <summary>
        /// Indikuje, zda je buffer prázdný a bylo voláno CompleteAdding.
        /// </summary>
        bool IsCompleted { get; }
    }
}
