# Monitoring Guide

This guide provides information on how to monitor the CCCZ Buddy Realtime Phonexia Server to ensure optimal performance and reliability.

## Monitoring Objectives

Effective monitoring of the CCCZ Buddy Realtime Phonexia Server should focus on:

1. **System Health** - Ensuring the service is running and responsive
2. **Resource Usage** - Monitoring CPU, memory, disk, and network usage
3. **Call Processing** - Tracking call volumes, durations, and success rates
4. **Speech-to-Text Performance** - Monitoring STT processing times and success rates
5. **Error Rates** - Identifying and addressing errors

## Key Metrics to Monitor

### System Health Metrics

| Metric | Description | Warning Threshold | Critical Threshold |
|--------|-------------|-------------------|-------------------|
| Service Status | Whether the service is running | N/A | Not Running |
| Service Uptime | How long the service has been running | < 1 day | < 1 hour |
| API Response Time | Time to respond to API requests | > 500ms | > 2000ms |
| SIP Response Time | Time to respond to SIP requests | > 200ms | > 1000ms |

### Resource Usage Metrics

| Metric | Description | Warning Threshold | Critical Threshold |
|--------|-------------|-------------------|-------------------|
| CPU Usage | Percentage of CPU used by the service | > 70% | > 90% |
| Memory Usage | Amount of memory used by the service | > 70% | > 90% |
| Disk Space | Free disk space for recordings and logs | < 20% | < 10% |
| Network I/O | Network traffic in/out | Depends on capacity | Depends on capacity |

### Call Processing Metrics

| Metric | Description | Warning Threshold | Critical Threshold |
|--------|-------------|-------------------|-------------------|
| Active Calls | Number of currently active calls | > 70% capacity | > 90% capacity |
| Call Setup Time | Time to establish a call | > 1000ms | > 3000ms |
| Call Success Rate | Percentage of successful calls | < 95% | < 90% |
| Call Duration | Average duration of calls | N/A | N/A |

### Speech-to-Text Metrics

| Metric | Description | Warning Threshold | Critical Threshold |
|--------|-------------|-------------------|-------------------|
| STT Processing Time | Time to process audio for STT | > 2x real-time | > 5x real-time |
| STT Success Rate | Percentage of successful STT operations | < 95% | < 90% |
| Phonexia API Latency | Response time of Phonexia API | > 500ms | > 2000ms |
| Transcription Quality | Confidence score of transcriptions | < 0.7 | < 0.5 |

### Error Metrics

| Metric | Description | Warning Threshold | Critical Threshold |
|--------|-------------|-------------------|-------------------|
| Error Rate | Percentage of operations resulting in errors | > 5% | > 10% |
| SIP Errors | Number of SIP protocol errors | > 10/hour | > 50/hour |
| RTP Errors | Number of RTP stream errors | > 10/hour | > 50/hour |
| STT Errors | Number of STT processing errors | > 5/hour | > 20/hour |

## Monitoring Tools

### Built-in Monitoring

The CCCZ Buddy Realtime Phonexia Server provides several built-in monitoring capabilities:

1. **Logging** - Detailed logs of all operations
2. **Health Endpoints** - HTTP endpoints for health checks
3. **Metrics Endpoints** - HTTP endpoints for metrics

#### Health Endpoints

The server exposes health check endpoints that can be used to monitor system health:

- `/health` - Overall health status
- `/health/live` - Liveness check (is the service running)
- `/health/ready` - Readiness check (is the service ready to accept requests)

Example:
```bash
curl http://server:5000/health
```

Response:
```json
{
  "status": "Healthy",
  "checks": [
    {
      "name": "SIP Service",
      "status": "Healthy",
      "description": "SIP service is running and accepting connections"
    },
    {
      "name": "Phonexia API",
      "status": "Healthy",
      "description": "Phonexia API is accessible"
    },
    {
      "name": "Disk Space",
      "status": "Healthy",
      "description": "Sufficient disk space available"
    }
  ]
}
```

#### Metrics Endpoints

The server exposes metrics endpoints that provide detailed metrics:

- `/metrics` - Prometheus-compatible metrics

Example:
```bash
curl http://server:5000/metrics
```

Response:
```
# HELP ccczbuddy_active_calls Current number of active calls
# TYPE ccczbuddy_active_calls gauge
ccczbuddy_active_calls 5

# HELP ccczbuddy_call_setup_time_seconds Time to establish a call in seconds
# TYPE ccczbuddy_call_setup_time_seconds histogram
ccczbuddy_call_setup_time_seconds_bucket{le="0.1"} 42
ccczbuddy_call_setup_time_seconds_bucket{le="0.5"} 89
ccczbuddy_call_setup_time_seconds_bucket{le="1"} 97
ccczbuddy_call_setup_time_seconds_bucket{le="2"} 99
ccczbuddy_call_setup_time_seconds_bucket{le="5"} 100
ccczbuddy_call_setup_time_seconds_bucket{le="+Inf"} 100
ccczbuddy_call_setup_time_seconds_sum 45.2
ccczbuddy_call_setup_time_seconds_count 100

# HELP ccczbuddy_stt_processing_time_seconds Time to process audio for STT in seconds
# TYPE ccczbuddy_stt_processing_time_seconds histogram
ccczbuddy_stt_processing_time_seconds_bucket{le="1"} 10
ccczbuddy_stt_processing_time_seconds_bucket{le="5"} 45
ccczbuddy_stt_processing_time_seconds_bucket{le="10"} 80
ccczbuddy_stt_processing_time_seconds_bucket{le="30"} 95
ccczbuddy_stt_processing_time_seconds_bucket{le="60"} 99
ccczbuddy_stt_processing_time_seconds_bucket{le="+Inf"} 100
ccczbuddy_stt_processing_time_seconds_sum 750.2
ccczbuddy_stt_processing_time_seconds_count 100
```

### External Monitoring Tools

#### Prometheus and Grafana

Prometheus can be used to scrape metrics from the server, and Grafana can be used to visualize them.

1. Configure Prometheus to scrape metrics from the server:

```yaml
scrape_configs:
  - job_name: 'ccczbuddy-phonexia'
    scrape_interval: 15s
    static_configs:
      - targets: ['server:5000']
```

2. Create Grafana dashboards to visualize the metrics.

#### Log Aggregation

Use a log aggregation tool like ELK Stack (Elasticsearch, Logstash, Kibana) or Graylog to collect and analyze logs.

1. Configure the server to send logs to the log aggregation system:

```json
"Logging": {
  "Elasticsearch": {
    "Url": "http://elasticsearch:9200",
    "IndexFormat": "ccczbuddy-phonexia-{0:yyyy.MM.dd}"
  }
}
```

2. Create dashboards and alerts based on log patterns.

#### System Monitoring

Use system monitoring tools like Nagios, Zabbix, or Datadog to monitor system health and resource usage.

Example Nagios check for service status:

```bash
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://server:5000/health)
if [ $response -eq 200 ]; then
    echo "OK - Service is healthy"
    exit 0
else
    echo "CRITICAL - Service is unhealthy"
    exit 2
fi
```

## Setting Up Alerts

### Alert Thresholds

Configure alerts based on the thresholds defined in the metrics tables above.

### Alert Channels

Set up multiple alert channels to ensure timely notification:

1. **Email** - For non-urgent issues
2. **SMS** - For urgent issues
3. **Pager Duty** - For critical issues
4. **Slack/Teams** - For team collaboration

### Alert Severity Levels

Define severity levels for alerts:

1. **Info** - Informational, no action required
2. **Warning** - Potential issue, monitor closely
3. **Error** - Issue requiring attention
4. **Critical** - Severe issue requiring immediate attention

### Alert Grouping

Group related alerts to prevent alert fatigue:

1. Group by component (SIP, RTP, STT)
2. Group by server
3. Group by time window

## Monitoring Best Practices

1. **Baseline Establishment**
   - Monitor the system under normal load to establish baseline metrics
   - Use baselines to set appropriate alert thresholds

2. **Proactive Monitoring**
   - Monitor trends to identify potential issues before they become critical
   - Set up alerts for warning thresholds to allow time for intervention

3. **Correlation Analysis**
   - Correlate metrics across different components
   - Look for patterns that indicate systemic issues

4. **Regular Review**
   - Regularly review monitoring data and alerts
   - Adjust thresholds based on operational experience

5. **Documentation**
   - Document all monitoring configurations
   - Document alert response procedures

## Monitoring Checklist

- [ ] Configure health check monitoring
- [ ] Configure metrics collection
- [ ] Set up log aggregation
- [ ] Create dashboards for key metrics
- [ ] Configure alerts for critical metrics
- [ ] Establish baseline metrics
- [ ] Document monitoring setup and procedures
- [ ] Train team on alert response procedures

## Troubleshooting

If monitoring indicates issues, refer to the [Troubleshooting Guide](troubleshooting.md) for detailed troubleshooting procedures.
