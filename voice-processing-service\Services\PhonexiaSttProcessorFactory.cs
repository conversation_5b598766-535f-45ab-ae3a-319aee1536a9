using System;
using System.Net.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Továrna pro vytváření instancí PhonexiaSttProcessor.
    /// </summary>
    public class PhonexiaSttProcessorFactory
    {
        private readonly ILoggerFactory _loggerFactory;
        private readonly IOptions<PhonexiaOptions> _options;
        private readonly IHttpClientFactory _httpClientFactory;

        /// <summary>
        /// Inicializuje novou instanci třídy <see cref="PhonexiaSttProcessorFactory"/>.
        /// </summary>
        /// <param name="loggerFactory">Továrna pro vytváření loggerů.</param>
        /// <param name="options">Konfigurace Phonexia služby.</param>
        /// <param name="httpClientFactory">Továrna pro vytváření HTTP klientů.</param>
        public PhonexiaSttProcessorFactory(
            ILoggerFactory loggerFactory,
            IOptions<PhonexiaOptions> options,
            IHttpClientFactory httpClientFactory)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
        }

        /// <summary>
        /// Vytvoří novou instanci PhonexiaSttProcessor.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru pro logování.</param>
        /// <param name="callerParty">Volající strana pro session sharing.</param>
        /// <param name="calledParty">Volaná strana pro session sharing.</param>
        /// <returns>Instance IAudioProcessor.</returns>
        public IAudioProcessor CreateProcessor(string callId, string callerParty = "", string calledParty = "")
        {
            var httpClient = _httpClientFactory.CreateClient("PhonexiaApi");
            var logger = _loggerFactory.CreateLogger<PhonexiaSttProcessor>();

            // Použijeme základní hodnoty pro parametry, které nejsou k dispozici
            string connectionId = $"conn_{callId}";
            string agentId = "unknown";
            string customerNumber = "unknown";
            string channelType = "unknown";

            // Vytvoříme mock KafkaProducer, protože není k dispozici
            KafkaProducer kafkaProducer = null;

            return new PhonexiaSttProcessor(
                callId,
                connectionId,
                agentId,
                customerNumber,
                channelType,
                callerParty,
                calledParty,
                _options,
                httpClient,
                kafkaProducer,
                logger);
        }
    }
}
