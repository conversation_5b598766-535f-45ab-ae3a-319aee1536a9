using System;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Net;
using SIPSorcery.Media;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using SIPSorceryMedia.Abstractions;
using System.Security.Cryptography;
using SIPSorcery.Sys;
// Add caching for retransmitted INVITEs
using System.Collections.Concurrent;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace SIP serveru jako IHostedService.
    /// </summary>
    internal class SipServerServiceBackup //: IHostedService
    {
        private readonly ILogger<SipServerService> _logger;
        private readonly ICallSessionManager _sessionManager;
        private readonly SipServerOptions _options;
        private readonly ILoggerFactory _loggerFactory;
        // Tato tov<PERSON>rna by <PERSON><PERSON><PERSON><PERSON>, protože porty v SDP a náhodně vytvořené porty by byly jiné
        // Ponecháváme ji zde pouze pro kompatibilitu s existujícím kódem
        //private readonly Func<IAudioInputReceiver> _audioInputReceiverFactory;
        private readonly Func<string, int, int, IAudioInputReceiver> _audioInputReceiverFactory;
        private readonly Func<string, IAudioProcessor> _audioProcessorFactory;
        private readonly IPortAllocator _portAllocator;
        private readonly IRtpAudioReceiverFactory _rtpAudioReceiverFactory;
        private SIPTransport _sipTransport;
        private readonly ConcurrentDictionary<string, SIPResponse> _inviteResponseCache = new ConcurrentDictionary<string, SIPResponse>();
        // Proměnné pro aktuální hovor
        private SIPServerUserAgent _userAgent;
        private VoIPMediaSession _rtpSession;

        /// <summary>
        /// Handles SDP negotiation, RTP binding, and session startup for an INVITE request.
        /// </summary>
        private async Task ProcessInviteNegotiationOldAsync(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing INVITE request from {remoteEndPoint}.");

            try
            {
                // Kontrola, zda je v INVITE nabídce kodek, který podporujeme
                var offerSdp = SDP.ParseSDPDescription(sipRequest.Body);
                var hasZeroPortOffer = offerSdp.Media.Any(m => m.Port == 0) || !offerSdp.Media.Any();
                IPEndPoint dstRtpEndPoint = SDP.GetSDPRTPEndPoint(sipRequest.Body);
                if (hasZeroPortOffer)
                {
                    var (rtpClientZero, rtcpClientZero) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                    int allocatedZeroRtp = ((IPEndPoint)rtpClientZero.Client.LocalEndPoint).Port;
                    var okZero = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                    okZero.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                    okZero.Body = sipRequest.Body.Replace("m=audio 0", $"m=audio {allocatedZeroRtp}");
                    _inviteResponseCache[callId] = okZero;
                    await _sipTransport.SendResponseAsync(okZero);
                    await _portAllocator.ReleasePortsAsync(callId, rtpClientZero, rtcpClientZero);
                    return;
                }

                // Vytvoření VoIPMediaSession pro zpracování RTP
                // Použijeme konstruktor s explicitním nastavením lokální IP adresy
                List<AudioCodecsEnum> codecs = new List<AudioCodecsEnum> { AudioCodecsEnum.PCMU, AudioCodecsEnum.PCMA, AudioCodecsEnum.G722 };

                var audioSource = AudioSourcesEnum.SineWave;

                AudioExtrasSource audioExtrasSource = new AudioExtrasSource(new AudioEncoder(), new AudioSourceOptions { AudioSource = audioSource });
                audioExtrasSource.RestrictFormats(formats => codecs.Contains(formats.Codec));
                var _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
                {
                    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
                    RtpPortRange = new PortRange(
                        (_options.RtpPortMin % 2 == 0 ? _options.RtpPortMin : _options.RtpPortMin + 1),
                        _options.RtpPortMax),
                    
                });

                // Nastavíme, aby VoIPMediaSession přijímala data z jakéhokoliv zdroje
                _rtpSession.AcceptRtpFromAny = true;
                _logger.LogInformation($"[{callId}] Created VoIPMediaSession with RTP starting at port {_options.RtpPortMin}");

                // Nastavení vzdáleného popisu z nabídky
                var setResult = _rtpSession.SetRemoteDescription(SdpType.offer, offerSdp);

                if (setResult != SetDescriptionResultEnum.OK)
                {
                    // Není podporovaný kodek nebo jiný problém s SDP
                    _logger.LogWarning($"[{callId}] Failed to set remote description: {setResult}");
                    SIPResponse noMatchingCodecResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.NotAcceptableHere, setResult.ToString());
                    await _sipTransport.SendResponseAsync(noMatchingCodecResponse);
                }
                else
                {
                    _logger.LogDebug($"[{callId}] Client offer contained supported audio codec.");

                    try
                    {
                        // Vytvoření SDP odpovědi pomocí VoIPMediaSession
                        // Reserve RTP/RTCP ports before building SDP answer
                        var (tempRtp, tempRtcp) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                        int reservedRtp = ((IPEndPoint)tempRtp.Client.LocalEndPoint).Port;
                        int reservedRtcp = ((IPEndPoint)tempRtcp.Client.LocalEndPoint).Port;
                        _logger.LogInformation($"[{callId}] Reserved RTP={reservedRtp}, RTCP={reservedRtcp}");
                        // Build SDP answer and override audio media port
                        var answerSdp = _rtpSession.CreateAnswer(null);
                        foreach (var media in answerSdp.Media)
                        {
                            if (media.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase))
                            {
                                media.Port = reservedRtp;
                                break;
                            }
                        }
                        // Override session-level connection address using client remote SIP endpoint
                        var clientAddress = remoteEndPoint.Address.ToString();
                        answerSdp.Connection.ConnectionAddress = clientAddress;

                        _logger.LogInformation($"[{callId}] Created SDP answer");

                        // Log SDP details for debugging
                        _logger.LogDebug($"[{callId}] SDP answer: {answerSdp}");
                        
                        // Allocate RTP/RTCP ports for this call
                        (UdpClient rtpClient, UdpClient rtcpClient) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                        int allocatedRtp = ((IPEndPoint)rtpClient.Client.LocalEndPoint).Port;
                        int allocatedRtcp = ((IPEndPoint)rtcpClient.Client.LocalEndPoint).Port;

                        // Získáme port z SDP odpovědi

                        // Vytvoření SDP odpovědi pomocí VoIPMediaSession

                        _logger.LogInformation($"[{callId}] Created SDP answer");

                        // Log SDP details for debugging
                        _logger.LogDebug($"[{callId}] SDP answer: {answerSdp}");

                        try {
                            // Get RTP endpoints from the session - important for debugging
                            var destinationEndPoint = _rtpSession.AudioDestinationEndPoint;
                            if (destinationEndPoint != null) {
                                _logger.LogInformation($"[{callId}] RTP endpoint: {destinationEndPoint}, expecting data from {dstRtpEndPoint}");

                                // Logování více informací o SDP pro diagnostiku
                                _logger.LogInformation($"[{callId}] SDP connection info: {answerSdp.Connection}");
                                foreach (var media in answerSdp.Media)
                                {
                                    _logger.LogInformation($"[{callId}] SDP media: {media.Media} port {media.Port}");
                                }

                                // Explicitní logování rozdílu mezi endpointy
                                if (destinationEndPoint.ToString() != dstRtpEndPoint.ToString())
                                {
                                    _logger.LogWarning($"[{callId}] RTP endpoint mismatch: SIPSorcery expects {destinationEndPoint}, SDP indicates {dstRtpEndPoint}");
                                    _logger.LogInformation($"[{callId}] This is normal with NAT/port forwarding and should not cause issues as long as AcceptRtpFromAny is true");
                                }
                            }
                        } catch (Exception ex) {
                            _logger.LogWarning($"[{callId}] Failed to get RTP endpoints: {ex.Message}");
                        }

                         // Create RTP receiver with allocated ports and update SDP media port
                         _logger.LogInformation($"[{callId}] Creating RTP receiver with allocated ports: RTP={allocatedRtp}, RTCP={allocatedRtcp}");
                         var receiver = _rtpAudioReceiverFactory.CreateReceiver(callId, allocatedRtp, allocatedRtcp);
                         // Override SDP media port to allocated RTP port
                         foreach (var media in answerSdp.Media)
                         {
                             if (media.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase))
                             {
                                 media.Port = allocatedRtp;
                                 _logger.LogInformation($"[{callId}] Updated SDP media port to allocated port {allocatedRtp}");
                                 break;
                             }
                         }
                         Func<IAudioInputReceiver> inputReceiverFactory = () => receiver;
                         Func<IAudioProcessor> audioProcessorFactory = () => _audioProcessorFactory(callId);

                        // Pokud je již aktivní hovor, ukončíme ho
                        if (_userAgent?.IsHungup == false)
                        {
                            _logger.LogWarning($"[{callId}] Hanging up existing call to accept new one.");
                            _userAgent.Hangup(false);
                            _rtpSession?.Close("New call");
                            await Task.Delay(100); // Dáme čas na ukončení
                        }

                        // Vytvoření UAS transakce a SIPServerUserAgent
                        UASInviteTransaction uasTransaction = new UASInviteTransaction(_sipTransport, sipRequest, null);
                        _userAgent = new SIPServerUserAgent(_sipTransport, null, uasTransaction, null);

                        // Registrace callbacku pro zrušení hovoru
                        _userAgent.CallCancelled += async (uasAgent, cancelReq) =>
                        {
                            _logger.LogInformation($"[{callId}] Call cancelled by client.");
                            _rtpSession?.Close("Call cancelled");
                            await _sessionManager.TerminateSessionAsync(callId);
                        };

                        // Registrace callbacku pro ukončení RTP
                        _rtpSession.OnRtpClosed += (reason) => _userAgent?.Hangup(false);

                        // Vytvoření session
                        var session = await _sessionManager.CreateSessionAsync(_userAgent, sipRequest, inputReceiverFactory, audioProcessorFactory);

                        // Odeslání odpovědi 100 Trying a 180 Ringing
                        _userAgent.Progress(SIPResponseStatusCodesEnum.Trying, null, null, null, null);
                        await Task.Delay(100);
                        _userAgent.Progress(SIPResponseStatusCodesEnum.Ringing, null, null, null, null);
                        await Task.Delay(100);

                        try {
                            // Get RTP endpoints from the session - important for debugging
                            var destinationEndPoint = _rtpSession.AudioDestinationEndPoint;
                            if (destinationEndPoint != null) {
                                _logger.LogInformation($"[{callId}] RTP endpoint: {destinationEndPoint}, expecting data from {dstRtpEndPoint}");

                                // Logování více informací o SDP pro diagnostiku
                                _logger.LogInformation($"[{callId}] SDP connection info: {answerSdp.Connection}");
                                foreach (var media in answerSdp.Media)
                                {
                                    _logger.LogInformation($"[{callId}] SDP media: {media.Media} port {media.Port}");
                                }

                                // Explicitní logování rozdílu mezi endpointy
                                if (destinationEndPoint.ToString() != dstRtpEndPoint.ToString())
                                {
                                    _logger.LogWarning($"[{callId}] RTP endpoint mismatch: SIPSorcery expects {destinationEndPoint}, SDP indicates {dstRtpEndPoint}");
                                    _logger.LogInformation($"[{callId}] This is normal with NAT/port forwarding and should not cause issues as long as AcceptRtpFromAny is true");
                                }
                            }
                        } catch (Exception ex) {
                            _logger.LogWarning($"[{callId}] Failed to get RTP endpoints: {ex.Message}");
                        }

                        // Odeslání odpovědi 200 OK s SDP
                        var sdpString = answerSdp.ToString().Replace("c=IN IP4 0.0.0.0", $"c=IN IP4 {clientAddress}");
                        _userAgent.Answer(SDP.SDP_MIME_CONTENTTYPE, sdpString, null, SIPDialogueTransferModesEnum.NotAllowed);
                        var okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                        okResponse.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                        okResponse.Body = sdpString;
                        _inviteResponseCache[callId] = okResponse;
                        await _sipTransport.SendResponseAsync(okResponse);
                        await _portAllocator.ReleasePortsAsync(callId, rtpClient, rtcpClient);

                        // Spuštění session
                        await session.StartAsync(CancellationToken.None);
                        await _rtpSession.Start();

                        // Log connection details after starting
                        _logger.LogInformation($"[{callId}] SIP and RTP sessions started successfully");
                        _logger.LogInformation($"[{callId}] Call from {sipRequest.Header.From.FromURI.User} established");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{callId}] Error processing incoming call.");

                        // Odeslání chybové odpovědi
                        SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                        await _sipTransport.SendResponseAsync(errorResponse);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing INVITE request.");

                // Odeslání chybové odpovědi
                SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                await _sipTransport.SendResponseAsync(errorResponse);
            }
        }
    }
}