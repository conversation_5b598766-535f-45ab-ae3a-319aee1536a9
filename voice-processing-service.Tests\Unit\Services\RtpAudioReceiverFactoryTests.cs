using System.Net;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class RtpAudioReceiverFactoryTests
    {
        [Fact]
        public void Constructor_DoesNotThrow()
        {
            var options = Options.Create(new SipServerOptions());
            var allocator = new PortAllocator(options, NullLogger<PortAllocator>.Instance);
            var factory = new RtpAudioReceiverFactory(NullLoggerFactory.Instance, options, allocator);
            Assert.NotNull(factory);
        }

        [Fact]
        public void CreateReceiver_BindsWithinRange()
        {
            var opts = new SipServerOptions { RtpPortMin = 15000, RtpPortMax = 15010 };
            var options = Options.Create(opts);
            var allocator = new PortAllocator(options, NullLogger<PortAllocator>.Instance);
            var factory = new RtpAudioReceiverFactory(NullLoggerFactory.Instance, options, allocator);

            IPAddress local = IPAddress.Loopback;
            var receiver = factory.CreateReceiver("call1", local);
            Assert.NotNull(receiver);

            int rtpPort = receiver.RtpLocalEndPoint.Port;
            Assert.InRange(rtpPort, opts.RtpPortMin, opts.RtpPortMax);
            Assert.Equal(rtpPort + 1, receiver.RtcpLocalEndPoint.Port);
        }
[Fact]
        public void CreateReceiver_WithSpecificPorts_UsesAllocator()
        {
            var opts = new SipServerOptions { RtpPortMin = 50002, RtpPortMax = 50003 };
            var options = Options.Create(opts);
            var allocator = new PortAllocator(options, NullLogger<PortAllocator>.Instance);
            var factory = new RtpAudioReceiverFactory(NullLoggerFactory.Instance, options, allocator);
            var receiver = factory.CreateReceiver("call2", 50002, 50003);

            Assert.Equal(50002, receiver.RtpLocalEndPoint.Port);
            Assert.Equal(50003, receiver.RtcpLocalEndPoint.Port);
        }
    }
}