using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Net;
using voice_processing_service.Interfaces;
using System.Threading;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace správce sessions hovorů.
    /// </summary>
    public class CallSessionManager : ICallSessionManager, IDisposable
    {
        private readonly ILogger<CallSessionManager> _logger;
        private readonly ILoggerFactory _loggerFactory;
        private readonly ConcurrentDictionary<string, ICallSession> _sessions = new ConcurrentDictionary<string, ICallSession>();
        private readonly IPortAllocator _portAllocator;
        private readonly ICallSynchronizationService _synchronizationService;
        private readonly Timer _healthCheckTimer;
        private readonly object _cleanupLock = new object();
        private bool _disposed = false;
        
        // Resource tracking for leak detection
        private readonly ConcurrentDictionary<string, SessionResourceInfo> _resourceTracking = new ConcurrentDictionary<string, SessionResourceInfo>();
        
        /// <summary>
        /// Information about resources allocated for a session
        /// </summary>
        private class SessionResourceInfo
        {
            public string CallId { get; set; }
            public DateTime CreatedAt { get; set; }
            public int RtpPort { get; set; }
            public int RtcpPort { get; set; }
            public bool IsActive { get; set; }
            public DateTime LastActivity { get; set; }
        }

        /// <summary>
        /// Generuje klíč dialogu ve formátu "Call-ID:FromTag:ToTag", fallback na "Call-ID" před navázáním dialogu.
        /// </summary>
        private string GenerateSessionKey(SIPRequest request)
        {
            var callId = request.Header.CallId;
            var fromTag = request.Header?.From?.FromTag;
            var toTag = request.Header?.To?.ToTag;
            if (!string.IsNullOrEmpty(fromTag) && !string.IsNullOrEmpty(toTag))
            {
                return $"{callId}:{fromTag}:{toTag}";
            }
            return callId;
        }

        /// <summary>
        /// Vytvoří novou instanci CallSessionManager.
        /// </summary>
        /// <param name="logger">Logger.</param>
        /// <param name="loggerFactory">Logger factory.</param>
        /// <param name="portAllocator">Port allocator for resource tracking.</param>
        /// <param name="synchronizationService">Synchronization service for immediate cleanup triggers.</param>
        public CallSessionManager(
            ILogger<CallSessionManager>? logger = null,
            ILoggerFactory? loggerFactory = null,
            IPortAllocator? portAllocator = null,
            ICallSynchronizationService? synchronizationService = null)
        {
            _logger = logger ?? NullLogger<CallSessionManager>.Instance;
            _loggerFactory = loggerFactory ?? NullLoggerFactory.Instance;
            _portAllocator = portAllocator;
            _synchronizationService = synchronizationService;
            
            // Start health check timer for resource leak detection (every 5 minutes)
            _healthCheckTimer = new Timer(PerformHealthCheck, null, (int)TimeSpan.FromMinutes(5).TotalMilliseconds, (int)TimeSpan.FromMinutes(5).TotalMilliseconds);
            
            _logger.LogInformation("CallSessionManager created with enhanced resource cleanup.");
        }

        /// <summary>
        /// Vytvoří novou session pro hovor.
        /// </summary>
        /// <param name="userAgent">SIP User Agent pro tento hovor.</param>
        /// <param name="inviteRequest">INVITE požadavek, který zahájil hovor.</param>
        /// <param name="inputReceiverFactory">Tovární metoda pro vytvoření přijímače audio dat.</param>
        /// <param name="audioProcessorFactory">Tovární metoda pro vytvoření procesoru audio dat.</param>
        /// <returns>Task reprezentující asynchronní operaci, která vrací vytvořenou session.</returns>
        public Task<ICallSession> CreateSessionAsync(
            SIPServerUserAgent userAgent,
            SIPRequest inviteRequest,
            Func<IAudioInputReceiver> inputReceiverFactory,
            Func<IAudioProcessor> audioProcessorFactory)
        {
            var sessionKey = GenerateSessionKey(inviteRequest);
            var callId = inviteRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Creating new session with key {sessionKey}.");

            // Kontrola, zda session již neexistuje (pre-dialog or in-dialog)
            ICallSession existingSession = null;
            var fromTag = inviteRequest.Header?.From?.FromTag;
            var toTag = inviteRequest.Header?.To?.ToTag;
            _logger.LogTrace($"[{callId}] FromTag: {fromTag}, ToTag: {toTag}");
            
            if (!string.IsNullOrEmpty(fromTag) && !string.IsNullOrEmpty(toTag))
            {
                // in-dialog: try full key, then fallback to Call-ID
                if (!_sessions.TryGetValue(sessionKey, out existingSession))
                {
                    if (_sessions.TryGetValue(callId, out existingSession))
                    {
                        _sessions.TryRemove(callId, out _);
                        _sessions.TryAdd(sessionKey, existingSession);
                    }
                }
            }
            else
            {
                // pre-dialog: key is callId
                _sessions.TryGetValue(sessionKey, out existingSession);
            }

            if (existingSession != null)
            {
                _logger.LogWarning($"[{callId}] Session already exists.");
                return Task.FromResult(existingSession);
            }

            IAudioInputReceiver? receiver = null;
            IAudioBuffer? buffer = null;
            IAudioProcessor? processor = null;

            try
            {
                // Vytvoření přijímače
                receiver = inputReceiverFactory();
                if (receiver == null)
                {
                    throw new InvalidOperationException($"[{callId}] Input receiver factory returned null.");
                }

                // Vytvoření bufferu
                buffer = new BlockingCollectionAudioBuffer(_loggerFactory.CreateLogger<BlockingCollectionAudioBuffer>());
                if (receiver is RtpAudioReceiver rtpReceiver)
                {
                    rtpReceiver.AudioFrameReceived += buffer.Add;
                }

                // Vytvoření procesoru
                processor = audioProcessorFactory();
                if (processor == null)
                {
                    throw new InvalidOperationException($"[{callId}] Audio processor factory returned null.");
                }

                // Enhanced callback pro úklid v manažeru s comprehensive resource cleanup
                Func<Task> cleanupCallback = async () =>
                {
                    await PerformEnhancedSessionCleanupAsync(callId, sessionKey, receiver);
                };

                // Vytvoření session
                var session = new CallSession(
                    userAgent,
                    inviteRequest,
                    receiver,
                    buffer,
                    processor,
                    cleanupCallback,
                    _loggerFactory.CreateLogger<CallSession>()
                );

                // Přidání session do slovníku a resource tracking
                if (_sessions.TryAdd(sessionKey, session))
                {
                    // Track resources for leak detection
                    TrackSessionResources(callId, session);
                    
                    _logger.LogInformation($"[{callId}] Session created and added to manager.");
                    return Task.FromResult<ICallSession>(session);
                }
                else
                {
                    // Toto by se nemělo stát, protože jsme již kontrolovali existenci session
                    _logger.LogError($"[{callId}] Failed to add session to manager.");
                    throw new InvalidOperationException($"Failed to add session for call {callId} to manager.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error creating session.");

                // Úklid v případě chyby
                processor?.Dispose();
                buffer?.Dispose();
                receiver?.Dispose();

                throw;
            }
        }

        /// <summary>
        /// Získá session podle identifikátoru hovoru.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <returns>Session hovoru, nebo null, pokud session neexistuje.</returns>
        public ICallSession GetSession(string callId)
        {
            if (string.IsNullOrEmpty(callId))
            {
                _logger.LogWarning("GetSession called with null or empty callId.");
                return null;
            }

            _logger.LogDebug($"[{callId}] GetSession called.");
            if (_sessions.TryGetValue(callId, out var session))
            {
                return session;
            }
            // fallback to full dialog key lookup
            return _sessions.Values.FirstOrDefault(s => s.CallId == callId);
        }

        /// <summary>
        /// Ukončí session hovoru.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task TerminateSessionAsync(string callId)
        {
            if (string.IsNullOrEmpty(callId))
            {
                _logger.LogWarning("TerminateSessionAsync called with null or empty callId.");
                return;
            }

            _logger.LogInformation($"[{callId}] Terminating session.");

            // Získání session
            var sessionEntry = _sessions.FirstOrDefault(kv => kv.Value.CallId == callId);
            if (sessionEntry.Value != null)
            {
                var session = sessionEntry.Value;
                try
                {
                    // Enhanced termination with comprehensive cleanup
                    await TerminateSessionWithCleanupAsync(session, sessionEntry.Key);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{callId}] Error terminating session.");
                    // Force cleanup even on error
                    await ForceCleanupSessionAsync(callId, sessionEntry.Key);
                }
                finally
                {
                    // Trigger immediate cleanup of synchronization locks
                    if (_synchronizationService != null)
                    {
                        try
                        {
                            await _synchronizationService.CleanupLocksAsync(callId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"[{callId}] Error cleaning up synchronization locks during session termination.");
                        }
                    }
                }
            }
            else
            {
                _logger.LogWarning($"[{callId}] Session not found for termination.");
            }
        }

        /// <summary>
        /// Aktualizuje existující session na základě re-INVITE požadavku.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <param name="reInviteRequest">Re-INVITE SIP požadavek.</param>
        /// <param name="newSdpOffer">Nová SDP nabídka.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task UpdateSessionAsync(string callId, SIPRequest reInviteRequest, SDP newSdpOffer)
        {
            if (string.IsNullOrEmpty(callId))
            {
                _logger.LogWarning("UpdateSessionAsync called with null or empty callId.");
                return;
            }

            _logger.LogInformation($"[{callId}] Updating session based on re-INVITE.");

            // Získání existující session
            var session = GetSession(callId);
            if (session == null)
            {
                _logger.LogWarning($"[{callId}] Session not found for update.");
                throw new InvalidOperationException($"Session not found for call {callId}");
            }

            try
            {
                // Delegace aktualizace na session
                await session.UpdateSessionAsync(reInviteRequest, newSdpOffer);
                _logger.LogInformation($"[{callId}] Session updated successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error updating session.");
                throw;
            }
        }

        /// <summary>
        /// Získá všechny aktivní session hovorů.
        /// </summary>
        /// <returns>Kolekce aktivních session hovorů.</returns>
        public IEnumerable<ICallSession> GetAllSessions()
        {
            _logger.LogDebug("GetAllSessions called.");
            return _sessions.Values.ToList();
        }

        /// <summary>
        /// Enhanced session cleanup with comprehensive resource release
        /// </summary>
        private async Task PerformEnhancedSessionCleanupAsync(string callId, string sessionKey, IAudioInputReceiver receiver)
        {
            _logger.LogInformation($"[{callId}] Enhanced cleanup callback invoked. Removing session from manager.");
            
            try
            {
                // Remove session from manager
                if (_sessions.TryRemove(sessionKey, out _))
                {
                    _logger.LogDebug($"[{callId}] Session successfully removed from manager.");
                }
                else
                {
                    _logger.LogWarning($"[{callId}] Session was not found in manager during cleanup callback.");
                }

                // Release RTP ports if available
                if (receiver is RtpAudioReceiver rtpReceiver && _portAllocator != null)
                {
                    try
                    {
                        await _portAllocator.ReleasePortsAsync(callId, rtpReceiver.RtpClient, rtpReceiver.RtcpClient);
                        _logger.LogDebug($"[{callId}] RTP ports released successfully.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"[{callId}] Error releasing RTP ports during cleanup.");
                    }
                }

                // Remove from resource tracking
                if (_resourceTracking.TryRemove(callId, out var resourceInfo))
                {
                    _logger.LogDebug($"[{callId}] Resource tracking information removed. Session duration: {DateTime.UtcNow - resourceInfo.CreatedAt}");
                }

                // Trigger immediate cleanup of synchronization locks
                if (_synchronizationService != null)
                {
                    try
                    {
                        await _synchronizationService.CleanupLocksAsync(callId);
                        _logger.LogDebug($"[{callId}] Synchronization locks cleaned up.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"[{callId}] Error cleaning up synchronization locks during session cleanup.");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error during enhanced session cleanup.");
            }
        }

        /// <summary>
        /// Terminates session with comprehensive cleanup
        /// </summary>
        private async Task TerminateSessionWithCleanupAsync(ICallSession session, string sessionKey)
        {
            var callId = session.CallId;
            
            try
            {
                // Stop session gracefully
                await session.StopAsync();
                _logger.LogInformation($"[{callId}] Session stopped gracefully.");
                
                // Dispose session to free resources
                session.Dispose();
                _logger.LogDebug($"[{callId}] Session disposed successfully.");
                
                // Session cleanup callback will handle the rest
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error during graceful session termination.");
                throw;
            }
        }

        /// <summary>
        /// Force cleanup session resources even when errors occur
        /// </summary>
        private async Task ForceCleanupSessionAsync(string callId, string sessionKey)
        {
            _logger.LogWarning($"[{callId}] Forcing session cleanup due to error.");
            
            try
            {
                // Force remove from sessions dictionary
                if (_sessions.TryRemove(sessionKey, out var session))
                {
                    try
                    {
                        session.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{callId}] Error disposing session during force cleanup.");
                    }
                }

                // Force remove from resource tracking
                _resourceTracking.TryRemove(callId, out _);

                _logger.LogInformation($"[{callId}] Force cleanup completed.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error during force cleanup.");
            }
        }

        /// <summary>
        /// Track session resources for leak detection
        /// </summary>
        private void TrackSessionResources(string callId, ICallSession session)
        {
            try
            {
                var resourceInfo = new SessionResourceInfo
                {
                    CallId = callId,
                    CreatedAt = DateTime.UtcNow,
                    RtpPort = session.CurrentRtpPort,
                    RtcpPort = session.CurrentRtcpPort,
                    IsActive = true,
                    LastActivity = DateTime.UtcNow
                };

                _resourceTracking.TryAdd(callId, resourceInfo);
                _logger.LogDebug($"[{callId}] Resource tracking started: RTP={resourceInfo.RtpPort}, RTCP={resourceInfo.RtcpPort}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"[{callId}] Error setting up resource tracking.");
            }
        }

        /// <summary>
        /// Periodic health check for resource leak detection
        /// </summary>
        private void PerformHealthCheck(object state)
        {
            if (_disposed)
                return;

            try
            {
                lock (_cleanupLock)
                {
                    if (_disposed)
                        return;

                    var now = DateTime.UtcNow;
                    var staleSessions = new List<string>();
                    var orphanedResources = new List<string>();

                    // Check for stale sessions (active for more than 2 hours without activity)
                    foreach (var kvp in _resourceTracking)
                    {
                        var callId = kvp.Key;
                        var resource = kvp.Value;

                        if (resource.IsActive && (now - resource.LastActivity).TotalHours > 2)
                        {
                            staleSessions.Add(callId);
                        }
                    }

                    // Check for orphaned resources (tracked but no active session)
                    foreach (var kvp in _resourceTracking)
                    {
                        var callId = kvp.Key;
                        var hasActiveSession = _sessions.Values.Any(s => s.CallId == callId);
                        
                        if (!hasActiveSession)
                        {
                            orphanedResources.Add(callId);
                        }
                    }

                    // Log health check results
                    var activeSessions = _sessions.Count;
                    var trackedResources = _resourceTracking.Count;

                    _logger.LogInformation($"Health Check - Active sessions: {activeSessions}, Tracked resources: {trackedResources}, Stale sessions: {staleSessions.Count}, Orphaned resources: {orphanedResources.Count}");

                    // Clean up orphaned resources
                    foreach (var callId in orphanedResources)
                    {
                        if (_resourceTracking.TryRemove(callId, out var resource))
                        {
                            _logger.LogWarning($"[{callId}] Cleaned up orphaned resource tracking. Port: {resource.RtpPort}, Age: {now - resource.CreatedAt}");
                        }
                    }

                    // Log warnings for stale sessions
                    foreach (var callId in staleSessions)
                    {
                        _logger.LogWarning($"[{callId}] Detected stale session - no activity for {(now - _resourceTracking[callId].LastActivity).TotalMinutes:F1} minutes");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during health check.");
            }
        }

        /// <summary>
        /// Gets diagnostic information about resource usage
        /// </summary>
        public string GetResourceDiagnostics()
        {
            try
            {
                var activeSessions = _sessions.Count;
                var trackedResources = _resourceTracking.Count;
                var oldestSession = _resourceTracking.Values.OrderBy(r => r.CreatedAt).FirstOrDefault();
                var oldestAge = oldestSession != null ? DateTime.UtcNow - oldestSession.CreatedAt : TimeSpan.Zero;

                return $"Sessions: {activeSessions}, Tracked: {trackedResources}, Oldest: {oldestAge.TotalMinutes:F1}min";
            }
            catch
            {
                return "Error getting diagnostics";
            }
        }

        /// <summary>
        /// Disposes the CallSessionManager and all resources
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            lock (_cleanupLock)
            {
                if (_disposed)
                    return;

                _disposed = true;

                try
                {
                    // Stop health check timer
                    _healthCheckTimer?.Dispose();

                    // Dispose all sessions
                    foreach (var kvp in _sessions)
                    {
                        try
                        {
                            kvp.Value.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"[{kvp.Value.CallId}] Error disposing session during manager disposal.");
                        }
                    }

                    _sessions.Clear();
                    _resourceTracking.Clear();

                    _logger.LogInformation("CallSessionManager disposed successfully.");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error disposing CallSessionManager.");
                }
            }
        }
    }
}
