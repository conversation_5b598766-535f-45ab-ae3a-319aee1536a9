function modify_kafka_message(tag, timestamp, record)
    local payload = record.payload
    return 1, timestamp, payload
end

local count = 0
function modify_kafka_message_detail(tag, timestamp, record)
    count = count + 1
    local payload = record.payload
    payload.topic = record.topic
    payload.status = 'processed by fluent-bit, total records: '..tostring(count)
    return 1, timestamp, payload
end

function parse_syslog_pri(tag, timestamp, record)
    local pri = tonumber(record["log.syslog.pri"])
    local inner_pri = tonumber(record["log.syslog.inner_pri"])

    if pri then
        -- Rozložení vnějšího PRI
        local facility_num = math.floor(pri / 8)
        local severity_num = pri % 8

        -- Mapování facility a severity
        local facility_map = {
            [0] = "kernel", [1] = "user-level", [2] = "mail", [3] = "system-daemons",
            [4] = "security/authorization", [5] = "syslogd", [6] = "line-printer", [7] = "network-news",
            [8] = "UUCP", [9] = "clock-daemon", [10] = "security/authorization (2)", [11] = "FTP",
            [12] = "NTP", [13] = "log-audit", [14] = "log-alert", [15] = "clock-daemon (2)",
            [16] = "local0", [17] = "local1", [18] = "local2", [19] = "local3",
            [20] = "local4", [21] = "local5", [22] = "local6", [23] = "local7"
        }
        local severity_map = {
            [0] = "Emergency", [1] = "Alert", [2] = "Critical", [3] = "Error",
            [4] = "Warning", [5] = "Notice", [6] = "Informational", [7] = "Debug"
        }

        record["log.syslog.facility"] = facility_map[facility_num] or "unknown"
        record["log.syslog.severity"] = severity_map[severity_num] or "unknown"
    end

    if inner_pri then
        -- Rozložení vnitřního PRI
        local inner_facility_num = math.floor(inner_pri / 8)
        local inner_severity_num = inner_pri % 8

        record["log.syslog.inner_facility"] = facility_map[inner_facility_num] or "unknown"
        record["log.syslog.inner_severity"] = severity_map[inner_severity_num] or "unknown"
    end

    return 1, timestamp, record
end