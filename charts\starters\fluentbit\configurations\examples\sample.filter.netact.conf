# PARSER: <PERSON><PERSON><PERSON><PERSON><PERSON>ý parsing pomocí regulárn<PERSON>ho výrazu
#[PARSER]
#    Name          syslog_regex_parser
#    Format        regex
#    Regex         <(?<pri>\d+)> (?<timestamp>\w+ \d+ \d+:\d+:\d+) (?<hostname>[^\s]+) (?<appname>[^\s]+) <(?<message_id>\d+)>1 (?<iso_timestamp>[^ ]+) (?<log_dn>[^ ]+) (?<log_source>[^\s]+) (?<log_content>.*)
#    Time_Key      timestamp
# FILTER: Přejmenování položek podle konvencí (Elastic Common Schema)
#[FILTER]
#    Name                modify
#    Match               *
#    Rename              pri              log.syslog.pri
#    Rename              hostname         host.name
#    Rename              iso_timestamp    @timestamp
#    Rename              log_dn           dn
#    Rename              log_source       log.source
#    Rename              log_content      log.message
#    Rename              processName      process.name
#    Rename              userName         user.name
#    Rename              remoteIp         source.ip
#    Rename              seType           se.type
#    Rename              swVersion        origin.software_version
#    Rename              software         origin.software
#    Rename              eventStatus      event.status

# PARSER: Vn<PERSON>j<PERSON><PERSON> (syslog zpráva s vloženou zprávou)
[PARSER]
    Name          syslog_outer_regex_parser
    Format        regex
    Regex         <(?<pri>\d+)> (?<timestamp>\w+ \d+ \d+:\d+:\d+) (?<hostname>[^\s]+) (?<appname>[^\s]+) (<.*)
    Time_Key      timestamp

# FILTER: Přejmenování klíčů vnější zprávy podle konvencí
[FILTER]
    Name                modify
    Match               *
    Rename              pri              log.syslog.pri
    Rename              hostname         host.name
    Rename              appname          log.source
    Rename              timestamp        @timestamp

# PARSER: Vnitřní zpráva (druhá syslog zpráva)
[PARSER]
    Name          syslog_inner_regex_parser
    Format        regex
    Regex         <(?<inner_pri>\d+)>1 (?<iso_timestamp>[^ ]+) (?<log_dn>[^ ]+) (?<log_source>[^\s]+) (?<log_unknown>[^\s]+) (?<log_details>.*)
    Time_Key      iso_timestamp

# FILTER: Přejmenování klíčů vnitřní zprávy
[FILTER]
    Name                modify
    Match               *
    Rename              inner_pri         log.syslog.inner_pri
    Rename              iso_timestamp     @timestamp
    Rename              log_dn            log.dn
    Rename              log_source        log.source
    Rename              log_details       message

# PARSER: Key-Value parser pro vnitřní zprávu
[PARSER]
    Name              kv_parser_inner
    Format            kv
    KV_Separator      =
    KV_Pair_Separator \s+
    KV_Allow_No_Value On

# PARSER: sudo zpráva (klíč-hodnota)
[PARSER]
    Name          syslog_sudo_regex_parser
    Format        regex
    Regex         <(?<pri>\d+)> (?<timestamp>\w+ \d+ \d+:\d+:\d+) (?<hostname>[^\s]+) (?<appname>[^\s]+\[\d+\]): (?<log_content>.*)
    Time_Key      timestamp

# PARSER: Key-Value parser pro sudo klíč-hodnota páry
[PARSER]
    Name              kv_parser_sudo
    Format            kv
    KV_Separator      =
    KV_Pair_Separator \s*;\s*
    KV_Allow_No_Value On

# FILTER: Přejmenování sudo klíčů podle konvencí
[FILTER]
    Name                modify
    Match               *
    Rename              PWD              process.working_directory
    Rename              USER             user.name
    Rename              COMMAND          process.command

# FILTER: Lua skript pro rozložení PRI na facility a severity (vnější i vnitřní zpráva)
[FILTER]
    Name              lua
    Match             *
    Script            /path/to/your/parse_syslog_pri.lua
    Call              parse_syslog_pri