using System;
using System.Net;
using System.Net.Sockets;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Factory for creating instances of RtpAudioReceiver.
    /// </summary>
    public class RtpAudioReceiverFactory : IRtpAudioReceiverFactory
    {
        private readonly ILoggerFactory _loggerFactory;
        private readonly SipServerOptions _options;
        private readonly ILogger<RtpAudioReceiverFactory> _logger;
        private readonly IPortAllocator _portAllocator;
        private readonly IOptions<RtpReceiverOptions> _rtpReceiverOptions;

        public RtpAudioReceiverFactory(
            ILoggerFactory loggerFactory,
            IOptions<SipServerOptions> options,
            IPortAllocator portAllocator,
            IOptions<RtpReceiverOptions> rtpReceiverOptions)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _logger = _loggerFactory.CreateLogger<RtpAudioReceiverFactory>();
            _portAllocator = portAllocator ?? throw new ArgumentNullException(nameof(portAllocator));
            _rtpReceiverOptions = rtpReceiverOptions ?? throw new ArgumentNullException(nameof(rtpReceiverOptions));
        }

        /// <summary>
        /// Creates a new RtpAudioReceiver with optionally specified RTP/RTCP ports.
        /// </summary>
        /// <param name="callId">Call identifier for logging.</param>
        /// <param name="rtpPort">Optional RTP port to bind to.</param>
        /// <param name="rtcpPort">Optional RTCP port to bind to.</param>
        /// <returns>Instance of IAudioInputReceiver.</returns>
                /// <inheritdoc/>
                public IAudioInputReceiver CreateReceiver(string callId, IPAddress localAddress)
                {
                    return CreateReceiver(callId, null, null);
                }
        
                public IAudioInputReceiver CreateReceiver(string callId, int? rtpPort = null, int? rtcpPort = null)
        {
            var localAddress = IPAddress.Any;
            UdpClient rtpClient;
            UdpClient rtcpClient;

            if (rtpPort.HasValue && rtcpPort.HasValue)
            {
                (rtpClient, rtcpClient) = _portAllocator
                    .AllocateSpecificPairAsync(callId, localAddress, rtpPort.Value, rtcpPort.Value)
                    .GetAwaiter().GetResult();
            }
            else
            {
                (rtpClient, rtcpClient) = _portAllocator
                    .AllocateRtpPairAsync(callId, localAddress)
                    .GetAwaiter().GetResult();
            }

            ConfigureBuffers(rtpClient);
            ConfigureBuffers(rtcpClient);
            _logger.LogInformation($"[{callId}] Created RTP receiver with ports: RTP={((IPEndPoint)rtpClient.Client.LocalEndPoint).Port}, RTCP={((IPEndPoint)rtcpClient.Client.LocalEndPoint).Port}");
            return new RtpAudioReceiver(callId, rtpClient, rtcpClient, _rtpReceiverOptions, _loggerFactory.CreateLogger<RtpAudioReceiver>());
        }

        private void ConfigureBuffers(UdpClient client)
        {
            client.Client.ReceiveBufferSize = 1048576;
            client.Client.SendBufferSize = 1048576;
        }
    }
}
