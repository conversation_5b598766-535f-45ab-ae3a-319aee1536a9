using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using System.Runtime.Serialization;
using Microsoft.Extensions.Logging.Abstractions;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;

namespace voice_processing_service.Tests.Simulators.CallSessionSimulator
{
    class Program
    {
        static async Task Main(string[] args)
        {
            // Vytvoření loggeru
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddFilter("Microsoft", LogLevel.Warning)
                    .AddFilter("System", LogLevel.Warning)
                    .AddFilter("voice_processing_service", LogLevel.Debug)
                    .AddConsole();
            });
            var logger = loggerFactory.CreateLogger<Program>();

            logger.LogInformation("Starting CallSession and CallSessionManager simulation.");

            // Vytvoření CallSessionManager
            var sessionManager = new CallSessionManager(NullLogger<CallSessionManager>.Instance, loggerFactory);

            // Simulace vytvoření a ukončení hovorů
            await SimulateCallsAsync(sessionManager, loggerFactory, logger);

            logger.LogInformation("Simulation completed.");
        }

        static async Task SimulateCallsAsync(
            CallSessionManager sessionManager,
            ILoggerFactory loggerFactory,
            ILogger logger)
        {
            // Vytvoření CancellationTokenSource pro ukončení simulace
            using var cts = new CancellationTokenSource();
            cts.CancelAfter(TimeSpan.FromSeconds(30)); // Simulace bude běžet 30 sekund

            // Simulace vytvoření několika hovorů
            var call1Task = CreateAndRunCallAsync(sessionManager, "call1", loggerFactory, logger, cts.Token);
            await Task.Delay(2000); // Pauza mezi hovory
            var call2Task = CreateAndRunCallAsync(sessionManager, "call2", loggerFactory, logger, cts.Token);
            await Task.Delay(2000); // Pauza mezi hovory
            var call3Task = CreateAndRunCallAsync(sessionManager, "call3", loggerFactory, logger, cts.Token);

            // Výpis aktivních hovorů
            logger.LogInformation("Active calls:");
            foreach (var session in sessionManager.GetAllSessions())
            {
                logger.LogInformation($"- {session.CallId} (started at {session.StartTime})");
            }

            // Ukončení hovoru call2 po 5 sekundách
            await Task.Delay(5000);
            logger.LogInformation("Terminating call2...");
            await sessionManager.TerminateSessionAsync("call2");

            // Výpis aktivních hovorů po ukončení call2
            logger.LogInformation("Active calls after terminating call2:");
            foreach (var session in sessionManager.GetAllSessions())
            {
                logger.LogInformation($"- {session.CallId} (started at {session.StartTime})");
            }

            // Čekání na ukončení všech hovorů
            try
            {
                await Task.WhenAll(call1Task, call2Task, call3Task);
                logger.LogInformation("All calls completed.");
            }
            catch (OperationCanceledException)
            {
                logger.LogInformation("Simulation was cancelled.");
            }
        }

        static async Task CreateAndRunCallAsync(
            CallSessionManager sessionManager,
            string callId,
            ILoggerFactory loggerFactory,
            ILogger logger,
            CancellationToken cancellationToken)
        {
            logger.LogInformation($"Creating call {callId}...");


            // Vytvoření INVITE požadavku
            var inviteRequest = new SIPRequest(SIPMethodsEnum.INVITE, SIPURI.ParseSIPURI($"sip:dummy.com"));
            // Vytvoření SIPServerUserAgent
            var sipTransport = new SIPTransport();
            var outboundProxy = new SIPEndPoint(SIPProtocolsEnum.udp, new System.Net.IPEndPoint(System.Net.IPAddress.Loopback, 5060));
            var uasTransaction = new UASInviteTransaction(sipTransport, inviteRequest, outboundProxy);
            var userAgent = new SIPServerUserAgent(sipTransport, null, uasTransaction, null);

            // Tovární metody pro vytvoření přijímače a procesoru
            Func<IAudioInputReceiver> receiverFactory = () =>
            {
                // Vytvoření mock přijímače
                var mockReceiver = new MockAudioInputReceiver(callId, loggerFactory.CreateLogger<MockAudioInputReceiver>());
                return mockReceiver;
            };

            Func<IAudioProcessor> processorFactory = () =>
            {
                // Vytvoření mock procesoru
                var mockProcessor = new MockAudioProcessor(callId, loggerFactory.CreateLogger<MockAudioProcessor>());
                return mockProcessor;
            };

            // Vytvoření session
            var session = await sessionManager.CreateSessionAsync(userAgent, inviteRequest, receiverFactory, processorFactory);

            // Spuštění session
            await session.StartAsync(cancellationToken);

            logger.LogInformation($"Call {callId} started.");

            // Simulace trvání hovoru (10-20 sekund)
            var callDuration = new Random().Next(10, 20);
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(callDuration), cancellationToken);
                logger.LogInformation($"Call {callId} duration ({callDuration}s) elapsed, terminating...");
                await sessionManager.TerminateSessionAsync(callId);
            }
            catch (OperationCanceledException)
            {
                logger.LogInformation($"Call {callId} was cancelled.");
            }
        }
    }

    // Mock třídy pro testování

    class MockAudioInputReceiver : IAudioInputReceiver
    {
        private readonly string _callId;
        private readonly ILogger<MockAudioInputReceiver> _logger;
        private bool _disposed = false;

        public System.Net.IPEndPoint RtpLocalEndPoint => new System.Net.IPEndPoint(System.Net.IPAddress.Loopback, 10000);
        public System.Net.IPEndPoint RtcpLocalEndPoint => new System.Net.IPEndPoint(System.Net.IPAddress.Loopback, 10001);

        public MockAudioInputReceiver(string callId, ILogger<MockAudioInputReceiver> logger)
        {
            _callId = callId;
            _logger = logger;
            _logger.LogInformation($"[{_callId}] MockAudioInputReceiver created.");
        }

        public Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(MockAudioInputReceiver));
            }

            _logger.LogInformation($"[{_callId}] MockAudioInputReceiver started listening.");

            // Simulace příjmu audio dat
            return Task.Run(async () =>
            {
                try
                {
                    while (!cancellationToken.IsCancellationRequested)
                    {
                        // Simulace příjmu audio dat každých 20ms
                        await Task.Delay(20, cancellationToken);

                        // Vytvoření náhodných audio dat
                        byte[] audioData = new byte[160];
                        new Random().NextBytes(audioData);

                        // Přidání dat do bufferu
                        buffer.Add(audioData);
                    }
                }
                catch (OperationCanceledException)
                {
                    // Očekávaná výjimka při zrušení
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{_callId}] Error in MockAudioInputReceiver.");
                }
                finally
                {
                    _logger.LogInformation($"[{_callId}] MockAudioInputReceiver stopped listening.");
                }
            });
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            _logger.LogInformation($"[{_callId}] MockAudioInputReceiver disposed.");
        }
    }

    class MockAudioProcessor : IAudioProcessor
    {
        private readonly string _callId;
        private readonly ILogger<MockAudioProcessor> _logger;
        private bool _disposed = false;

        public string ProcessorId => $"MOCK_{_callId}";

        public MockAudioProcessor(string callId, ILogger<MockAudioProcessor> logger)
        {
            _callId = callId;
            _logger = logger;
            _logger.LogInformation($"[{_callId}] MockAudioProcessor created.");
        }

        public Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(MockAudioProcessor));
            }

            _logger.LogInformation($"[{_callId}] MockAudioProcessor started processing.");

            // Simulace zpracování audio dat
            return Task.Run(async () =>
            {
                try
                {
                    int totalBytesProcessed = 0;
                    while (!cancellationToken.IsCancellationRequested)
                    {
                        if (buffer.TryTake(out var audioData, 100, cancellationToken))
                        {
                            totalBytesProcessed += audioData.Length;
                            _logger.LogDebug($"[{_callId}] Processed {audioData.Length} bytes (total: {totalBytesProcessed}).");
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    // Očekávaná výjimka při zrušení
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{_callId}] Error in MockAudioProcessor.");
                }
                finally
                {
                    _logger.LogInformation($"[{_callId}] MockAudioProcessor stopped processing.");
                }
            });
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            _logger.LogInformation($"[{_callId}] MockAudioProcessor disposed.");
        }
    }
}