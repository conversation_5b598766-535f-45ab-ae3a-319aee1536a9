using System;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Integration.Services
{
    public class CallSessionIntegrationTests
    {
        // Simple mock implementations for integration
        private class MockAudioInputReceiver : IAudioInputReceiver, IDisposable
        {
            public System.Net.IPEndPoint RtpLocalEndPoint => new System.Net.IPEndPoint(System.Net.IPAddress.Loopback, 0);
            public System.Net.IPEndPoint RtcpLocalEndPoint => new System.Net.IPEndPoint(System.Net.IPAddress.Loopback, 1);
            public Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken) =>
                Task.CompletedTask;
            public Task UpdateConfigurationAsync(System.Net.IPEndPoint newRemoteEndpoint, string[] supportedCodecs) =>
                Task.CompletedTask;
            public void Dispose() { }
        }

        private class MockAudioProcessor : IAudioProcessor, IDisposable
        {
            public string ProcessorId => "MOCK_PROC";
            public Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken) =>
                Task.CompletedTask;
            public void Dispose() { }
        }

        private SIPServerUserAgent CreateUserAgent() =>
            (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));

        private SIPRequest CreateInvite(string callId)
        {
            var invite = (SIPRequest)FormatterServices.GetUninitializedObject(typeof(SIPRequest));
            var header = (SIPHeader)FormatterServices.GetUninitializedObject(typeof(SIPHeader));
            header.CallId = callId;
            invite.Header = header;
            return invite;
        }

        [Fact]
        public async Task CreateAndTerminateSession_RemovesSessionFromManager()
        {
            var manager = new CallSessionManager();
            var userAgent = CreateUserAgent();
            var invite = CreateInvite("CALL_TEST");
            var receiver = new MockAudioInputReceiver();
            var processor = new MockAudioProcessor();

            var session = await manager.CreateSessionAsync(
                userAgent,
                invite,
                () => receiver,
                () => processor);

            // Start and then terminate
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(100));
            await session.StartAsync(cts.Token);

            await manager.TerminateSessionAsync(session.CallId);

            Assert.Null(manager.GetSession(session.CallId));
        }

        [Fact]
        public async Task MultipleConcurrentSessions_LifecycleManagement()
        {
            var manager = new CallSessionManager();
            var ua = CreateUserAgent();

            // Session A
            var inviteA = CreateInvite("A");
            var sessionA = await manager.CreateSessionAsync(ua, inviteA, () => new MockAudioInputReceiver(), () => new MockAudioProcessor());
            await sessionA.StartAsync(CancellationToken.None);

            // Session B
            var inviteB = CreateInvite("B");
            var sessionB = await manager.CreateSessionAsync(ua, inviteB, () => new MockAudioInputReceiver(), () => new MockAudioProcessor());
            await sessionB.StartAsync(CancellationToken.None);

            // Terminate A
            await manager.TerminateSessionAsync("A");

            var remaining = manager.GetAllSessions().Select(s => s.CallId);
            Assert.DoesNotContain("A", remaining);
            Assert.Contains("B", remaining);
        }
    }
}