using voice_processing_service.Models;
using SIPSorcery.SIP;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Interface for managing SIP registrations.
    /// </summary>
    public interface ISipRegistrationManager
    {
        /// <summary>
        /// Processes a SIP REGISTER request and returns the appropriate response.
        /// </summary>
        /// <param name="request">The SIP REGISTER request.</param>
        /// <returns>The SIP response to send back.</returns>
        Task<SIPResponse> ProcessRegistrationAsync(SIPRequest request);

        /// <summary>
        /// Gets all active registrations.
        /// </summary>
        /// <returns>Collection of active registrations.</returns>
        IEnumerable<SipRegistration> GetActiveRegistrations();

        /// <summary>
        /// Gets a specific registration by user URI.
        /// </summary>
        /// <param name="userUri">The user URI to search for.</param>
        /// <returns>The registration if found, null otherwise.</returns>
        SipRegistration? GetRegistration(string userUri);

        /// <summary>
        /// Removes expired registrations.
        /// </summary>
        /// <returns>Number of registrations removed.</returns>
        Task<int> CleanupExpiredRegistrationsAsync();

        /// <summary>
        /// Gets the total number of active registrations.
        /// </summary>
        int ActiveRegistrationCount { get; }
    }
}
