using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting; // Add this line
using voice_processing_service.Interfaces;
using voice_processing_service.Services;
using Xunit;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using System.Net.Sockets;

namespace voice_processing_service.Tests.Integration.DI
{
    public class ServiceRegistrationTests
    {
        [Fact]
        public void TestServiceRegistration()
        {
            // Arrange
            var services = new ServiceCollection();
            services.AddLogging();
            services.AddSingleton<ICallSessionManager>(sp =>
                new CallSessionManager(
                    NullLogger<CallSessionManager>.Instance,
                    NullLoggerFactory.Instance));
            services.AddSingleton<IOptions<SipServerOptions>>(Options.Create(new SipServerOptions()));
            services.AddSingleton<Func<string, int, int, IAudioInputReceiver>>(_ => (callId, rtp, rtcp) => null!);
            services.AddSingleton<Func<string, IAudioProcessor>>(_ => _ => null!);
            services.AddSingleton<ILoggerFactory>(_ => NullLoggerFactory.Instance);
            services.AddSingleton(typeof(ILogger<>), typeof(NullLogger<>));
            // Add dummy implementations for IPortAllocator and IRtpAudioReceiverFactory
            services.AddSingleton<IPortAllocator>(_ => new DummyPortAllocator());
            services.AddSingleton<IRtpAudioReceiverFactory>(_ => new DummyRtpAudioReceiverFactory());
            services.AddHostedService<SipServerService>(sp =>
                new SipServerService(
                    NullLogger<SipServerService>.Instance,
                    sp.GetRequiredService<ICallSessionManager>(),
                    sp.GetRequiredService<IOptions<SipServerOptions>>(),
                    sp.GetRequiredService<ILoggerFactory>(),
                    sp.GetRequiredService<IPortAllocator>(),
                    sp.GetRequiredService<IRtpAudioReceiverFactory>(),
                    sp.GetRequiredService<Func<string, IAudioProcessor>>(),
                    (callId, caller, called) => null!,
                    new SipRegistrationManager(NullLogger<SipRegistrationManager>.Instance, sp.GetRequiredService<IOptions<SipServerOptions>>()),
                    new CallSynchronizationService(NullLogger<CallSynchronizationService>.Instance)));

            var serviceProvider = services.BuildServiceProvider();

            // Act & Assert
            Assert.NotNull(serviceProvider.GetService<ICallSessionManager>());
            Assert.NotNull(serviceProvider.GetService<IHostedService>());

        }
    
        // Dummy implementations for testing
        public class DummyPortAllocator : IPortAllocator
        {
            public int AllocatePort() => 0;
            public void ReleasePort(int port) { }

            // Dummy implementations for missing interface members
            public Task<(UdpClient rtpClient, UdpClient rtcpClient)> AllocateRtpPairAsync(string callId, System.Net.IPAddress ip)
            {
                return Task.FromResult<(UdpClient, UdpClient)>((null!, null!));
            }

            public Task<(UdpClient rtpClient, UdpClient rtcpClient)> AllocateSpecificPairAsync(string callId, System.Net.IPAddress ip, int rtpPort, int rtcpPort)
            {
                return Task.FromResult<(UdpClient, UdpClient)>((null!, null!));
            }

            public Task ReleasePortsAsync(string callId, UdpClient rtp, UdpClient rtcp)
            {
                return Task.CompletedTask;
            }
        }
    
        public class DummyRtpAudioReceiverFactory : IRtpAudioReceiverFactory
        {
            public object Create() => null!;

            public IAudioInputReceiver CreateReceiver(string callId, int? rtpPort, int? rtcpPort)
            {
                return null!;
            }

            public IAudioInputReceiver CreateReceiver(string callId, System.Net.IPAddress ip)
            {
                return null!;
            }
        }
    }
}
