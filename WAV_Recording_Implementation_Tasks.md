# WAV Recording Implementation Tasks

## Phase 1: Core Components Implementation

### Task 1: Create BroadcastingAudioBuffer
**File**: `voice-processing-service/Services/BroadcastingAudioBuffer.cs`
```csharp
// Implements IAudioBuffer
// Broadcasts audio packets from source to multiple target buffers
// Key methods:
// - Add(byte[] data) - adds to all target buffers
// - TryTake() - not directly used (targets handle their own)
// - CompleteAdding() - completes all target buffers
```

### Task 2: Create CompositeAudioProcessor
**File**: `voice-processing-service/Services/CompositeAudioProcessor.cs`
```csharp
// Implements IAudioProcessor
// Manages multiple child processors in parallel
// Key responsibilities:
// - Create BroadcastingAudioBuffer for data distribution
// - Start all child processors with their own buffers
// - Coordinate lifecycle (Initialize, StartProcessing, Dispose)
// - Handle errors from individual processors gracefully
```

### Task 3: Create CompositeAudioProcessorFactory
**File**: `voice-processing-service/Services/CompositeAudioProcessorFactory.cs`
```csharp
// Creates CompositeAudioProcessor instances
// Dependencies:
// - PhonexiaSttProcessorFactory
// - WavAudioProcessorFactory
// - IOptions<SipServerOptions>
// Logic:
// - Always includes STT processor
// - Conditionally includes WAV processor based on config
```

### Task 4: Update WavAudioProcessorFactory
**File**: `voice-processing-service/Services/WavAudioProcessorFactory.cs`
**Changes**:
- Add constructor parameters for caller/called party
- Update filename generation to include metadata
- Create date-based subdirectories

## Phase 2: Configuration Updates

### Task 5: Update Configuration Model
**File**: `voice-processing-service/Configuration/SipServerOptions.cs`
**Add**:
```csharp
public class WavRecordingOptions
{
    public bool Enabled { get; set; } = false;
    public string Directory { get; set; } = "RecordedCalls";
    public int MaxFileSizeMB { get; set; } = 500;
    public int RetentionDays { get; set; } = 30;
    public bool IncludeMetadata { get; set; } = true;
    public string FileNamePattern { get; set; } = "call_{callId}_{caller}_{called}_{timestamp}";
}

// In SipServerOptions:
public WavRecordingOptions WavRecording { get; set; } = new();
```

### Task 6: Update appsettings.json
**File**: `voice-processing-service/appsettings.json`
**Add**:
```json
"SipServer": {
  "WavRecording": {
    "Enabled": true,
    "Directory": "RecordedCalls",
    "MaxFileSizeMB": 500,
    "RetentionDays": 30,
    "IncludeMetadata": true
  }
}
```

## Phase 3: Dependency Injection Updates

### Task 7: Update Program.cs
**File**: `voice-processing-service/Program.cs`
**Changes**:
```csharp
// Line 49-50: Keep existing factories
builder.Services.AddSingleton<WavAudioProcessorFactory>();
builder.Services.AddSingleton<PhonexiaSttProcessorFactory>();

// Add new composite factory
builder.Services.AddSingleton<CompositeAudioProcessorFactory>();

// Line 52-56: Update enhanced processor factory
builder.Services.AddTransient<Func<string, string, string, IAudioProcessor>>(sp =>
{
    var factory = sp.GetRequiredService<CompositeAudioProcessorFactory>();
    return (callId, callerParty, calledParty) => factory.CreateProcessor(callId, callerParty, calledParty);
});
```

## Phase 4: Testing

### Task 8: Unit Tests for CompositeAudioProcessor
**File**: `voice-processing-service.Tests/Unit/Services/CompositeAudioProcessorTests.cs`
**Test Cases**:
- Parallel processor execution
- Error isolation between processors
- Proper resource disposal
- Buffer broadcasting verification

### Task 9: Unit Tests for BroadcastingAudioBuffer
**File**: `voice-processing-service.Tests/Unit/Services/BroadcastingAudioBufferTests.cs`
**Test Cases**:
- Data duplication to all targets
- Completion propagation
- Backpressure handling
- Thread safety

### Task 10: Integration Tests
**File**: `voice-processing-service.Tests/Integration/Services/WavRecordingIntegrationTests.cs`
**Test Cases**:
- End-to-end call recording
- Both WAV and STT output verification
- Configuration enable/disable
- File system operations

## Phase 5: Documentation and Deployment

### Task 11: Update Docker Configuration
**File**: `voice-processing-service/Dockerfile`
**Ensure**:
- Recordings directory exists
- Proper permissions set
- Volume mount point configured

### Task 12: Update docker-compose.yml
**File**: `docker-compose.yml`
**Add**:
```yaml
volumes:
  - ./data/recordings:/app/recordings
environment:
  - SipServer__WavRecording__Enabled=true
```

## Implementation Order

1. **Core Infrastructure** (Tasks 1-3)
   - BroadcastingAudioBuffer
   - CompositeAudioProcessor
   - CompositeAudioProcessorFactory

2. **Configuration** (Tasks 5-6)
   - Update configuration models
   - Update appsettings.json

3. **Integration** (Tasks 4, 7)
   - Update WavAudioProcessorFactory
   - Wire up in Program.cs

4. **Testing** (Tasks 8-10)
   - Unit tests
   - Integration tests

5. **Deployment** (Tasks 11-12)
   - Docker updates
   - Documentation

## File Modification Summary

### New Files to Create:
1. `voice-processing-service/Services/BroadcastingAudioBuffer.cs`
2. `voice-processing-service/Services/CompositeAudioProcessor.cs`
3. `voice-processing-service/Services/CompositeAudioProcessorFactory.cs`
4. `voice-processing-service.Tests/Unit/Services/CompositeAudioProcessorTests.cs`
5. `voice-processing-service.Tests/Unit/Services/BroadcastingAudioBufferTests.cs`
6. `voice-processing-service.Tests/Integration/Services/WavRecordingIntegrationTests.cs`

### Files to Modify:
1. `voice-processing-service/Services/WavAudioProcessorFactory.cs` - Add metadata support
2. `voice-processing-service/Configuration/SipServerOptions.cs` - Add WavRecordingOptions
3. `voice-processing-service/appsettings.json` - Add WavRecording configuration
4. `voice-processing-service/Program.cs` - Update DI registration
5. `voice-processing-service/Dockerfile` - Ensure recordings directory
6. `docker-compose.yml` - Add volume mount and environment variables

## Code Implementation Guidelines

### Error Handling Pattern
```csharp
try
{
    // Processor-specific operations
}
catch (Exception ex)
{
    _logger.LogError(ex, "Error in {Processor}: {Message}", 
        processorName, ex.Message);
    // Don't rethrow - allow other processors to continue
}
```

### Resource Management Pattern
```csharp
public void Dispose()
{
    // Dispose all child processors
    foreach (var processor in _processors)
    {
        try
        {
            processor?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing processor");
        }
    }
    
    // Dispose all buffers
    foreach (var buffer in _buffers)
    {
        buffer?.Dispose();
    }
}
```

### Async Coordination Pattern
```csharp
public async Task StartProcessingAsync(IAudioBuffer sourceBuffer, CancellationToken ct)
{
    var tasks = new List<Task>();
    
    foreach (var (processor, buffer) in _processors.Zip(_buffers))
    {
        tasks.Add(Task.Run(async () =>
        {
            try
            {
                await processor.StartProcessingAsync(buffer, ct);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Processor failed");
            }
        }, ct));
    }
    
    // Start broadcasting task
    var broadcastTask = StartBroadcastingAsync(sourceBuffer, ct);
    
    await Task.WhenAll(tasks.Concat(new[] { broadcastTask }));
}
```

## Success Metrics

1. **Functional Validation**
   - [ ] WAV files created for all test calls
   - [ ] STT continues to work without issues
   - [ ] Configuration toggles work correctly
   - [ ] Metadata included in filenames

2. **Performance Validation**
   - [ ] Memory usage increase < 5%
   - [ ] No STT latency degradation
   - [ ] CPU usage increase < 10%
   - [ ] Disk I/O within acceptable limits

3. **Reliability Validation**
   - [ ] Graceful handling of disk full scenarios
   - [ ] Proper cleanup on session termination
   - [ ] No memory leaks after extended operation
   - [ ] Recovery from transient failures

## Notes for Implementation Team

1. **Start with the CompositeAudioProcessor** - This is the core component that enables parallel processing.

2. **Test BroadcastingAudioBuffer thoroughly** - This is critical for data integrity. Ensure no packets are lost during broadcasting.

3. **Make WAV recording truly optional** - The system should work identically whether WAV recording is enabled or disabled.

4. **Consider performance implications** - With two processors per call, resource usage will increase. Monitor carefully during testing.

5. **Implement comprehensive logging** - This will be crucial for debugging issues in production.

6. **Plan for storage management** - WAV files can be large. Consider implementing automatic cleanup based on retention policies.