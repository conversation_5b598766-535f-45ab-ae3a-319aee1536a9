# RTP/HTTP/WebSocket Streams

[Diagram illustrating the stream process was present here in the original documentation]

1.  #### Open RTP/HTTP/WebSocket input stream

    Send request to open RTP input stream (POST /input_stream/rtp), HTTP chunked input stream (POST /input_stream/http) or WebSocket input stream (POST /input_stream/websocket).

    Response to this request should contain Input stream ID and number of opened Port (only for RTP input streams). Server has limited number of free ports for streams and if this count is exceed, HTTP status 403 (Forbidden) is returned. Stream is automatically closed, if no data is sent for longer than 10sec (default value, can be configured on server settings).

2.  #### Bind technology to opened input stream

    Send request to bind technology to opened stream (e.g. POST /technologies/stt/input_stream). Response to this request should contain Task ID (used in request to get result).

    It is possible to unbind the technology in any moment of the stream duration (e.g. DELETE /technologies/stt/input_stream). If technology is unbound, getting results (e.g. GET /technologies/stt/input_stream) will not be possible.

    It is possible to bind more technologies to one opened stream.

    **Result modes (only for STT, KWS, VAD)**

    Results of some technologies can be obtained in different ways. Result mode can be set when binding technology to the input stream and can not be changed during runtime of a task.

    - **Complete** - everytime is result obtained, contains all scores/words. Supported technologies - [KWS Stream](#%2Ftechnologies%2Fkeywordspotting%2Finput_stream), [VAD Stream](#%2Ftechnologies%2Fvad%2Finput_stream), [STT Stream](#%2Ftechnologies%2Fstt%2Finput_stream).
    - **Incremental** - result contains scores/words processed since last getting result. Supported technologies - [KWS Stream](#%2Ftechnologies%2Fkeywordspotting%2Finput_stream), [VAD Stream](#%2Ftechnologies%2Fvad%2Finput_stream), [STT Stream](#%2Ftechnologies%2Fstt%2Finput_stream).

3.  #### Sending RTP/HTTP input stream data

    If the input stream is successfully opened and client has Input stream ID and port (given in step 1.), client can start sending data into the stream.

    - **RTP** - data are being sent over RTP protocol. For more information, see [RFC 3550](https://tools.ietf.org/html/rfc3550).

      Supported RTP Payload types are:

      - **0** (PCMU, Little-Endian, 8000 Hz, 1 channel)
      - **8** (PCMA, Little-Endian, 8000 Hz, 1 channel)
      - **10** (L16, Little-Endian, 44100 Hz, 2 channels)
      - **11** (L16, Little-Endian, 44100 Hz, 1 channel)
      - **35** (L16, Little-Endian, 8000 Hz, 2 channels)
      - **36** (L16, Little-Endian, 8000 Hz, 1 channel)

    - **HTTP** - data are being sent by request `PUT /input_stream/http`.

      Supported format in HTTP chunked stream is RAW s16le. Frequency and number of channels is defined by request `POST /input_stream/http`

    - **WebSocket** - data are being sent over WebSocket opened by request `GET /input_stream/websocket`.

      Supported format in WebSocket stream is RAW s16le. Frequency and number of channels is defined in request's query parameters.

    **Important notice:** Multichannel input stream can be used only in connection with [Time Analysis Stream Technology](#%2Ftechnologies%2Ftimeanalysis%2Finput_stream). Other stream technologies currently does not support multichannel stream.

4.  #### Getting technology results

    Getting the results is possible using one of following ways:

    ### Polling

    Simultaneously while streaming data, it is possible to get current results (e.g. `GET /technologies/stt/input_stream`). Getting results is possible until the result contains `is_last=true` flag. The point is that even if the input stream gets closed, technologies can still continue processing of remaining data and additional results can be obtained.

    ### WebSocket

    The server allows to open WebSocket for receiving results during stream task processing. To send WebSocket handshake request, use GET request of used technology (e.g. `GET /technologies/stt/input_stream`).

    WebSocket handshake request should contain the following parameters in the header: Upgrade, Connection, Sec-WebSocket-Version, Sec-WebSocket-Key and session parameter (X-SessionID) or basic authorization (Authorization).

    ```
    GET /technologies/stt/input_stream?task=54048266-b694-440a-8a9e-0cef5668efea&interval=0.33 HTTP/1.1
    Host: server.example.com
    Upgrade: websocket
    Connection: Upgrade
    Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==
    Sec-WebSocket-Version: 13
    X-SessionID: 258f505c-a6fa-4c3f-8a87-b048874ac6aa
    Accept: application/json
    ```

    Partial results of the streaming task are sent periodically via WebSocket message. The query parameter `interval` sets minimum interval of sent messages; actual interval may be greater, depending on input data. WebSocket is opened until the streaming task is finished. The last result contains `is_last=true` flag. WebSocket is closed after the last result by server.

    If an error occurs during stream processing, the error message is sent through WebSocket and then the connection is immediately closed.

    Only one WebSocket can be opened for one streaming technology task and it is not possible to combine WebSocket with Webhook.

    ### Webhook

    The server allows to register Webhook callback for receiving results during stream task processing. To register Webhook callback, send GET request of used technology (e.g. `GET /technologies/stt/input_stream`) and use `X-WebhookTarget` HTTP header containing webhook URL.

    Example HTTP request for registration of Webhook callback (the technology is expected to run):

    ```
    GET /technologies/stt/input_stream?task=54048266-b694-440a-8a9e-0cef5668efea&interval=0.33 HTTP/1.1
    Host: server.example.com:8600
    Accept: application/json
    X-SessionID: 27508c43-1f48-46c9-bdaa-f00cec181444
    X-WebhookTarget: http://webhook-target.example.com:8601
    ```

    The server returns a response with HTTP code 202.

    Partial results of the streaming task are sent periodically to Webhook callback. The query parameter `interval` sets interval of Webhook calls. Webhook callback is automatically unregistered after the last result is delivered (last result contains `is_last=true` flag).

    If an error occurs during stream processing, the error message is sent to Webhook callback and then Webhook callback is immediately unregistered.

    Only one Webhook callback can be registered for one streaming technology task and it is not possible to combine Webhook with WebSocket.

    ***

    See also specific technologies for more information:

    - `GET /technologies/keywordspotting/input_stream`
    - `GET /technologies/speakerid/input_stream`
    - `GET /technologies/speakerid4/input_stream`
    - `GET /technologies/stt/input_stream`
    - `GET /technologies/timeanalysis/input_stream`
    - `GET /technologies/vad/input_stream`
    - `GET /technologies/sqestim/input_stream`

5.  #### Close RTP/HTTP/WebSocket input stream

    After all the audio data are sent it is recommended to send request to close the stream (`DELETE /input_stream/rtp` or `DELETE /input_stream/http`). The input stream is automatically closed, if no data is sent for more than 10 seconds (default value, can be configured via server settings).
    In addition, WebSocket stream is stopped immediately

    - after WebSocket connection is closed by client, or,
    - in case of error, by server.
