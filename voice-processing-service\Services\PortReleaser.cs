using System.Net;
using System.Net.Sockets;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;

//namespace voice_processing_service.Services
//{
//    /// <summary>
//    /// Služba pro uvolnění portů, kter<PERSON> mohou být <PERSON>.
//    /// </summary>
//    public class PortReleaser
//    {
//        private readonly ILogger<PortReleaser> _logger;
//        private readonly SipServerOptions _options;

//        public PortReleaser(ILogger<PortReleaser> logger, IOptions<SipServerOptions> options)
//        {
//            _logger = logger;
//            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
//        }

//        /// <summary>
//        /// Pokusí se uvolnit port tím, že ho otevře a ihned zavře.
//        /// </summary>
//        /// <param name="port">Port, který se má uvolnit.</param>
//        /// <param name="ipAddress">IP adresa, na které se má port uvolnit.</param>
//        /// <returns>True, pokud byl port úspěšně uvolněn, jinak false.</returns>
//        public bool TryReleasePort(int port, IPAddress ipAddress)
//        {
//            if (port <= 0)
//            {
//                _logger.LogWarning($"Invalid port number: {port}");
//                return false;
//            }

//            try
//            {
//                // Pokusíme se vytvořit a zavřít UDP socket na daném portu
//                using var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
                
//                // Nastavíme ReuseAddress, aby bylo možné port znovu použít
//                socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                
//                // Pokusíme se navázat socket na daný port
//                socket.Bind(new IPEndPoint(ipAddress, port));
                
//                // Ihned socket zavřeme, čímž uvolníme port
//                socket.Close();
                
//                _logger.LogInformation($"Successfully released port {port}");
//                return true;
//            }
//            catch (SocketException ex)
//            {
//                _logger.LogWarning($"Failed to release port {port}: {ex.Message} (Error code: {ex.SocketErrorCode})");
//                return false;
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, $"Unexpected error when releasing port {port}");
//                return false;
//            }
//        }

//        /// <summary>
//        /// Pokusí se uvolnit rozsah portů.
//        /// </summary>
//        /// <param name="startPort">Počáteční port rozsahu.</param>
//        /// <param name="endPort">Koncový port rozsahu.</param>
//        /// <param name="ipAddress">IP adresa, na které se mají porty uvolnit.</param>
//        /// <returns>Počet úspěšně uvolněných portů.</returns>
//        public int TryReleasePortRange(int startPort, int endPort, IPAddress ipAddress)
//        {
//            int releasedCount = 0;
            
//            for (int port = startPort; port <= endPort; port++)
//            {
//                if (TryReleasePort(port, ipAddress))
//                {
//                    releasedCount++;
//                }
//            }
            
//            _logger.LogInformation($"Released {releasedCount} ports from range {startPort}-{endPort}");
//            return releasedCount;
//        }
//    }
//}
