{{- if .Values.nginx.transport.enabled }}
apiVersion: k8s.nginx.org/v1alpha1
kind: TransportServer
metadata:
  name: nginx-demo
spec:
  action:
    pass: {{ .Values.nginx.transport.upstream.name }}
  listener:
    name: {{ .Values.nginx.transport.listener.name }}
    protocol: {{ .Values.nginx.transport.listener.protocol }}
  upstreams:
    - name: {{ .Values.nginx.transport.upstream.name }}
      port: {{ .Values.nginx.transport.upstream.port }}
      service: {{ .Values.nginx.transport.upstream.service }}
{{- end }}