using System.Collections.Concurrent;
using System.Net;
using Microsoft.Extensions.Options;
using SIPSorcery.SIP;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Models;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Manages SIP registrations for the server.
    /// </summary>
    public class SipRegistrationManager : ISipRegistrationManager
    {
        private readonly ILogger<SipRegistrationManager> _logger;
        private readonly SipServerOptions _options;
        private readonly ConcurrentDictionary<string, SipRegistration> _registrations;
        private readonly Timer _cleanupTimer;

        public SipRegistrationManager(ILogger<SipRegistrationManager> logger, IOptions<SipServerOptions> options)
        {
            _logger = logger;
            _options = options.Value;
            _registrations = new ConcurrentDictionary<string, SipRegistration>();

            // Setup cleanup timer to run every 5 minutes
            _cleanupTimer = new Timer(async _ => await CleanupExpiredRegistrationsAsync(), 
                null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        public async Task<SIPResponse> ProcessRegistrationAsync(SIPRequest request)
        {
            try
            {
                var userUri = request.Header.From.FromURI.ToString();
                var callId = request.Header.CallId;

                _logger.LogInformation($"Processing REGISTER request for {userUri}, Call-ID: {callId}");

                // Extract user part (e.g., "17999" from "sip:<EMAIL>")
                var userPart = request.Header.From.FromURI.User;

                // Check if this endpoint is allowed to register
                if (!_options.AllowedRegistrationEndpoints.Contains(userPart))
                {
                    _logger.LogWarning($"Registration denied for unauthorized endpoint: {userPart}");
                    return SIPResponse.GetResponse(request, SIPResponseStatusCodesEnum.Forbidden, 
                        "Endpoint not authorized for registration");
                }

                // Get expiry from Expires header or Contact header
                var expiry = GetExpiryFromRequest(request);

                // Handle unregistration (expiry = 0)
                if (expiry == 0)
                {
                    return HandleUnregistration(request, userUri);
                }

                // Validate expiry bounds
                expiry = ValidateExpiry(expiry);

                // Create or update registration
                var registration = CreateOrUpdateRegistration(request, userUri, expiry);

                _logger.LogInformation($"Registration successful for {userUri}, expires in {expiry} seconds");

                // Create successful response
                var response = SIPResponse.GetResponse(request, SIPResponseStatusCodesEnum.Ok, null);
                
                // Add Contact header with expiry
                if (request.Header.Contact?.Count > 0)
                {
                    var contact = request.Header.Contact[0];
                    contact.Expires = expiry;
                    response.Header.Contact = new List<SIPContactHeader> { contact };
                }

                // Add Date header
                response.Header.Date = DateTime.UtcNow.ToString("r");

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing REGISTER request");
                return SIPResponse.GetResponse(request, SIPResponseStatusCodesEnum.InternalServerError, 
                    "Internal server error");
            }
        }

        private int GetExpiryFromRequest(SIPRequest request)
        {
            // First check Expires header
            if (request.Header.Expires != -1)
            {
                return (int)request.Header.Expires;
            }

            // Then check Contact header expires parameter
            if (request.Header.Contact?.Count > 0)
            {
                var contact = request.Header.Contact[0];
                if (contact.Expires != -1)
                {
                    return (int)contact.Expires;
                }
            }

            // Default expiry
            return _options.DefaultRegistrationExpiry;
        }

        private int ValidateExpiry(int requestedExpiry)
        {
            if (requestedExpiry < _options.MinRegistrationExpiry)
            {
                _logger.LogDebug($"Requested expiry {requestedExpiry} too low, using minimum {_options.MinRegistrationExpiry}");
                return _options.MinRegistrationExpiry;
            }

            if (requestedExpiry > _options.MaxRegistrationExpiry)
            {
                _logger.LogDebug($"Requested expiry {requestedExpiry} too high, using maximum {_options.MaxRegistrationExpiry}");
                return _options.MaxRegistrationExpiry;
            }

            return requestedExpiry;
        }

        private SIPResponse HandleUnregistration(SIPRequest request, string userUri)
        {
            if (_registrations.TryRemove(userUri, out var removedRegistration))
            {
                _logger.LogInformation($"Unregistered {userUri}");
            }
            else
            {
                _logger.LogDebug($"Attempted to unregister non-existent registration: {userUri}");
            }

            var response = SIPResponse.GetResponse(request, SIPResponseStatusCodesEnum.Ok, null);
            
            // Add Contact header with expires=0
            if (request.Header.Contact?.Count > 0)
            {
                var contact = request.Header.Contact[0];
                contact.Expires = 0;
                response.Header.Contact = new List<SIPContactHeader> { contact };
            }

            return response;
        }

        private SipRegistration CreateOrUpdateRegistration(SIPRequest request, string userUri, int expiry)
        {
            var contactUri = request.Header.Contact?.Count > 0 ? 
                request.Header.Contact[0].ContactURI.ToString() : userUri;

            var registration = new SipRegistration
            {
                UserUri = userUri,
                ContactUri = contactUri,
                RemoteEndPoint = request.RemoteSIPEndPoint?.GetIPEndPoint(),
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddSeconds(expiry),
                CallId = request.Header.CallId,
                CSeq = (uint)request.Header.CSeq,
                UserAgent = request.Header.UserAgent ?? "Unknown"
            };

            _registrations.AddOrUpdate(userUri, registration, (key, existing) => registration);

            return registration;
        }

        public IEnumerable<SipRegistration> GetActiveRegistrations()
        {
            return _registrations.Values.Where(r => r.IsActive);
        }

        public SipRegistration? GetRegistration(string userUri)
        {
            _registrations.TryGetValue(userUri, out var registration);
            return registration?.IsActive == true ? registration : null;
        }

        public async Task<int> CleanupExpiredRegistrationsAsync()
        {
            var expiredKeys = _registrations
                .Where(kvp => !kvp.Value.IsActive)
                .Select(kvp => kvp.Key)
                .ToList();

            var removedCount = 0;
            foreach (var key in expiredKeys)
            {
                if (_registrations.TryRemove(key, out var expired))
                {
                    removedCount++;
                    _logger.LogDebug($"Removed expired registration for {expired.UserUri}");
                }
            }

            if (removedCount > 0)
            {
                _logger.LogInformation($"Cleaned up {removedCount} expired registrations");
            }

            return removedCount;
        }

        public int ActiveRegistrationCount => _registrations.Values.Count(r => r.IsActive);

        public void Dispose()
        {
            _cleanupTimer?.Dispose();
        }
    }
}
