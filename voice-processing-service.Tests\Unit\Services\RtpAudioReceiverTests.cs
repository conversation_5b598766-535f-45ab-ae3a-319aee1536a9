using System;
using System.Net;
using System.Net.Sockets;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;
using voice_processing_service.Services;
using voice_processing_service.Interfaces;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class RtpAudioReceiverTests
    {
        [Fact]
        public void Constructor_InitializesEndpoints()
        {
            using var rtpClient = new UdpClient(0);
            using var rtcpClient = new UdpClient(0);
            var logger = NullLogger<RtpAudioReceiver>.Instance;
            var receiver = new RtpAudioReceiver("call1", rtpClient, rtcpClient, logger);

            Assert.NotNull(receiver.RtpLocalEndPoint);
            Assert.NotNull(receiver.RtcpLocalEndPoint);
            Assert.Equal(((IPEndPoint)rtpClient.Client.LocalEndPoint).Port, receiver.RtpLocalEndPoint.Port);
            Assert.Equal(((IPEndPoint)rtcpClient.Client.LocalEndPoint).Port, receiver.RtcpLocalEndPoint.Port);
        }

        [Fact]
        public async Task StartListeningAsync_RespectsCancellationToken()
        {
            using var rtpClient = new UdpClient(0);
            using var rtcpClient = new UdpClient(0);
            var logger = NullLogger<RtpAudioReceiver>.Instance;
            var receiver = new RtpAudioReceiver("call2", rtpClient, rtcpClient, logger);

            using var buffer = new BlockingCollectionAudioBuffer(NullLogger<BlockingCollectionAudioBuffer>.Instance);
            using var cts = new CancellationTokenSource(100);

            // Should complete without throwing
            await receiver.StartListeningAsync(buffer, cts.Token);
            Assert.True(buffer.IsAddingCompleted);
        }

        [Fact(Skip = "Private parser method not supported in current implementation")]
        public void ParseAndHandleRtp_ExtractsAudioData()
        {
            using var rtpClient = new UdpClient(0);
            using var rtcpClient = new UdpClient(0);
            var logger = NullLogger<RtpAudioReceiver>.Instance;
            var receiver = new RtpAudioReceiver("call3", rtpClient, rtcpClient, logger);

            using var buffer = new BlockingCollectionAudioBuffer(NullLogger<BlockingCollectionAudioBuffer>.Instance);

            // Build RTP header with payload type 0 and payload [1,2,3]
            byte[] header = new byte[12];
            header[0] = 0x80;
            header[1] = 0x00; // PT=0
            byte[] payload = new byte[] {1,2,3};
            byte[] packet = new byte[header.Length + payload.Length];
            Buffer.BlockCopy(header, 0, packet, 0, header.Length);
            Buffer.BlockCopy(payload, 0, packet, header.Length, payload.Length);

            // Invoke private ParseAndHandleRtp
            var method = typeof(RtpAudioReceiver)
                .GetMethod("ParseAndHandleRtp", BindingFlags.NonPublic | BindingFlags.Instance);
            method.Invoke(receiver, new object[] { packet, receiver.RtpLocalEndPoint, new Action<byte[]>(buffer.Add) });

            buffer.CompleteAdding();
            Assert.True(buffer.TryTake(out var result, 0, CancellationToken.None));
            Assert.Equal(payload, result);
        }

        [Fact(Skip = "Private parser method not supported in current implementation")]
        public void ParseAndHandleRtp_IgnoresUnsupportedPayload()
        {
            using var rtpClient = new UdpClient(0);
            using var rtcpClient = new UdpClient(0);
            var logger = NullLogger<RtpAudioReceiver>.Instance;
            var receiver = new RtpAudioReceiver("call4", rtpClient, rtcpClient, logger);

            using var buffer = new BlockingCollectionAudioBuffer(NullLogger<BlockingCollectionAudioBuffer>.Instance);

            // Build RTP header with payload type 96 unsupported
            byte[] header = new byte[12];
            header[0] = 0x80;
            header[1] = 96; // PT=96
            byte[] payload = new byte[] {1,2,3};
            byte[] packet = new byte[header.Length + payload.Length];
            Buffer.BlockCopy(header, 0, packet, 0, header.Length);
            Buffer.BlockCopy(payload, 0, packet, header.Length, payload.Length);

            var method = typeof(RtpAudioReceiver)
                .GetMethod("ParseAndHandleRtp", BindingFlags.NonPublic | BindingFlags.Instance);
            method.Invoke(receiver, new object[] { packet, receiver.RtpLocalEndPoint, new Action<byte[]>(buffer.Add) });

            buffer.CompleteAdding();
            Assert.False(buffer.TryTake(out _ , 0, CancellationToken.None));
        }

        [Fact]
        public void Dispose_ClosesUdpClients()
        {
            var rtpClient = new UdpClient(0);
            var rtcpClient = new UdpClient(0);
            var logger = NullLogger<RtpAudioReceiver>.Instance;
            var receiver = new RtpAudioReceiver("call5", rtpClient, rtcpClient, logger);

            ((IDisposable)receiver).Dispose();

            Assert.Throws<NullReferenceException>(() => rtpClient.Client.Send(new byte[1], SocketFlags.None));
            Assert.Throws<NullReferenceException>(() => rtcpClient.Client.Send(new byte[1], SocketFlags.None));
        }

        [Fact]
        public async Task StartListeningAsync_ThrowsAfterDispose()
        {
            using var rtpClient = new UdpClient(0);
            using var rtcpClient = new UdpClient(0);
            var logger = NullLogger<RtpAudioReceiver>.Instance;
            var receiver = new RtpAudioReceiver("callDispose", rtpClient, rtcpClient, logger);

            using var buffer = new BlockingCollectionAudioBuffer(NullLogger<BlockingCollectionAudioBuffer>.Instance);
            ((IDisposable)receiver).Dispose();

            await Assert.ThrowsAsync<ObjectDisposedException>(
                () => receiver.StartListeningAsync(buffer, CancellationToken.None));
        }
    }
}