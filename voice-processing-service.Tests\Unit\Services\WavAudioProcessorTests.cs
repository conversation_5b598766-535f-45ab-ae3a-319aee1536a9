using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;
using voice_processing_service.Services;
using voice_processing_service.Interfaces;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class WavAudioProcessorTests
    {
        [Fact]
        public void Constructor_ShouldInitializeProcessor()
        {
            var processor = new WavAudioProcessor("testcall", "testfile.wav", new NullLogger<WavAudioProcessor>());
            Assert.NotNull(processor);
            Assert.Equal("WAV_testfile", processor.ProcessorId);
        }

        [Fact]
        public async Task StartProcessingAsync_ShouldCreateFileWithHeader_WhenCancelledImmediately()
        {
            var tempFile = Path.Combine(Path.GetTempPath(), $"wav_{Guid.NewGuid()}.wav");
            var processor = new WavAudioProcessor("call1", tempFile, new NullLogger<WavAudioProcessor>());
            using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());
            var cts = new CancellationTokenSource();
            cts.Cancel();

            await processor.StartProcessingAsync(buffer, cts.Token);

            Assert.True(File.Exists(tempFile));

            var header = File.ReadAllBytes(tempFile);
            var riff = Encoding.ASCII.GetBytes("RIFF");
            Assert.Equal(riff, header.AsSpan(0, riff.Length).ToArray());

            File.Delete(tempFile);
        }

        [Fact]
        public async Task StartProcessingAsync_ShouldWriteAudioDataToFile()
        {
            var tempFile = Path.Combine(Path.GetTempPath(), $"wav_{Guid.NewGuid()}.wav");
            var processor = new WavAudioProcessor("call2", tempFile, new NullLogger<WavAudioProcessor>());
            using var buffer = new BlockingCollectionAudioBuffer(new NullLogger<BlockingCollectionAudioBuffer>());

            var sample = new byte[] { 1, 2, 3, 4, 5 };
            buffer.Add(sample);
            buffer.CompleteAdding();

            await processor.StartProcessingAsync(buffer, CancellationToken.None);

            var fileData = File.ReadAllBytes(tempFile);
            // Skip header (44 bytes)
            var payload = fileData[44..(44 + sample.Length)];
            Assert.Equal(sample, payload);

            File.Delete(tempFile);
        }

        [Fact]
        public void Dispose_ShouldAllowFileAccessAfterDispose()
        {
            var tempFile = Path.Combine(Path.GetTempPath(), $"wav_{Guid.NewGuid()}.wav");
            var processor = new WavAudioProcessor("call3", tempFile, new NullLogger<WavAudioProcessor>());
            processor.Dispose();

            // After dispose, file should be creatable/openable
            using var stream = File.Open(tempFile, FileMode.OpenOrCreate, FileAccess.ReadWrite);
            Assert.NotNull(stream);
            stream.Close();
            File.Delete(tempFile);
        }
    }
}