# Testovací plán pro ověření funkčnosti běhu serveru a simulátoru na jedné stanici

Tento dokument popisuje testovací plán pro ověření, že implementované změny umožňují běh serveru a simulátoru na jedné stanici.

## 1. Příprava testovacího prostředí

### 1.1 Požadavky

- Vývojové prostředí s .NET 9.0
- Nainstalované všechny potřebné z<PERSON>ti (SIPSorcery 8.0.11)
- Testovací WAV soubor pro simulátor
- Volné porty pro SIP a RTP komunikaci

### 1.2 Konfigurace testovacího prostředí

1. <PERSON><PERSON><PERSON><PERSON><PERSON> se, že máte aktuální verzi kódu z repozitáře
2. Implementujte změny popsané v souboru `implementation.md`
3. Zkompilujte oba projekty (server i simulátor)
4. Připravte testovací WAV soubor (pokud není k dispozici, můžete ho vygenerovat pomocí příkazu `voice-processing-simulator generate test.wav 10 440`)

## 2. Testovací scénáře

### 2.1 Test 1: Spuštění serveru s výchozími porty

**Cíl:** Ověřit, že server se správně spustí s výchozími porty.

**Kroky:**
1. Spusťte server bez parametrů příkazové řádky:
   ```bash
   dotnet run --project voice-processing-service/voice-processing-service.csproj
   ```
2. Ověřte v logu, že server naslouchá na správných portech (SIP: 5060, RTP: 30002-30010)

**Očekávaný výsledek:**
- Server se úspěšně spustí
- V logu je vidět, že server naslouchá na správných portech

### 2.2 Test 2: Spuštění serveru s vlastními porty

**Cíl:** Ověřit, že server se správně spustí s vlastními porty.

**Kroky:**
1. Spusťte server s parametry příkazové řádky:
   ```bash
   dotnet run --project voice-processing-service/voice-processing-service.csproj -- --sip-port 5061 --rtp-port-min 31000 --rtp-port-max 31010
   ```
2. Ověřte v logu, že server naslouchá na správných portech (SIP: 5061, RTP: 31000-31010)

**Očekávaný výsledek:**
- Server se úspěšně spustí
- V logu je vidět, že server naslouchá na správných portech

### 2.3 Test 3: Spuštění simulátoru s výchozími porty

**Cíl:** Ověřit, že simulátor se správně spustí s výchozími porty.

**Kroky:**
1. Spusťte simulátor s minimálními parametry:
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30
   ```
2. Ověřte v logu, že simulátor naslouchá na správných portech (SIP: 5070, RTP: 25000-25010)

**Očekávaný výsledek:**
- Simulátor se úspěšně spustí
- V logu je vidět, že simulátor naslouchá na správných portech

### 2.4 Test 4: Spuštění simulátoru s vlastními porty

**Cíl:** Ověřit, že simulátor se správně spustí s vlastními porty.

**Kroky:**
1. Spusťte simulátor s parametry pro porty:
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30 5071 26000 26010
   ```
2. Ověřte v logu, že simulátor naslouchá na správných portech (SIP: 5071, RTP: 26000-26010)

**Očekávaný výsledek:**
- Simulátor se úspěšně spustí
- V logu je vidět, že simulátor naslouchá na správných portech

### 2.5 Test 5: Spuštění serveru a simulátoru na jedné stanici s výchozími porty

**Cíl:** Ověřit, že server a simulátor mohou běžet na jedné stanici s výchozími porty.

**Kroky:**
1. Spusťte server s výchozími porty:
   ```bash
   dotnet run --project voice-processing-service/voice-processing-service.csproj
   ```
2. V jiném terminálu spusťte simulátor s výchozími porty:
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30
   ```
3. Sledujte logy obou komponent

**Očekávaný výsledek:**
- Server a simulátor se úspěšně spustí
- Simulátor se úspěšně připojí k serveru
- RTP stream je správně přenášen
- Hovor je správně ukončen po uplynutí času

### 2.6 Test 6: Spuštění serveru a simulátoru na jedné stanici s vlastními porty

**Cíl:** Ověřit, že server a simulátor mohou běžet na jedné stanici s vlastními porty.

**Kroky:**
1. Spusťte server s vlastními porty:
   ```bash
   dotnet run --project voice-processing-service/voice-processing-service.csproj -- --sip-port 5061 --rtp-port-min 31000 --rtp-port-max 31010
   ```
2. V jiném terminálu spusťte simulátor s vlastními porty:
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5061 30 5071 26000 26010
   ```
3. Sledujte logy obou komponent

**Očekávaný výsledek:**
- Server a simulátor se úspěšně spustí
- Simulátor se úspěšně připojí k serveru
- RTP stream je správně přenášen
- Hovor je správně ukončen po uplynutí času

### 2.7 Test 7: Spuštění více instancí serveru na jedné stanici

**Cíl:** Ověřit, že je možné spustit více instancí serveru na jedné stanici.

**Kroky:**
1. Spusťte první instanci serveru s vlastními porty:
   ```bash
   # Windows
   set SIPSERVER__LISTENPORT=5061
   set SIPSERVER__RTPPORTMIN=31000
   set SIPSERVER__RTPPORTMAX=31010
   dotnet run --project voice-processing-service/voice-processing-service.csproj

   # Linux/macOS
   export SIPSERVER__LISTENPORT=5061
   export SIPSERVER__RTPPORTMIN=31000
   export SIPSERVER__RTPPORTMAX=31010
   dotnet run --project voice-processing-service/voice-processing-service.csproj
   ```
2. V jiném terminálu spusťte druhou instanci serveru s jinými porty:
   ```bash
   # Windows
   set SIPSERVER__LISTENPORT=5062
   set SIPSERVER__RTPPORTMIN=32000
   set SIPSERVER__RTPPORTMAX=32010
   dotnet run --project voice-processing-service/voice-processing-service.csproj

   # Linux/macOS
   export SIPSERVER__LISTENPORT=5062
   export SIPSERVER__RTPPORTMIN=32000
   export SIPSERVER__RTPPORTMAX=32010
   dotnet run --project voice-processing-service/voice-processing-service.csproj
   ```
3. Ověřte, že obě instance serveru běží současně

**Očekávaný výsledek:**
- Obě instance serveru se úspěšně spustí
- Každá instance naslouchá na svých portech

### 2.8 Test 8: Spuštění více instancí simulátoru na jedné stanici

**Cíl:** Ověřit, že je možné spustit více instancí simulátoru na jedné stanici.

**Kroky:**
1. Spusťte první instanci simulátoru s vlastními porty:
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5061 30 5071 26000 26010
   ```
2. V jiném terminálu spusťte druhou instanci simulátoru s jinými porty:
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5062 30 5072 27000 27010
   ```
3. Ověřte, že obě instance simulátoru běží současně

**Očekávaný výsledek:**
- Obě instance simulátoru se úspěšně spustí
- Každá instance naslouchá na svých portech

## 3. Testování chybových stavů

### 3.1 Test 9: Konflikt portů

**Cíl:** Ověřit, že aplikace správně reaguje na konflikt portů.

**Kroky:**
1. Spusťte server s výchozími porty:
   ```bash
   dotnet run --project voice-processing-service/voice-processing-service.csproj
   ```
2. V jiném terminálu spusťte další instanci serveru se stejnými porty (bez změny proměnných prostředí):
   ```bash
   dotnet run --project voice-processing-service/voice-processing-service.csproj
   ```
3. Sledujte chybové hlášky

**Očekávaný výsledek:**
- První instance serveru se úspěšně spustí
- Druhá instance serveru selže s chybou o konfliktu portů

### 3.2 Test 10: Neplatné porty

**Cíl:** Ověřit, že aplikace správně reaguje na neplatné porty.

**Kroky:**
1. Spusťte server s neplatnými porty pomocí proměnných prostředí:
   ```bash
   # Windows
   set SIPSERVER__LISTENPORT=99999
   set SIPSERVER__RTPPORTMIN=100000
   set SIPSERVER__RTPPORTMAX=100010
   dotnet run --project voice-processing-service/voice-processing-service.csproj

   # Linux/macOS
   export SIPSERVER__LISTENPORT=99999
   export SIPSERVER__RTPPORTMIN=100000
   export SIPSERVER__RTPPORTMAX=100010
   dotnet run --project voice-processing-service/voice-processing-service.csproj
   ```
2. Sledujte chybové hlášky

**Očekávaný výsledek:**
- Server selže s chybou o neplatných portech

## 4. Testování výkonu

### 4.1 Test 11: Dlouhodobý test

**Cíl:** Ověřit, že server a simulátor mohou běžet na jedné stanici po delší dobu.

**Kroky:**
1. Spusťte server s vlastními porty:
   ```bash
   # Windows
   set SIPSERVER__LISTENPORT=5061
   set SIPSERVER__RTPPORTMIN=31000
   set SIPSERVER__RTPPORTMAX=31010
   dotnet run --project voice-processing-service/voice-processing-service.csproj

   # Linux/macOS
   export SIPSERVER__LISTENPORT=5061
   export SIPSERVER__RTPPORTMIN=31000
   export SIPSERVER__RTPPORTMAX=31010
   dotnet run --project voice-processing-service/voice-processing-service.csproj
   ```
2. V jiném terminálu spusťte simulátor s vlastními porty a delší dobou hovoru:
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5061 300 5071 26000 26010
   ```
3. Sledujte logy obou komponent po dobu 5 minut

**Očekávaný výsledek:**
- Server a simulátor běží stabilně po celou dobu testu
- RTP stream je správně přenášen
- Hovor je správně ukončen po uplynutí času

### 4.2 Test 12: Test s více současnými hovory

**Cíl:** Ověřit, že server může zpracovat více současných hovorů.

**Kroky:**
1. Spusťte server s vlastními porty:
   ```bash
   # Windows
   set SIPSERVER__LISTENPORT=5061
   set SIPSERVER__RTPPORTMIN=31000
   set SIPSERVER__RTPPORTMAX=31100
   dotnet run --project voice-processing-service/voice-processing-service.csproj

   # Linux/macOS
   export SIPSERVER__LISTENPORT=5061
   export SIPSERVER__RTPPORTMIN=31000
   export SIPSERVER__RTPPORTMAX=31100
   dotnet run --project voice-processing-service/voice-processing-service.csproj
   ```
2. V jiném terminálu spusťte první instanci simulátoru:
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5061 60 5071 26000 26010
   ```
3. V dalším terminálu spusťte druhou instanci simulátoru:
   ```bash
   dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5061 60 5072 27000 27010
   ```
4. Sledujte logy všech komponent

**Očekávaný výsledek:**
- Server a oba simulátory běží stabilně
- Oba hovory jsou úspěšně navázány
- RTP streamy jsou správně přenášeny
- Oba hovory jsou správně ukončeny po uplynutí času

## 5. Závěrečné ověření

Po úspěšném provedení všech testů by mělo být potvrzeno, že:

1. Server a simulátor mohou běžet na jedné stanici
2. Je možné konfigurovat porty pro obě komponenty
3. Implementace je kompatibilní s aktuální verzí SIPSorcery (8.0.11)
4. RTP porty v SDP odpovídají portům, na kterých komponenty skutečně naslouchají
5. Je možné spustit více instancí serveru a simulátoru na jedné stanici

Pokud některý z testů selže, je potřeba identifikovat příčinu a provést potřebné úpravy v implementaci.
