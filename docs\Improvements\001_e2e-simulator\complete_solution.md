# Komplexní ř<PERSON>í pro běh serveru a simulátoru na jedné stanici

Tento dokument obsahuje ucelený návrh úprav aplikace, který umožní běh serveru (voice-processing-service) a simulátoru (voice-processing-simulator) na jedné stanici.

## 1. Identifikované problémy

### 1.1 Konflikty portů

Server a simulátor nemohou běžet na stejné stanici, protože:

1. Simulátor má pevně nastavený SIP port (5070) a rozsah RTP portů (25000-25010)
2. Server má konfiguraci portů v appsettings.json, ale nemá možnost snadného přepsání této konfigurace
3. Implementace není plně kompatibilní s poslední verzí SIPSorcery (8.0.11)

### 1.2 Problém s metodou CreateReceiverWithSpecificPorts

Metoda `CreateReceiverWithSpecificPorts` ve třídě `RtpAudioReceiverFactory` obsahuje pevně nastavené porty (40002 a 40003) pro vytvoření UDP klientů, což je v rozporu s parametry metody:

```csharp
public IAudioInputReceiver CreateReceiverWithSpecificPorts(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
{
    try
    {
        var aaa = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40002));
        var bbb = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40003));

        // ...

        // Vytvořit UDP klienty
        var rtpClient = aaa;
        var rtcpClient = bbb;

        // ...
    }
    // ...
}
```

Tato implementace ignoruje parametry `rtpPort` a `rtcpPort` a místo toho používá pevně nastavené porty 40002 a 40003. To způsobuje, že RTP komunikace nefunguje správně, protože server naslouchá na jiných portech, než které jsou uvedeny v SDP.

### 1.3 Nekonzistence v nastavení IP adresy

V metodě `CreateReceiverWithSpecificPorts` je použita IP adresa `IPAddress.Any` místo parametru `localAddress`:

```csharp
var aaa = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40002));
var bbb = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40003));
```

Toto může způsobit problémy, pokud je server nakonfigurován tak, aby naslouchal pouze na konkrétní IP adrese.

### 1.4 Potenciální problém s RTP porty v SDP

V metodě `ProcessInviteAsync` třídy `SipServerService` je vytvořen `VoIPMediaSession` s rozsahem portů z konfigurace:

```csharp
var _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(_options.RtpPortMin, _options.RtpPortMax),
});
```

Ale později v kódu je vytvořen `RtpAudioReceiver` s konkrétními porty:

```csharp
Func<IAudioInputReceiver> inputReceiverFactory = () => _audioInputReceiverWithPortsFactory(callId, rtpPort, rtcpPort);
```

Pokud porty v `RtpAudioReceiver` neodpovídají portům, které jsou použity v SDP (vytvořeném z `VoIPMediaSession`), RTP komunikace nebude fungovat správně.

## 2. Navrhované řešení

### 2.1 Úpravy v simulátoru (voice-processing-simulator)

#### 2.1.1 Přidání parametrů příkazové řádky pro konfiguraci SIP a RTP portů

Rozšíříme stávající příkaz "simulate" o další parametry:

```csharp
case "simulate":
    if (args.Length < 4)
    {
        Console.WriteLine("Usage: voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds] [local_sip_port] [rtp_port_min] [rtp_port_max]");
        Console.WriteLine("Example: voice-processing-simulator simulate test.wav 127.0.0.1 5060 30 5070 25000 25010");
        return;
    }
    string wavFile = args[1];
    string serverIp = args[2];
    int serverPort = int.Parse(args[3]);
    int callDurationSeconds = args.Length > 4 ? int.Parse(args[4]) : 30; // Výchozí hodnota 30 sekund
    int localSipPort = args.Length > 5 ? int.Parse(args[5]) : 5070; // Výchozí hodnota 5070
    int rtpPortMin = args.Length > 6 ? int.Parse(args[6]) : 25000; // Výchozí hodnota 25000
    int rtpPortMax = args.Length > 7 ? int.Parse(args[7]) : 25010; // Výchozí hodnota 25010
    await SimulateCallAsync(wavFile, serverIp, serverPort, callDurationSeconds, localSipPort, rtpPortMin, rtpPortMax);
    break;
```

#### 2.1.2 Úprava metody SimulateCallAsync

Upravíme metodu SimulateCallAsync, aby používala konfigurované porty:

```csharp
private static async Task SimulateCallAsync(string wavFile, string serverIp, int serverPort, int callDurationSeconds, int localSipPort = 5070, int rtpPortMin = 25000, int rtpPortMax = 25010)
{
    // Existující kód...
    
    // Přidání UDP kanálu pro SIP komunikaci s konfigurovatelným portem
    var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, localSipPort);
    sipTransport.AddSIPChannel(sipChannel);
    _logger.LogInformation($"SIP UDP channel created on {sipChannel.ListeningEndPoint}");
    
    // Vytvoření VoIP media session s konfigurovatelným rozsahem portů
    _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
    {
        MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
        RtpPortRange = new PortRange(rtpPortMin, rtpPortMax),
    });
    
    // Zbytek existujícího kódu...
}
```

#### 2.1.3 Aktualizace nápovědy

Aktualizujeme metodu ShowUsage, aby obsahovala informace o nových parametrech:

```csharp
private static void ShowUsage()
{
    Console.WriteLine("Usage:");
    Console.WriteLine("  voice-processing-simulator generate <output_file> <duration_seconds> <tone_frequency>");
    Console.WriteLine("  voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds] [local_sip_port] [rtp_port_min] [rtp_port_max]");
    Console.WriteLine("Examples:");
    Console.WriteLine("  voice-processing-simulator generate test.wav 10 440");
    Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5060 30");
    Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5060 30 5070 25000 25010");
}
```

### 2.2 Úpravy v serveru (voice-processing-service)

#### 2.2.1 Konfigurace pomocí appsettings.json a proměnných prostředí

Server používá konfiguraci z appsettings.json, kterou lze přepsat pomocí proměnných prostředí. ASP.NET Core a Kestrel nativně podporují tuto funkcionalitu, takže není potřeba přidávat vlastní kód pro zpracování parametrů příkazové řádky.

Konfigurace v appsettings.json:

```json
"SipServer": {
  "ListenIpAddress": "Any",
  "ListenPort": 5060,
  "RtpPortMin": 30002,
  "RtpPortMax": 30010,
  "WavRecordingDirectory": "RecordedCalls"
}
```

Přepsání konfigurace pomocí proměnných prostředí:

```bash
# Windows
set SIPSERVER__LISTENPORT=5061
set SIPSERVER__RTPPORTMIN=31000
set SIPSERVER__RTPPORTMAX=31010

# Linux/macOS
export SIPSERVER__LISTENPORT=5061
export SIPSERVER__RTPPORTMIN=31000
export SIPSERVER__RTPPORTMAX=31010
```

ASP.NET Core automaticky nahradí dvojtečky v názvech sekcí konfigurace dvěma podtržítky v názvech proměnných prostředí.

#### 2.2.2 Úprava metody CreateReceiverWithSpecificPorts

Metodu `CreateReceiverWithSpecificPorts` ve třídě `RtpAudioReceiverFactory` je potřeba upravit tak, aby správně používala parametry `rtpPort` a `rtcpPort` pro vytvoření UDP klientů:

```csharp
public IAudioInputReceiver CreateReceiverWithSpecificPorts(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
{
    try
    {
        _logger.LogInformation($"[{callId}] Attempting to create RTP receiver with specific ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit UDP klienty s požadovanými porty
        var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
        var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));

        // Nastavení většího bufferu pro UDP klienty
        rtpClient.Client.ReceiveBufferSize = 1048576; // 1MB buffer
        rtpClient.Client.SendBufferSize = 1048576;
        rtcpClient.Client.ReceiveBufferSize = 1048576;
        rtcpClient.Client.SendBufferSize = 1048576;

        _logger.LogInformation($"[{callId}] Successfully created RTP receiver with ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit a vrátit RtpAudioReceiver
        return new RtpAudioReceiver(callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());
    }
    catch (SocketException ex)
    {
        // Port je již obsazen nebo nemáme dostatečná oprávnění
        _logger.LogWarning($"[{callId}] Could not bind to RTP port {rtpPort} and RTCP port {rtcpPort}: {ex.Message} (Error code: {ex.SocketErrorCode})");

        // Zkusíme ještě jednou uvolnit porty s jiným přístupem
        _logger.LogInformation($"[{callId}] Attempting to force release ports with different approach");

        try
        {
            // Pokus o vytvoření socketů s nastavením ReuseAddress
            using (var socket1 = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp))
            {
                socket1.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                socket1.Bind(new IPEndPoint(localAddress, rtpPort));
                socket1.Close();
                _logger.LogInformation($"[{callId}] Successfully force-released RTP port {rtpPort}");
            }

            using (var socket2 = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp))
            {
                socket2.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                socket2.Bind(new IPEndPoint(localAddress, rtcpPort));
                socket2.Close();
                _logger.LogInformation($"[{callId}] Successfully force-released RTCP port {rtcpPort}");
            }

            // Zkusíme znovu vytvořit UDP klienty
            var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
            var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));

            // Nastavení většího bufferu pro UDP klienty
            rtpClient.Client.ReceiveBufferSize = 1048576; // 1MB buffer
            rtpClient.Client.SendBufferSize = 1048576;
            rtcpClient.Client.ReceiveBufferSize = 1048576;
            rtcpClient.Client.SendBufferSize = 1048576;

            _logger.LogInformation($"[{callId}] Successfully created RTP receiver after force-releasing ports: RTP={rtpPort}, RTCP={rtcpPort}");

            // Vytvořit a vrátit RtpAudioReceiver
            return new RtpAudioReceiver(callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());
        }
        catch (Exception innerEx)
        {
            _logger.LogWarning($"[{callId}] Force-release of ports failed: {innerEx.Message}");
            throw new InvalidOperationException($"Could not create RTP receiver with ports: RTP={rtpPort}, RTCP={rtcpPort}", innerEx);
        }
    }
}
```

#### 2.2.3 Zajištění konzistence RTP portů v SDP a RtpAudioReceiver

Je potřeba zajistit, že porty použité v `RtpAudioReceiver` odpovídají portům, které jsou použity v SDP. Doporučujeme následující postup:

1. Vytvořit `VoIPMediaSession` s rozsahem portů z konfigurace
2. Vytvořit SDP odpověď pomocí `CreateAnswer`
3. Extrahovat RTP port z SDP odpovědi
4. Vytvořit `RtpAudioReceiver` s tímto portem

```csharp
// Vytvoření VoIPMediaSession s rozsahem portů z konfigurace
var _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(_options.RtpPortMin, _options.RtpPortMax),
});

// Vytvoření SDP odpovědi
var answerSdp = _rtpSession.CreateAnswer(offerSdp);

// Extrakce RTP portu z SDP odpovědi
int rtpPort = answerSdp.Media.First(m => m.Media == SDPMediaTypesEnum.audio).Port;
int rtcpPort = rtpPort + 1;

// Vytvoření RtpAudioReceiver s tímto portem
Func<IAudioInputReceiver> inputReceiverFactory = () => _audioInputReceiverWithPortsFactory(callId, rtpPort, rtcpPort);
```

#### 2.2.4 Úprava Properties/launchSettings.json

Přidáme profil s vlastními porty pomocí proměnných prostředí:

```json
{
  "profiles": {
    "voice_processing_service": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7001;http://localhost:5001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "voice_processing_service_custom_ports": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7001;http://localhost:5001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "SIPSERVER__LISTENPORT": "5061",
        "SIPSERVER__RTPPORTMIN": "31000",
        "SIPSERVER__RTPPORTMAX": "31010"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
```

### 2.3 Další doporučení

#### 2.3.1 Logování RTP portů

Doporučujeme přidat podrobnější logování RTP portů, aby bylo možné lépe diagnostikovat problémy s RTP komunikací:

```csharp
_logger.LogInformation($"[{callId}] SDP RTP port: {rtpPort}, RTCP port: {rtcpPort}");
_logger.LogInformation($"[{callId}] RtpAudioReceiver RTP port: {rtpAudioReceiver.RtpLocalEndPoint.Port}, RTCP port: {rtpAudioReceiver.RtcpLocalEndPoint.Port}");
```

#### 2.3.2 Ošetření výjimek

Doporučujeme přidat lepší ošetření výjimek, zejména při vytváření UDP klientů a zpracování RTP paketů:

```csharp
try
{
    // Vytvoření UDP klientů
    var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
    var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));
    
    // ...
}
catch (SocketException ex)
{
    _logger.LogError(ex, $"[{callId}] Error creating UDP clients: {ex.Message} (Error code: {ex.SocketErrorCode})");
    throw;
}
catch (Exception ex)
{
    _logger.LogError(ex, $"[{callId}] Unexpected error creating UDP clients: {ex.Message}");
    throw;
}
```

## 3. Příklady použití

### 3.1 Spuštění serveru s vlastními porty

```bash
# Windows - nastavení proměnných prostředí před spuštěním
set SIPSERVER__LISTENPORT=5061
set SIPSERVER__RTPPORTMIN=31000
set SIPSERVER__RTPPORTMAX=31010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Linux/macOS - nastavení proměnných prostředí před spuštěním
export SIPSERVER__LISTENPORT=5061
export SIPSERVER__RTPPORTMIN=31000
export SIPSERVER__RTPPORTMAX=31010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Alternativně lze použít příkaz s proměnnými prostředí v jednom řádku
# Windows
set SIPSERVER__LISTENPORT=5061 && set SIPSERVER__RTPPORTMIN=31000 && set SIPSERVER__RTPPORTMAX=31010 && dotnet run --project voice-processing-service/voice-processing-service.csproj

# Linux/macOS
SIPSERVER__LISTENPORT=5061 SIPSERVER__RTPPORTMIN=31000 SIPSERVER__RTPPORTMAX=31010 dotnet run --project voice-processing-service/voice-processing-service.csproj
```

### 3.2 Spuštění simulátoru s vlastními porty

```bash
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30 5070 25000 25010
```

### 3.3 Spuštění serveru a simulátoru na jedné stanici

Pro spuštění serveru a simulátoru na jedné stanici je potřeba zajistit, aby používaly různé porty. Například:

1. Server:
   - SIP port: 5060 (standardní)
   - RTP porty: 30000-30010

2. Simulátor:
   - SIP port: 5070
   - RTP porty: 25000-25010

```bash
# Terminál 1 - Server (Windows)
set SIPSERVER__LISTENPORT=5060
set SIPSERVER__RTPPORTMIN=30000
set SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 1 - Server (Linux/macOS)
export SIPSERVER__LISTENPORT=5060
export SIPSERVER__RTPPORTMIN=30000
export SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 2 - Simulátor
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30 5070 25000 25010
```

## 4. Kompatibilita s SIPSorcery 8.0.11

Implementace serveru (voice-processing-service) a simulátoru (voice-processing-simulator) je kompatibilní s SIPSorcery 8.0.11. Hlavní části kódu, které pracují s SIP a RTP, jsou implementovány správně a měly by fungovat s touto verzí knihovny.

Doporučujeme implementovat konfiguraci portů v simulátoru pomocí parametrů příkazové řádky, jak je navrženo v tomto dokumentu, aby bylo možné spustit více instancí simulátoru na jedné stanici.

Při aktualizaci na novější verze SIPSorcery v budoucnu bude potřeba zkontrolovat, zda nedošlo k změnám v API, které by mohly ovlivnit funkčnost serveru a simulátoru.

## 5. Testování

Po implementaci těchto úprav je potřeba otestovat, zda server a simulátor mohou běžet na jedné stanici a správně komunikovat. Doporučený postup:

1. Spusťte server s vlastními porty
2. Spusťte simulátor s vlastními porty
3. Ověřte, že simulátor se úspěšně připojí k serveru
4. Ověřte, že RTP stream je správně přenášen
5. Ověřte, že hovor je správně ukončen

Pokud některý z kroků selže, zkontrolujte:

1. Logy serveru a simulátoru pro identifikaci problému
2. Zda jsou porty správně nakonfigurovány a nejsou v konfliktu
3. Zda jsou porty povoleny ve firewallu
4. Zda jsou obě komponenty kompatibilní s použitou verzí SIPSorcery

## 6. Závěr

Implementace navržených úprav umožní běh serveru a simulátoru na jedné stanici bez konfliktů portů. Hlavní změny zahrnují:

1. Přidání parametrů příkazové řádky pro konfiguraci portů v simulátoru
2. Využití proměnných prostředí pro konfiguraci portů v serveru
3. Úpravu metody `CreateReceiverWithSpecificPorts` pro správné použití parametrů `rtpPort` a `rtcpPort`
4. Zajištění konzistence RTP portů v SDP a RtpAudioReceiver

Tyto změny jsou kompatibilní s SIPSorcery 8.0.11 a měly by umožnit bezproblémový běh serveru a simulátoru na jedné stanici.
