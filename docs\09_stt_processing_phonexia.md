# Implementace online transkripce pomocí Phonexia API

## Popis úkolu

Tento úkol zahrnuje nahrazení WAV procesoru, který ukládá příchozí stream do WAV souboru, za online transkripci pomocí Phonexia REST API. Phonexia API umožňuje zpracovávat RTP stream přímo, což bude využito pro získání transkripce v reálném čase. Výsledky transkripce budou vypisovány na konzoli pomocí `console.log`.

## Technické detaily

### Implementace PhonexiaSttProcessor

Třída `PhonexiaSttProcessor` bude implementovat rozhraní `IAudioProcessor` a bude zodpovědná za:

1. Vytvoření RTP streamu na Phonexia API
2. Přeposílání příchozích RTP dat na Phonexia API
3. Příjem výsledků transkripce přes WebSocket
4. Vý<PERSON> výsledků transkripce na konzoli

### Změny v konfiguraci

Konfigurace `PhonexiaOptions` bude rozšířena o:

1. URL pro Phonexia API (včetně portu)
2. Přihlašovací údaje (uživatelské jméno a heslo)
3. Konfiguraci pro RTP stream (model, interval aktualizací)

### Modely pro Phonexia API

Budou vytvořeny nové modely pro komunikaci s Phonexia API:

1. `PhonexiaStreamResponse` - odpověď při vytvoření streamu
2. `PhonexiaSttTaskResponse` - odpověď při vytvoření STT úlohy
3. `PhonexiaSttResult` - model pro výsledky transkripce

### Implementace WebSocket klienta

Pro příjem výsledků transkripce bude implementován WebSocket klient, který bude:

1. Připojovat se k Phonexia API WebSocket endpointu
2. Zpracovávat příchozí zprávy s výsledky transkripce
3. Extrahovat text z výsledků a vypisovat ho na konzoli

## Implementační kroky

### 1. Rozšíření konfigurace

1. Aktualizace třídy `PhonexiaOptions` o nové konfigurační položky
2. Aktualizace `appsettings.json` o nové konfigurační hodnoty

### 2. Vytvoření modelů pro Phonexia API

1. Vytvoření modelů pro komunikaci s Phonexia API
2. Implementace deserializace JSON odpovědí

### 3. Implementace PhonexiaSttProcessor

1. Vytvoření třídy `PhonexiaSttProcessor` implementující `IAudioProcessor`
2. Implementace metody `StartProcessingAsync` pro zpracování audio dat z bufferu
3. Implementace metod pro komunikaci s Phonexia API:
   - `LoginAsync` - přihlášení k Phonexia API
   - `CreateRtpStreamAsync` - vytvoření RTP streamu
   - `BindSttToStreamAsync` - připojení STT technologie ke streamu
   - `ConnectToResultsWebSocketAsync` - připojení k WebSocket pro příjem výsledků
   - `ProcessWebSocketMessagesAsync` - zpracování zpráv z WebSocket
   - `StopStreamAsync` - ukončení streamu

### 4. Implementace továrny pro PhonexiaSttProcessor

1. Vytvoření třídy `PhonexiaSttProcessorFactory` pro vytváření instancí `PhonexiaSttProcessor`
2. Registrace továrny v DI kontejneru

### 5. Aktualizace CallSessionManager

1. Aktualizace metody `CreateCallSession` pro použití `PhonexiaSttProcessor` místo `WavAudioProcessor`

## Testovací scénáře

### Unit testy pro PhonexiaSttProcessor

1. **Test konstruktoru**
   - Ověřit, že instance PhonexiaSttProcessor je vytvořena bez chyb
   - Ověřit, že všechny závislosti jsou správně nastaveny

2. **Test metody StartProcessingAsync**
   - Ověřit, že metoda StartProcessingAsync vytvoří RTP stream
   - Ověřit, že metoda StartProcessingAsync připojí STT technologii ke streamu
   - Ověřit, že metoda StartProcessingAsync připojí WebSocket pro příjem výsledků
   - Ověřit, že metoda StartProcessingAsync zpracuje audio data z bufferu

3. **Test zpracování WebSocket zpráv**
   - Ověřit, že zprávy z WebSocket jsou správně deserializovány
   - Ověřit, že text z výsledků je správně extrahován a vypsán na konzoli

### Integrační testy

1. **Test end-to-end**
   - Vytvořit instanci PhonexiaSttProcessor a AudioBuffer
   - Přidat audio data do bufferu
   - Spustit zpracování a ověřit, že výsledky jsou vypsány na konzoli

## Implementační detaily

### Přihlášení k Phonexia API

```csharp
private async Task<string> LoginAsync(CancellationToken cancellationToken)
{
    var authHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_options.Username}:{_options.Password}"));
    var loginRequest = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/login");
    loginRequest.Headers.Authorization = new AuthenticationHeaderValue("Basic", authHeader);

    var loginResponse = await _httpClient.SendAsync(loginRequest, cancellationToken);
    loginResponse.EnsureSuccessStatusCode();
    var loginJson = await loginResponse.Content.ReadAsStringAsync(cancellationToken);
    var loginData = JsonSerializer.Deserialize<PhonexiaLoginResponse>(loginJson);
    
    return loginData.Result.Session.Id;
}
```

### Vytvoření RTP streamu

```csharp
private async Task<(string streamId, int port)> CreateRtpStreamAsync(CancellationToken cancellationToken)
{
    var streamRequest = new HttpRequestMessage(HttpMethod.Post, $"{_options.ApiUrl}/input_stream/rtp?frequency=8000&n_channels=1");
    streamRequest.Headers.Add("X-SessionID", _sessionId);

    var streamResponse = await _httpClient.SendAsync(streamRequest, cancellationToken);
    streamResponse.EnsureSuccessStatusCode();
    var streamJson = await streamResponse.Content.ReadAsStringAsync(cancellationToken);
    var streamData = JsonSerializer.Deserialize<PhonexiaStreamResponse>(streamJson);
    
    return (streamData.Result.InputStream, streamData.Result.Port);
}
```

### Připojení STT technologie ke streamu

```csharp
private async Task<string> BindSttToStreamAsync(string streamId, CancellationToken cancellationToken)
{
    var sttRequest = new HttpRequestMessage(HttpMethod.Post, 
        $"{_options.ApiUrl}/technologies/stt/input_stream?input_stream={streamId}&model={_options.Model}");
    sttRequest.Headers.Add("X-SessionID", _sessionId);
    
    var sttResponse = await _httpClient.SendAsync(sttRequest, cancellationToken);
    sttResponse.EnsureSuccessStatusCode();
    var sttJson = await sttResponse.Content.ReadAsStringAsync(cancellationToken);
    var sttData = JsonSerializer.Deserialize<PhonexiaSttTaskResponse>(sttJson);
    
    return sttData.Result.StreamTaskInfo.Id;
}
```

### Připojení k WebSocket pro příjem výsledků

```csharp
private async Task ConnectToResultsWebSocketAsync(string taskId, CancellationToken cancellationToken)
{
    _websocket = new ClientWebSocket();
    var uri = new Uri($"ws://{_options.ApiUrl.Replace("http://", "")}/technologies/stt/input_stream?task={taskId}&interval=0.33&trigger_events=transcription");
    
    _websocket.Options.SetRequestHeader("X-SessionID", _sessionId);
    _websocket.Options.SetRequestHeader("Accept", "application/json");
    
    await _websocket.ConnectAsync(uri, cancellationToken);
    
    // Spuštění asynchronního zpracování zpráv
    _ = ProcessWebSocketMessagesAsync(cancellationToken);
}
```

### Zpracování zpráv z WebSocket

```csharp
private async Task ProcessWebSocketMessagesAsync(CancellationToken cancellationToken)
{
    var buffer = new byte[8192];
    
    try
    {
        while (_websocket.State == WebSocketState.Open && !cancellationToken.IsCancellationRequested)
        {
            var result = await _websocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);
            
            if (result.MessageType == WebSocketMessageType.Close)
            {
                await _websocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", cancellationToken);
                break;
            }
            
            if (result.MessageType == WebSocketMessageType.Text)
            {
                var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                ProcessTranscriptionResult(message);
            }
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"[{_callId}] Error processing WebSocket messages.");
    }
}
```

### Zpracování výsledků transkripce

```csharp
private void ProcessTranscriptionResult(string message)
{
    try
    {
        var sttData = JsonSerializer.Deserialize<PhonexiaSttWebSocketResponse>(message);
        
        if (sttData.Result.OneBestResult != null)
        {
            foreach (var segment in sttData.Result.OneBestResult.Segmentation)
            {
                if (!string.IsNullOrEmpty(segment.Word) && !segment.Word.StartsWith("<"))
                {
                    Console.WriteLine($"Transcribed Text: {segment.Word}");
                }
            }
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"[{_callId}] Error processing transcription result.");
    }
}
```

## Závěr

Implementace online transkripce pomocí Phonexia API nahradí ukládání příchozího streamu do WAV souboru. Výsledky transkripce budou vypisovány na konzoli v reálném čase. Toto řešení umožní získávat textový přepis hovoru během jeho průběhu, což může být využito pro další analýzu nebo zpracování.
