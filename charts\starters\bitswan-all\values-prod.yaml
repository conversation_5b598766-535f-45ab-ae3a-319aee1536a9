# Declare variables to be passed into your templates.

# BitSwan Worker configuration
# https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/BITSWAN_HELM.md
sites:
  <domain>-<project>:
    <instance>:
      commit: <short-SHA-seven-digits>
      configuration: site.conf
      debug: false
      environment: production
      enabled: true
      imageVersion: 1.0.0

# -------------------------------------
# Configuration Defaults
# -------------------------------------

default:
  autoscaling:
    enabled: false
    cpuBound: false
    memoryBound: false
    minReplicas: 1
    maxReplicas: 4
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  hostAliases:
    enabled: false
    records:
      *******: dns.google
  livenessProbe:
    initialDelaySeconds: 240
    periodSeconds: 10
  pvc:
    enabled: false
    accessModes:
      - ReadWriteOncePod
    resources:
      requests:
        storage: 300Mi
    storageClassName: csi-cephfs-sc
  replicas: 1
  resources:
    limits:
      cpu: 1100m
      memory: 2Gi
    requests:
      cpu: 100m
      memory: 1Gi
  sla: 0
  startupProbe:
    failureThreshold: 30
    periodSeconds: 10
  terminationGracePeriodSeconds: 180

# -------------------------------------
# General Deployment Configurations
# -------------------------------------

alerting:
  enabled: false
  info:
  warning:
    CPU:
    Memory:
    VolumeCapacity:
  critical:
    FrequentRestarts:
      count: 5
      period: 10m
    CPU:
    Memory:
    VolumeCapacity:
cronjob: false

# Docker Registry
image:
  registry: network.git.cz.o2:5005
  pullPolicy: IfNotPresent

# Do not use or with caution please
nameOverride: ""
fullnameOverride: ""

# Service Account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Service
service:
  port: 8080
  name: http

# Ingress
ingress:
  enabled: false
  # NOTE: This is your K8s Cluster's FQDN Base Domain
  baseDomain: okd-jzm.network.cz.o2
  className: "nginx"
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - paths:
        - path: /doc
          pathType: Prefix
        - path: /
          pathType: Prefix
  tls:
    enabled: true

affinity: {}
nodeSelector: {}
podAnnotations: {}
podSecurityContext: {}
securityContext: {}
tolerations: []
