namespace voice_processing_service.Configuration
{
    /// <summary>
    /// Konfigurace pro Phonexia API.
    /// </summary>
    public class PhonexiaOptions
    {
        /// <summary>
        /// URL Phonexia API.
        /// </summary>
        public string ApiUrl { get; set; } = "http://localhost:8600";

        /// <summary>
        /// Uživatelské jméno pro Phonexia API.
        /// </summary>
        public string Username { get; set; } = "admin";

        /// <summary>
        /// Heslo pro Phonexia API.
        /// </summary>
        public string Password { get; set; } = "phonexia";

        /// <summary>
        /// API klíč pro Phonexia API.
        /// </summary>
        public string ApiKey { get; set; }

        /// <summary>
        /// Jazyk pro STT.
        /// </summary>
        public string Language { get; set; } = "cs-CZ";

        /// <summary>
        /// Model pro STT.
        /// </summary>
        public string Model { get; set; } = "CS_CZ_O2_6";

        /// <summary>
        /// Velikost chunku audio dat v bytech.
        /// </summary>
        public int ChunkSizeBytes { get; set; } = 8000; // 1 sekunda při 8kHz

        /// <summary>
        /// Počet sdílených Phonexia sessions v poolu pro concurrent zpracování.
        /// </summary>
        public int SessionPoolSize { get; set; } = 2;
    }
}
