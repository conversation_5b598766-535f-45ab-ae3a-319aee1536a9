using System;
using System.IO;
using System.Threading.Tasks;
using NAudio.Wave;
using NAudio.Wave.SampleProviders; // for MediaFoundationResampler
using Microsoft.Extensions.Logging;
using SIPSorcery.Media;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Sys;
using SIPSorceryMedia.Abstractions;

namespace voice_processing_simulator
{
    /// <summary>
    /// <PERSON>les sending RTP audio from an in-memory WAV stream.
    /// </summary>
    internal static class InMemoryRtpStreamer
    {
        /// <summary>
        /// Reads PCM data from a WAV stream, resamples if necessary, and sends it over RTP.
        /// </summary>
        /// <param name="wavStream">The WAV data stream positioned at 0.</param>
        /// <param name="rtpSession">The RTP media session to send audio through.</param>
        /// <param name="logger">Logger for diagnostic output.</param>
        public static async Task StartSendingRtpMemoryAsync(MemoryStream wavStream, VoIPMediaSession rtpSession, ILogger logger)
        {
            logger.LogInformation("Starting to send RTP stream from in-memory microphone data.");

            // Define target PCM format
            const int targetSampleRateHz = 8000;
            const int targetChannels = 1;
            const int targetBitsPerSample = 16;
            var targetFormat = new WaveFormat(targetSampleRateHz, targetBitsPerSample, targetChannels);
            var targetRtpSampleRate = AudioSamplingRatesEnum.Rate8KHz;

            try
            {
                wavStream.Position = 0;
                using var reader = new WaveFileReader(wavStream);
                logger.LogInformation($"Source WAV stream: {reader.WaveFormat.Encoding}, {reader.WaveFormat.SampleRate}Hz, {reader.WaveFormat.Channels}ch, {reader.WaveFormat.BitsPerSample}bit");

                using var pcmStream = new MemoryStream();

                // Conversion / resampling logic
                if (reader.WaveFormat.Encoding != WaveFormatEncoding.Pcm ||
                    reader.WaveFormat.BitsPerSample != targetFormat.BitsPerSample ||
                    reader.WaveFormat.Channels != targetFormat.Channels)
                {
                    // Convert bit depth/channels first
                    var intermediateFormat = new WaveFormat(reader.WaveFormat.SampleRate, targetFormat.BitsPerSample, targetFormat.Channels);
                    using (var conversionStream = new WaveFormatConversionStream(intermediateFormat, reader))
                    {
                        if (conversionStream.WaveFormat.SampleRate != targetFormat.SampleRate)
                        {
                            using var resampler = new MediaFoundationResampler(conversionStream, targetFormat) { ResamplerQuality = 60 };
                            WaveFileWriter.WriteWavFileToStream(pcmStream, resampler);
                        }
                        else
                        {
                            WaveFileWriter.WriteWavFileToStream(pcmStream, conversionStream);
                        }
                    }
                }
                else if (reader.WaveFormat.SampleRate != targetFormat.SampleRate)
                {
                    using var resampler = new MediaFoundationResampler(reader, targetFormat) { ResamplerQuality = 60 };
                    WaveFileWriter.WriteWavFileToStream(pcmStream, resampler);
                }
                else
                {
                    WaveFileWriter.WriteWavFileToStream(pcmStream, reader);
                }

                pcmStream.Position = 0;
                double durationSec = (double)pcmStream.Length / targetFormat.AverageBytesPerSecond;
                logger.LogInformation($"Prepared PCM data: {pcmStream.Length} bytes (~{durationSec:F2}s). Sending via RTP at {targetSampleRateHz}Hz.");

                // Send over RTP
                await rtpSession.AudioExtrasSource.SendAudioFromStream(pcmStream, targetRtpSampleRate);

                logger.LogInformation("In-memory RTP stream sending completed.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error sending in-memory RTP stream.");
            }
        }
    }
}