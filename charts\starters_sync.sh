#!/usr/bin/env bash
# Copyright © O2 Czech Republic a.s. - All Rights Reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.
# Terms and Conditions of usage are defined in file 'LICENSE.txt', which is part of this source code package.

set -e

function loggie() {
    LOG_SEVERITY=${1}
    LOG_LINE="${2}"
    TIMESTAMP=$(date +'%Y-%m-%d %H:%M:%S')

    if [[ "${LOG_LEVEL}" == "DEBUG" ]]; then
        # Print everything
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    elif [[ "${LOG_LEVEL}" == "ERROR" && "${LOG_SEVERITY}" == "ERROR" ]]; then
        # Print only ERROR messages
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    elif [[ "${LOG_LEVEL}" == "WARNING" && ( "${LOG_SEVERITY}" != "INFO" && "${LOG_SEVERITY}" != "DEBUG" ) ]]; then
        # Print only WARNING and ERROR messages
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    elif [[ "${LOG_LEVEL}" == "INFO" ]]; then
        # Print INFO+ messages
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    fi
}

LOG_LEVEL="INFO"
SOURCE_GIT="******************.o2:ntwcl/gitopsntw.git"
SOURCE_PATH="templates/helm/starters/"

loggie "INFO" "Relocating previous Helm Starters into '.previous' folder"
mkdir .previous
mv starters/*/ .previous
loggie "INFO" "Done relocating"

loggie "INFO" "Fetching new Helm Starters from '${SOURCE_GIT}': '${SOURCE_PATH}'"
echo "==============================="
git init .update
cd .update/
git remote add -f origin "${SOURCE_GIT}"
git config core.sparseCheckout true
echo "${SOURCE_PATH}" >> .git/info/sparse-checkout
git pull origin main
echo "==============================="
loggie "INFO" "Done fetching"

loggie "INFO" "Finishing the update"
cd ..
rm -rf .update/.git
mv .update/${SOURCE_PATH}/*/ starters
rm -rf .update .previous
loggie "INFO" "Done updating Helm Starters"

