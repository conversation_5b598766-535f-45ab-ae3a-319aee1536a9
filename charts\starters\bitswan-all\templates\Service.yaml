{{- range $bitswan, $values := .Values.sites }}
{{- range $instance, $data := $values }}
{{- $bitsafe := cat $bitswan "-" $instance | replace "_" "" | nospace -}}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ $.Release.Name }}-{{ $bitsafe }}
  labels:
    {{- include "initial.labels" $ | nindent 4 }}
spec:
  clusterIP: None
  selector:
    {{- include "initial.selectorLabels" $ | nindent 4 }}
  ports:
    - port: {{ $.Values.service.port }}
      targetPort: {{ $.Values.service.name }}
      protocol: TCP
      name: {{ $.Values.service.name }}
{{- end }}
{{- end }}
