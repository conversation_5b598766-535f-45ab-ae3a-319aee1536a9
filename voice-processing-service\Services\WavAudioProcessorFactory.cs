using System;
using System.IO;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Factory for creating WavAudioProcessor instances.
    /// </summary>
    public class WavAudioProcessorFactory
    {
        private readonly ILoggerFactory _loggerFactory;
        private readonly WavRecordingOptions _options;

        public WavAudioProcessorFactory(ILoggerFactory loggerFactory, IOptions<WavRecordingOptions> options)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }

        /// <summary>
        /// Creates a new instance of WavAudioProcessor.
        /// </summary>
        /// <param name="callId">Call identifier for logging.</param>
        /// <returns>IAudioProcessor instance.</returns>
        public IAudioProcessor CreateProcessor(string callId)
        {
            if (string.IsNullOrWhiteSpace(callId)) throw new ArgumentNullException(nameof(callId));

            var baseDir = _options.Directory;
            var utcStart = DateTime.UtcNow;
            var template = _options.FilenameTemplate;

            // Step 2: construct processor with templating so it can rotate segments and re-render names.
            if (!string.IsNullOrWhiteSpace(baseDir) && !string.IsNullOrWhiteSpace(template))
            {
                return new WavAudioProcessor(
                    callId,
                    baseDir!,
                    template!,
                    utcStart,
                    _options,
                    _loggerFactory.CreateLogger<WavAudioProcessor>());
            }

            // Fallback to single path (legacy behavior)
            string wavPath;
            var fileName = $"call_{callId}_{DateTime.Now:yyyyMMddHHmmss}.wav";
            var fallbackDir = string.IsNullOrWhiteSpace(baseDir) ? "RecordedCalls" : baseDir!;
            wavPath = Path.Combine(fallbackDir, fileName);

            return new WavAudioProcessor(callId, wavPath, _loggerFactory.CreateLogger<WavAudioProcessor>());
        }

        private static string RenderTemplate(string template, string callId, DateTime utcStart, int segmentIndex)
        {
            var rendered = template;

            // {utcStart[:format]}
            rendered = Regex.Replace(rendered, @"\{utcStart(?::([^}]+))?\}", m =>
            {
                var fmt = m.Groups.Count > 1 ? m.Groups[1].Value : null;
                return string.IsNullOrEmpty(fmt) ? utcStart.ToString("yyyyMMdd'T'HHmmss'Z'") : utcStart.ToString(fmt);
            });

            // {date}
            rendered = rendered.Replace("{date}", utcStart.ToString("yyyyMMdd"));

            // {callId}
            rendered = rendered.Replace("{callId}", callId);

            // {segmentIndex[:format]}
            rendered = Regex.Replace(rendered, @"\{segmentIndex(?::([^}]+))?\}", m =>
            {
                var fmt = m.Groups.Count > 1 ? m.Groups[1].Value : null;
                return string.IsNullOrEmpty(fmt) ? segmentIndex.ToString() : segmentIndex.ToString(fmt);
            });

            return rendered;
        }
    }
}