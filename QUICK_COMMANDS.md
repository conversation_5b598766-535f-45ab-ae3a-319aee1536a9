# Quick Command Reference

## Build & Deploy Commands

### **Windows PowerShell (Development)**
```powershell
# Build image (monitor progress yourself)
docker build -t voice-processing-service:0.1 ./voice-processing-service

# Save for transfer
docker save voice-processing-service:0.1 -o voice-processing-service-0.1.tar

# Test locally with logs
mkdir -p data/logs, data/recordings
docker run -d --name voice-processing-service -p 5090:5090/udp -p 10000-10100:10000-10100/udp -p 8081:8081 -v ${PWD}/data/logs:/app/logs -v ${PWD}/data/recordings:/app/recordings -e PHONEXIA_API_URL=http://localhost:8600 voice-processing-service:0.1

# Monitor logs
docker logs -f voice-processing-service

# Health check
curl http://localhost:8081/health

# Cleanup
docker stop voice-processing-service
docker rm voice-processing-service
```

### **RHEL9 Podman (Production)**
```bash
# Load image
podman load -i voice-processing-service-0.1.tar

# Create directories
mkdir -p /data/logs /data/recordings

# Run with logs and Phonexia URL
podman run -d --name voice-processing-service -p 5090:5090/udp -p 10000-10100:10000-10100/udp -p 8081:8081 -v /data/logs:/app/logs -v /data/recordings:/app/recordings -e PHONEXIA_API_URL=http://your-phonexia-server:8600 voice-processing-service:0.1

# Monitor logs (should show startup and SIP activity)
podman logs -f voice-processing-service

# Check log files
tail -f /data/logs/voice-processing-service-*.log

# Health check
curl http://localhost:8081/health

# Management
podman ps
podman stop voice-processing-service
podman rm voice-processing-service
```

## Expected Log Output

You should now see logs like:
```
[10:30:15 INF] === Voice Processing Service Starting ===
[10:30:15 INF] Environment: Production
[10:30:15 INF] SIP Listen Port: 5090
[10:30:15 INF] HTTP API Port: http://+:8081
[10:30:15 INF] Phonexia API URL: http://your-phonexia-server:8600
[10:30:15 INF] Log Path: /app/logs
[10:30:15 INF] === Voice Processing Service Started Successfully ===
[10:30:16 INF] SIP server started on 0.0.0.0:5090
[10:30:20 INF] Processing REGISTER request for sip:<EMAIL>
```

## Key Changes Made

1. **✅ Removed complex build scripts** - You build manually with `docker build`
2. **✅ Enhanced console logging** - Added startup messages and better formatting
3. **✅ Added log path mapping** - `-v /data/logs:/app/logs` in all commands
4. **✅ Added Phonexia URL** - `-e PHONEXIA_API_URL=http://your-server:8600` in all commands
5. **✅ Real-time log monitoring** - Use `docker logs -f` or `podman logs -f`

## Ports Summary
- **5090/udp** - SIP signaling
- **8081/tcp** - HTTP API & health checks  
- **10000-10100/udp** - RTP media range
