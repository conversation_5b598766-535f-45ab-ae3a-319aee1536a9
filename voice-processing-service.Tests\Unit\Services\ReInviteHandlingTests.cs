using System;
using System.Collections.Generic;
using System.Net;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Moq;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Net;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    /// <summary>
    /// Comprehensive unit tests for re-INVITE handling scenarios
    /// Tests detection logic, session updates, synchronization, resource cleanup, and error handling
    /// </summary>
    public class ReInviteHandlingTests
    {
        #region Mock Classes

        private class MockAudioReceiver : IAudioInputReceiver
        {
            public bool UpdateConfigurationCalled { get; private set; }
            public IPEndPoint LastRemoteEndpoint { get; private set; }
            public string[] LastSupportedCodecs { get; private set; }
            public int UpdateConfigurationCallCount { get; private set; }

            public IPEndPoint RtpLocalEndPoint { get; } = new IPEndPoint(IPAddress.Loopback, 10002);
            public IPEndPoint RtcpLocalEndPoint { get; } = new IPEndPoint(IPAddress.Loopback, 10003);

            public Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
            {
                return Task.CompletedTask;
            }

            public Task UpdateConfigurationAsync(IPEndPoint newRemoteEndpoint, string[] supportedCodecs)
            {
                UpdateConfigurationCalled = true;
                LastRemoteEndpoint = newRemoteEndpoint;
                LastSupportedCodecs = supportedCodecs;
                UpdateConfigurationCallCount++;
                return Task.CompletedTask;
            }

            public void Dispose() { }
        }

        private class MockAudioBuffer : IAudioBuffer
        {
            public void Add(byte[] data) { }
            public void Add(byte[] data, CancellationToken cancellationToken) { }
            public bool TryTake(out byte[] data, int timeout, CancellationToken cancellationToken) 
            { 
                data = null; 
                return false; 
            }
            public byte[] Take(CancellationToken cancellationToken) { return null; }
            public void CompleteAdding() { }
            public bool IsAddingCompleted => false;
            public bool IsCompleted => false;
            public void Dispose() { }
        }

        private class MockAudioProcessor : IAudioProcessor
        {
            public string ProcessorId => "MOCK_PROCESSOR";
            public Task InitializeAsync(CancellationToken cancellationToken) { return Task.CompletedTask; }
            public Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken) 
            { 
                return Task.CompletedTask; 
            }
            public void Dispose() { }
        }

        private class MockCallSessionManager : ICallSessionManager
        {
            private readonly Dictionary<string, ICallSession> _sessions = new Dictionary<string, ICallSession>();
            
            public bool UpdateSessionAsyncCalled { get; private set; }
            public string LastUpdateCallId { get; private set; }
            public SIPRequest LastUpdateRequest { get; private set; }
            public SDP LastUpdateSdp { get; private set; }

            public Task<ICallSession> CreateSessionAsync(SIPServerUserAgent userAgent, SIPRequest inviteRequest, Func<IAudioInputReceiver> inputReceiverFactory, Func<IAudioProcessor> audioProcessorFactory)
            {
                var session = new CallSession(userAgent, inviteRequest, inputReceiverFactory(), new MockAudioBuffer(), audioProcessorFactory(), () => Task.CompletedTask, NullLogger<CallSession>.Instance);
                _sessions[inviteRequest.Header.CallId] = session;
                return Task.FromResult<ICallSession>(session);
            }

            public ICallSession GetSession(string callId)
            {
                _sessions.TryGetValue(callId, out var session);
                return session;
            }

            public Task TerminateSessionAsync(string callId)
            {
                _sessions.Remove(callId);
                return Task.CompletedTask;
            }

            public Task UpdateSessionAsync(string callId, SIPRequest reInviteRequest, SDP newSdpOffer)
            {
                UpdateSessionAsyncCalled = true;
                LastUpdateCallId = callId;
                LastUpdateRequest = reInviteRequest;
                LastUpdateSdp = newSdpOffer;
                
                var session = GetSession(callId);
                if (session == null)
                    throw new InvalidOperationException($"Session not found for call {callId}");
                
                return session.UpdateSessionAsync(reInviteRequest, newSdpOffer);
            }

            public IEnumerable<ICallSession> GetAllSessions()
            {
                return _sessions.Values;
            }
        }

        private class MockSynchronizationService : ICallSynchronizationService
        {
            private readonly Dictionary<string, int> _lockCounts = new Dictionary<string, int>();
            
            public int ActiveLockCount => _lockCounts.Values.Sum();
            
            public int GetLockCount(string callId) => _lockCounts.TryGetValue(callId, out var count) ? count : 0;

            public async Task<T> ExecuteWithLockAsync<T>(string callId, Func<Task<T>> function, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
            {
                _lockCounts[callId] = _lockCounts.TryGetValue(callId, out var count) ? count + 1 : 1;
                return await function();
            }

            public async Task ExecuteWithLockAsync(string callId, Func<Task> action, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
            {
                _lockCounts[callId] = _lockCounts.TryGetValue(callId, out var count) ? count + 1 : 1;
                await action();
            }

            public Task CleanupLocksAsync(string callId)
            {
                _lockCounts.Remove(callId);
                return Task.CompletedTask;
            }
        }

        #endregion

        #region Test Data Creation Helpers

        private SIPRequest CreateMockInviteRequest(string callId, string fromTag = null, string toTag = null)
        {
            var invite = (SIPRequest)FormatterServices.GetUninitializedObject(typeof(SIPRequest));
            var header = (SIPHeader)FormatterServices.GetUninitializedObject(typeof(SIPHeader));
            
            header.CallId = callId;
            header.From = (SIPFromHeader)FormatterServices.GetUninitializedObject(typeof(SIPFromHeader));
            header.To = (SIPToHeader)FormatterServices.GetUninitializedObject(typeof(SIPToHeader));
            
            if (!string.IsNullOrEmpty(fromTag))
                header.From.FromTag = fromTag;
            if (!string.IsNullOrEmpty(toTag))
                header.To.ToTag = toTag;
            
            invite.Header = header;
            invite.Method = SIPMethodsEnum.INVITE;
            
            return invite;
        }

        private SDP CreateMockSdpOffer(string connectionAddress = "*************", int port = 5004)
        {
            string sdpString = $@"v=0
o=- 3848946217 3848946217 IN IP4 {connectionAddress}
s=-
c=IN IP4 {connectionAddress}
t=0 0
m=audio {port} RTP/AVP 0
a=rtpmap:0 PCMU/8000";

            return SDP.ParseSDPDescription(sdpString);
        }

        #endregion

        #region 1. Detection Logic Tests

        [Fact]
        public async Task UpdateSessionAsync_CallsUpdateConfigurationOnAudioReceiver()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var invite = CreateMockInviteRequest("test-call-id");
            
            var session = new CallSession(
                userAgent,
                invite,
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            var reInviteRequest = CreateMockInviteRequest("test-call-id");
            var sdpOffer = CreateMockSdpOffer();

            // Act
            await session.UpdateSessionAsync(reInviteRequest, sdpOffer);

            // Assert
            Assert.True(mockReceiver.UpdateConfigurationCalled);
            Assert.NotNull(mockReceiver.LastRemoteEndpoint);
            Assert.Equal("*************", mockReceiver.LastRemoteEndpoint.Address.ToString());
            Assert.Equal(5004, mockReceiver.LastRemoteEndpoint.Port);
            Assert.Contains("PCMU", mockReceiver.LastSupportedCodecs);
        }

        [Fact]
        public void CurrentRtpPort_ReturnsCorrectPortFromReceiver()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var invite = CreateMockInviteRequest("test-call-id");
            
            var session = new CallSession(
                userAgent,
                invite,
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            // Act & Assert
            Assert.Equal(10002, session.CurrentRtpPort);
            Assert.Equal(10003, session.CurrentRtcpPort);
        }

        [Theory]
        [InlineData("test-call-1", null, null, false)] // Initial INVITE - no To tag
        [InlineData("test-call-2", "from123", null, false)] // Initial INVITE - no To tag
        [InlineData("test-call-3", "from123", "to456", true)] // re-INVITE - both tags present
        [InlineData("test-call-4", null, "to456", true)] // re-INVITE - To tag present (edge case)
        public void IsReInvite_DetectsCorrectly_BasedOnToTag(string callId, string fromTag, string toTag, bool expectedIsReInvite)
        {
            // Arrange
            var request = CreateMockInviteRequest(callId, fromTag, toTag);
            var mockSessionManager = new MockCallSessionManager();
            
            // For re-INVITE cases, create an existing session
            if (expectedIsReInvite)
            {
                var existingInvite = CreateMockInviteRequest(callId, fromTag, null);
                var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
                mockSessionManager.CreateSessionAsync(userAgent, existingInvite, () => new MockAudioReceiver(), () => new MockAudioProcessor()).Wait();
            }

            // Act - We'll test this through SipServerService behavior since IsReInvite is private
            // The detection logic is based on To-tag presence and session existence
            var hasToTag = !string.IsNullOrWhiteSpace(toTag);
            var hasExistingSession = mockSessionManager.GetSession(callId) != null;

            // Assert
            Assert.Equal(expectedIsReInvite, hasToTag);
            if (expectedIsReInvite)
            {
                Assert.True(hasExistingSession);
            }
        }

        #endregion

        #region 2. Session Update Behavior Tests

        [Fact]
        public async Task UpdateSessionAsync_UpdatesExistingSessionParameters()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");
            var newSdpOffer = CreateMockSdpOffer("*************", 6000);

            // Act
            await session.UpdateSessionAsync(reInviteRequest, newSdpOffer);

            // Assert
            Assert.True(mockReceiver.UpdateConfigurationCalled);
            Assert.Equal("*************", mockReceiver.LastRemoteEndpoint.Address.ToString());
            Assert.Equal(6000, mockReceiver.LastRemoteEndpoint.Port);
            Assert.Equal(1, mockReceiver.UpdateConfigurationCallCount);
        }

        [Fact]
        public async Task UpdateSessionAsync_DoesNotReplaceRtpReceiver_OnlyUpdatesConfiguration()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var originalRtpPort = mockReceiver.RtpLocalEndPoint.Port;
            var originalRtcpPort = mockReceiver.RtcpLocalEndPoint.Port;
            
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");
            var newSdpOffer = CreateMockSdpOffer("*************", 6000);

            // Act
            await session.UpdateSessionAsync(reInviteRequest, newSdpOffer);

            // Assert - RTP receiver ports should remain the same (not replaced)
            Assert.Equal(originalRtpPort, session.CurrentRtpPort);
            Assert.Equal(originalRtcpPort, session.CurrentRtcpPort);
            Assert.True(mockReceiver.UpdateConfigurationCalled);
        }

        [Fact]
        public async Task UpdateSessionAsync_HandlesMultipleCodecs_CorrectlyFiltersSupported()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            // Create SDP with multiple codecs
            string sdpString = @"v=0
o=- 3848946217 3848946217 IN IP4 *************
s=-
c=IN IP4 *************
t=0 0
m=audio 5004 RTP/AVP 0 8 18
a=rtpmap:0 PCMU/8000
a=rtpmap:8 PCMA/8000
a=rtpmap:18 G729/8000";

            var sdpOffer = SDP.ParseSDPDescription(sdpString);
            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");

            // Act
            await session.UpdateSessionAsync(reInviteRequest, sdpOffer);

            // Assert
            Assert.True(mockReceiver.UpdateConfigurationCalled);
            Assert.Contains("PCMU", mockReceiver.LastSupportedCodecs);
            Assert.Contains("PCMA", mockReceiver.LastSupportedCodecs);
            Assert.Contains("G722", mockReceiver.LastSupportedCodecs); // Default supported codecs
        }

        #endregion

        #region 3. Synchronization Tests

        [Fact]
        public async Task CallSessionManager_UpdateSessionAsync_UsesSynchronization()
        {
            // Arrange
            var mockSessionManager = new MockCallSessionManager();
            var mockSyncService = new MockSynchronizationService();
            
            var sessionManager = new CallSessionManager(
                NullLogger<CallSessionManager>.Instance,
                NullLoggerFactory.Instance,
                null,
                mockSyncService);

            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var initialInvite = CreateMockInviteRequest("test-call-id");
            
            // Create initial session
            await sessionManager.CreateSessionAsync(
                userAgent, 
                initialInvite, 
                () => new MockAudioReceiver(), 
                () => new MockAudioProcessor());

            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");
            var newSdpOffer = CreateMockSdpOffer();

            // Act
            await sessionManager.UpdateSessionAsync("test-call-id", reInviteRequest, newSdpOffer);

            // Assert
            // Note: In actual implementation, synchronization would be tested through SipServerService
            // which calls ExecuteWithLockAsync before calling UpdateSessionAsync
            Assert.NotNull(sessionManager.GetSession("test-call-id"));
        }

        [Fact]
        public async Task ConcurrentReInvites_ShouldBeSerialized()
        {
            // Arrange
            var mockSyncService = new MockSynchronizationService();
            var callId = "test-call-id";
            var executionOrder = new List<int>();
            var completionTasks = new List<Task>();

            // Act - Simulate concurrent re-INVITEs
            for (int i = 0; i < 3; i++)
            {
                var taskId = i;
                var task = mockSyncService.ExecuteWithLockAsync(callId, async () =>
                {
                    executionOrder.Add(taskId);
                    await Task.Delay(10); // Simulate processing time
                });
                completionTasks.Add(task);
            }

            await Task.WhenAll(completionTasks);

            // Assert
            Assert.Equal(3, executionOrder.Count);
            Assert.Equal(3, mockSyncService.GetLockCount(callId));
        }

        #endregion

        #region 4. Resource Cleanup Tests

        [Fact]
        public async Task SessionTermination_CleansUpResourcesCorrectly()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var mockBuffer = new MockAudioBuffer();
            var mockProcessor = new MockAudioProcessor();
            var cleanupCalled = false;
            
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                mockBuffer,
                mockProcessor,
                () => { cleanupCalled = true; return Task.CompletedTask; },
                NullLogger<CallSession>.Instance);

            // Act
            await session.StopAsync();

            // Assert
            Assert.True(cleanupCalled);
        }

        [Fact]
        public void SessionDispose_CleansUpAllResources()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var mockBuffer = new MockAudioBuffer();
            var mockProcessor = new MockAudioProcessor();
            
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                mockBuffer,
                mockProcessor,
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            // Act
            session.Dispose();

            // Assert - No exceptions should be thrown
            // In a real scenario, we would verify that Dispose was called on all components
            Assert.True(true); // Placeholder - actual test would verify disposal
        }

        [Fact]
        public async Task CallSessionManager_TerminateSession_RemovesFromTracking()
        {
            // Arrange
            var sessionManager = new CallSessionManager(
                NullLogger<CallSessionManager>.Instance,
                NullLoggerFactory.Instance);

            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var invite = CreateMockInviteRequest("test-call-id");
            
            await sessionManager.CreateSessionAsync(
                userAgent, 
                invite, 
                () => new MockAudioReceiver(), 
                () => new MockAudioProcessor());

            // Verify session exists
            Assert.NotNull(sessionManager.GetSession("test-call-id"));

            // Act
            await sessionManager.TerminateSessionAsync("test-call-id");

            // Assert
            Assert.Null(sessionManager.GetSession("test-call-id"));
        }

        #endregion

        #region 5. Error Handling Tests

        [Fact]
        public void CallSessionManager_UpdateSessionAsync_ThrowsForNonExistentSession()
        {
            // Arrange
            var sessionManager = new CallSessionManager(
                NullLogger<CallSessionManager>.Instance,
                NullLoggerFactory.Instance);

            var reInviteRequest = CreateMockInviteRequest("nonexistent-call-id", "from123", "to456");
            var newSdpOffer = CreateMockSdpOffer();

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(
                () => sessionManager.UpdateSessionAsync("nonexistent-call-id", reInviteRequest, newSdpOffer));
            
            Assert.Contains("Session not found for call nonexistent-call-id", exception.Result.Message);
        }

        [Fact]
        public async Task UpdateSessionAsync_HandlesInvalidSdpGracefully()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");
            
            // Create SDP with no audio media
            string invalidSdpString = @"v=0
o=- 3848946217 3848946217 IN IP4 *************
s=-
c=IN IP4 *************
t=0 0";

            var invalidSdpOffer = SDP.ParseSDPDescription(invalidSdpString);

            // Act - Should not throw, but should handle gracefully
            await session.UpdateSessionAsync(reInviteRequest, invalidSdpOffer);

            // Assert - UpdateConfiguration should not be called with invalid SDP
            Assert.False(mockReceiver.UpdateConfigurationCalled);
        }

        [Fact]
        public async Task UpdateSessionAsync_HandlesInvalidRemoteAddress()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");
            
            // Create SDP with invalid IP address
            string sdpString = @"v=0
o=- 3848946217 3848946217 IN IP4 invalid.ip.address
s=-
c=IN IP4 invalid.ip.address
t=0 0
m=audio 5004 RTP/AVP 0
a=rtpmap:0 PCMU/8000";

            var sdpOffer = SDP.ParseSDPDescription(sdpString);

            // Act - Should handle invalid IP gracefully
            await session.UpdateSessionAsync(reInviteRequest, sdpOffer);

            // Assert - Should not crash, but may not update configuration
            // The actual behavior depends on implementation - it should log warning and continue
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("   ")]
        public async Task CallSessionManager_UpdateSessionAsync_HandlesInvalidCallId(string invalidCallId)
        {
            // Arrange
            var sessionManager = new CallSessionManager(
                NullLogger<CallSessionManager>.Instance,
                NullLoggerFactory.Instance);

            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");
            var newSdpOffer = CreateMockSdpOffer();

            // Act
            await sessionManager.UpdateSessionAsync(invalidCallId, reInviteRequest, newSdpOffer);

            // Assert - Should handle gracefully (method logs warning and returns)
            // No exception should be thrown for invalid call IDs
        }

        #endregion

        #region 6. Integration-Style Tests

        [Fact]
        public async Task CompleteReInviteFlow_UpdatesSessionCorrectly()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var mockSessionManager = new MockCallSessionManager();
            
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var initialInvite = CreateMockInviteRequest("test-call-id");
            
            // Create initial session
            await mockSessionManager.CreateSessionAsync(
                userAgent, 
                initialInvite, 
                () => mockReceiver, 
                () => new MockAudioProcessor());

            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");
            var newSdpOffer = CreateMockSdpOffer("*************", 6000);

            // Act
            await mockSessionManager.UpdateSessionAsync("test-call-id", reInviteRequest, newSdpOffer);

            // Assert
            Assert.True(mockSessionManager.UpdateSessionAsyncCalled);
            Assert.Equal("test-call-id", mockSessionManager.LastUpdateCallId);
            Assert.Equal(reInviteRequest, mockSessionManager.LastUpdateRequest);
            Assert.Equal(newSdpOffer, mockSessionManager.LastUpdateSdp);
            Assert.True(mockReceiver.UpdateConfigurationCalled);
        }

        [Fact]
        public async Task MultipleReInvites_SameSession_UpdatesCorrectly()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            // Act - Multiple re-INVITEs
            await session.UpdateSessionAsync(
                CreateMockInviteRequest("test-call-id", "from123", "to456"), 
                CreateMockSdpOffer("*************", 6000));

            await session.UpdateSessionAsync(
                CreateMockInviteRequest("test-call-id", "from123", "to456"), 
                CreateMockSdpOffer("*************", 6002));

            await session.UpdateSessionAsync(
                CreateMockInviteRequest("test-call-id", "from123", "to456"), 
                CreateMockSdpOffer("*************", 6004));

            // Assert
            Assert.Equal(3, mockReceiver.UpdateConfigurationCallCount);
            Assert.Equal("*************", mockReceiver.LastRemoteEndpoint.Address.ToString());
            Assert.Equal(6004, mockReceiver.LastRemoteEndpoint.Port);
        }

        #endregion

        #region 7. Edge Cases and Boundary Tests

        [Fact]
        public async Task UpdateSessionAsync_WithZeroPort_HandlesGracefully()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");
            var sdpOfferWithZeroPort = CreateMockSdpOffer("*************", 0);

            // Act
            await session.UpdateSessionAsync(reInviteRequest, sdpOfferWithZeroPort);

            // Assert - Should not update configuration with zero port
            Assert.False(mockReceiver.UpdateConfigurationCalled);
        }

        [Fact]
        public async Task UpdateSessionAsync_WithEmptyConnectionAddress_HandlesGracefully()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var session = new CallSession(
                (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent)),
                CreateMockInviteRequest("test-call-id"),
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            var reInviteRequest = CreateMockInviteRequest("test-call-id", "from123", "to456");
            
            // Create SDP with empty connection address
            string sdpString = @"v=0
o=- 3848946217 3848946217 IN IP4 
s=-
c=IN IP4 
t=0 0
m=audio 5004 RTP/AVP 0
a=rtpmap:0 PCMU/8000";

            var sdpOffer = SDP.ParseSDPDescription(sdpString);

            // Act
            await session.UpdateSessionAsync(reInviteRequest, sdpOffer);

            // Assert - Should handle gracefully without crashing
            // Implementation-specific behavior
        }

        [Fact]
        public void CallSessionProperties_ReturnCorrectValues()
        {
            // Arrange
            var mockReceiver = new MockAudioReceiver();
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var invite = CreateMockInviteRequest("test-call-id");
            
            var session = new CallSession(
                userAgent,
                invite,
                mockReceiver,
                new MockAudioBuffer(),
                new MockAudioProcessor(),
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            // Act & Assert
            Assert.Equal("test-call-id", session.CallId);
            Assert.Equal(userAgent, session.UserAgent);
            Assert.Equal(invite, session.InitialInviteRequest);
            Assert.Equal(mockReceiver, session.AudioReceiver);
            Assert.Equal(10002, session.CurrentRtpPort);
            Assert.Equal(10003, session.CurrentRtcpPort);
        }

        #endregion
    }
}