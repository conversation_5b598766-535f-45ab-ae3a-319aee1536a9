using System;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using SIPSorcery.Net;
using SIPSorcery.SIP;
using voice_processing_service.Configuration;
using voice_processing_service.Services;
using voice_processing_service.Interfaces;
using Xunit;
using System.Reflection;
using System.Collections.Concurrent;
using System.Collections.Generic;
using voice_processing_service.Models;

namespace voice_processing_service.Tests.Integration.Services
{
    public class SipServerServiceIntegrationTests
    {
        private SipServerService CreateService(CallSessionManager sessionManager, SipServerOptions options = null)
        {
            var logger = new NullLogger<SipServerService>();
            var opts = options ?? new SipServerOptions
            {
                ListenPort = 0,
                WavRecordingDirectory = "test_wav",
                RtpPortMin = 40000,
                RtpPortMax = 40010
            };
            var optionsWrapper = Options.Create(opts);
            var loggerFactory = new NullLoggerFactory();
            var portAllocator = new StubPortAllocator();
            var audioInputReceiverFactory = new StubRtpAudioReceiverFactory();
            Func<string, IAudioProcessor> simpleAudioProcessorFactory = callId => new StubAudioProcessor();
            Func<string, string, string, IAudioProcessor> enhancedAudioProcessorFactory = (callId, caller, called) => new StubAudioProcessor();
            var registrationManager = new StubSipRegistrationManager();
            var synchronizationService = new StubSynchronizationService();
            return new SipServerService(logger, sessionManager, optionsWrapper, loggerFactory, portAllocator, audioInputReceiverFactory, simpleAudioProcessorFactory, enhancedAudioProcessorFactory, registrationManager, synchronizationService);
        }

        private SIPRequest CreateInvite(string callId, string sdpBody)
        {
            string invite =
                $"INVITE sip:service@localhost SIP/2.0\r\n" +
                $"Via: SIP/2.0/UDP 127.0.0.1:5060;branch=z9hG4bK\r\n" +
                $"From: <sip:tester@localhost>;tag=1\r\n" +
                $"To: <sip:service@localhost>\r\n" +
                $"Call-ID: {callId}\r\n" +
                $"CSeq: 1 INVITE\r\n" +
                $"Content-Length: {sdpBody.Length}\r\n\r\n" +
                sdpBody;
            return SIPRequest.ParseSIPRequest(invite);
        }

        private SIPRequest CreateBye(string callId)
        {
            string bye =
                $"BYE sip:service@localhost SIP/2.0\r\n" +
                $"Via: SIP/2.0/UDP 127.0.0.1:5060;branch=z9hG4bK\r\n" +
                $"From: <sip:tester@localhost>;tag=1\r\n" +
                $"To: <sip:service@localhost>\r\n" +
                $"Call-ID: {callId}\r\n" +
                $"CSeq: 2 BYE\r\n" +
                "Content-Length: 0\r\n\r\n";
            return SIPRequest.ParseSIPRequest(bye);
        }

        private SIPRequest CreateCancel(string callId)
        {
            string cancel =
                $"CANCEL sip:service@localhost SIP/2.0\r\n" +
                $"Via: SIP/2.0/UDP 127.0.0.1:5060;branch=z9hG4bK\r\n" +
                $"From: <sip:tester@localhost>;tag=1\r\n" +
                $"To: <sip:service@localhost>\r\n" +
                $"Call-ID: {callId}\r\n" +
                $"CSeq: 1 CANCEL\r\n" +
                "Content-Length: 0\r\n\r\n";
            return SIPRequest.ParseSIPRequest(cancel);
        }

        private async Task SendRequest(SipServerService service, SIPRequest request)
        {
            var serviceType = typeof(SipServerService);
            Task invokeTask;
            if (request.Method == SIPMethodsEnum.INVITE)
            {
                var method = serviceType.GetMethod("ProcessInviteAsync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var local = new SIPEndPoint(SIPProtocolsEnum.udp, new IPEndPoint(IPAddress.Loopback, 5060));
                var remote = new SIPEndPoint(SIPProtocolsEnum.udp, new IPEndPoint(IPAddress.Loopback, 5060));
                invokeTask = (Task)method.Invoke(service, new object[] { local, remote, request });
            }
            else if (request.Method == SIPMethodsEnum.BYE)
            {
                var method = serviceType.GetMethod("ProcessByeAsync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                invokeTask = (Task)method.Invoke(service, new object[] { request });
            }
            else if (request.Method == SIPMethodsEnum.CANCEL)
            {
                var method = serviceType.GetMethod("ProcessCancelAsync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                invokeTask = (Task)method.Invoke(service, new object[] { request });
            }
            else
            {
                var method = serviceType.GetMethod("OnSipRequestReceived", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var local = new SIPEndPoint(SIPProtocolsEnum.udp, new IPEndPoint(IPAddress.Loopback, 5060));
                var remote = new SIPEndPoint(SIPProtocolsEnum.udp, new IPEndPoint(IPAddress.Loopback, 5060));
                invokeTask = (Task)method.Invoke(service, new object[] { local, remote, request });
            }
            await invokeTask.ConfigureAwait(false);
        }

        [Fact]
        public async Task InviteRequest_CreatesSession()
        {
            var sessionManager = new CallSessionManager();
            var service = CreateService(sessionManager);
            await service.StartAsync(CancellationToken.None);

            string callId = Guid.NewGuid().ToString();
            string sdp = "v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=Test\r\nc=IN IP4 127.0.0.1\r\nm=audio 40000 RTP/AVP 0\r\n";
            var invite = CreateInvite(callId, sdp);
            await SendRequest(service, invite);

            await Task.Delay(500);

            Assert.Single(sessionManager.GetAllSessions());
            var session = sessionManager.GetSession(callId);
            Assert.NotNull(session);

            await service.StopAsync(CancellationToken.None);
        }

        [Fact]
        public async Task ByeRequest_TerminatesSession()
        {
            var sessionManager = new CallSessionManager();
            var service = CreateService(sessionManager);
            await service.StartAsync(CancellationToken.None);

            string callId = Guid.NewGuid().ToString();
            string sdp = "v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=Test\r\nc=IN IP4 127.0.0.1\r\nm=audio 40000 RTP/AVP 0\r\n";
            var invite = CreateInvite(callId, sdp);
            await SendRequest(service, invite);
            await Task.Delay(500);

            Assert.Single(sessionManager.GetAllSessions());

            var bye = CreateBye(callId);
            await SendRequest(service, bye);
            await Task.Delay(500);
            await service.StopAsync(CancellationToken.None);
            Assert.Empty(sessionManager.GetAllSessions());
        }

        [Fact]
        public async Task InviteRequest_SdpContainsAllocatedRtpPort()
        {
            var sessionManager = new CallSessionManager();
            var service = CreateService(sessionManager);
            await service.StartAsync(CancellationToken.None);

            string callId = Guid.NewGuid().ToString();
            string sdp =
                "v=0\r\n" +
                "o=- 0 0 IN IP4 127.0.0.1\r\n" +
                "s=Test\r\n" +
                "c=IN IP4 127.0.0.1\r\n" +
                "m=audio 0 RTP/AVP 0\r\n";
            var invite = CreateInvite(callId, sdp);
            await SendRequest(service, invite);
            await Task.Delay(500);

            // Retrieve cached 200 OK response
            var cacheField = typeof(SipServerService)
                .GetField("_inviteResponseCache", BindingFlags.Instance | BindingFlags.NonPublic);
            var cache = (ConcurrentDictionary<string, SIPResponse>)cacheField.GetValue(service);
            Assert.True(cache.TryGetValue(callId, out var okResponse));

            var answerSdp = SDP.ParseSDPDescription(okResponse.Body);
            var audioMedia = answerSdp.Media.First(m => m.Media == SDPMediaTypesEnum.audio);
            Assert.Equal(40000, audioMedia.Port);
        }

        [Fact]
        public async Task CancelRequest_TerminatesSession()
        {
            var sessionManager = new CallSessionManager();
            var service = CreateService(sessionManager);
            await service.StartAsync(CancellationToken.None);

            string callId = Guid.NewGuid().ToString();
            string sdp = "v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=Test\r\nc=IN IP4 127.0.0.1\r\nm=audio 40000 RTP/AVP 0\r\n";
            var invite = CreateInvite(callId, sdp);
            await SendRequest(service, invite);
            await Task.Delay(500);

            Assert.Single(sessionManager.GetAllSessions());

            var cancel = CreateCancel(callId);
            await SendRequest(service, cancel);
            await Task.Delay(500);
            await service.StopAsync(CancellationToken.None);
            Assert.Empty(sessionManager.GetAllSessions());
        }

        // Stub classes implementing required interface members

        private class StubSipRegistrationManager : ISipRegistrationManager
        {
            public void Dispose() { }

            public Task<SIPResponse> ProcessRegistrationAsync(SIPRequest request)
            {
                return Task.FromResult<SIPResponse>(null);
            }

            public IEnumerable<SipRegistration> GetActiveRegistrations()
            {
                return Enumerable.Empty<SipRegistration>();
            }

            public SipRegistration? GetRegistration(string userUri)
            {
                return null;
            }

            public Task<int> CleanupExpiredRegistrationsAsync()
            {
                return Task.FromResult(0);
            }

            public int ActiveRegistrationCount => 0;
        }

        private class StubPortAllocator : IPortAllocator
        {
            public int AllocatePort() => 40000;
            public void ReleasePort(int port) { }

            public Task<(UdpClient rtpClient, UdpClient rtcpClient)> AllocateRtpPairAsync(string callId, IPAddress localAddress)
            {
                var rtp = new UdpClient(40000);
                var rtcp = new UdpClient(40001);
                return Task.FromResult((rtpClient: rtp, rtcpClient: rtcp));
            }

            public Task<(UdpClient rtpClient, UdpClient rtcpClient)> AllocateSpecificPairAsync(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
            {
                var rtp = new UdpClient(rtpPort);
                var rtcp = new UdpClient(rtcpPort);
                return Task.FromResult((rtpClient: rtp, rtcpClient: rtcp));
            }

            public Task ReleasePortsAsync(string callId, UdpClient rtp, UdpClient rtcp)
            {
                rtp?.Dispose();
                rtcp?.Dispose();
                return Task.CompletedTask;
            }
        }

        private class StubRtpAudioReceiverFactory : IRtpAudioReceiverFactory
        {
            //public IAudioInputReceiver Create(string callId, int rtpPort, int rtcpPort) => new StubAudioInputReceiver();

            public IAudioInputReceiver CreateReceiver(string callId, int? rtpPort, int? rtcpPort)
            {
                // For testing, ignore nullable and just use default ports
                return new StubAudioInputReceiver();
            }

            public IAudioInputReceiver CreateReceiver(string callId, IPAddress localAddress)
            {
                // For testing, ignore localAddress and just use default receiver
                return new StubAudioInputReceiver();
            }
        }

        private class StubAudioInputReceiver : IAudioInputReceiver
        {
            public IPEndPoint RtpLocalEndPoint { get; } = new IPEndPoint(IPAddress.Loopback, 40000);
            public IPEndPoint RtcpLocalEndPoint { get; } = new IPEndPoint(IPAddress.Loopback, 40001);

            public void Dispose() { }
            public Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
            {
                return Task.CompletedTask;
            }
            public Task UpdateConfigurationAsync(IPEndPoint newRemoteEndpoint, string[] supportedCodecs)
            {
                return Task.CompletedTask;
            }
        }

        private class StubAudioProcessor : IAudioProcessor
        {
            public string ProcessorId { get; } = Guid.NewGuid().ToString();

            public void Dispose() { }
            public Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
            {
                return Task.CompletedTask;
            }

            public Task InitializeAsync(CancellationToken cancellationToken)
            {
                return Task.CompletedTask;
            }
        }

        private class StubSynchronizationService : ICallSynchronizationService
        {
            public int ActiveLockCount => 0;
            
            public async Task<T> ExecuteWithLockAsync<T>(string callId, Func<Task<T>> function, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
            {
                return await function();
            }

            public async Task ExecuteWithLockAsync(string callId, Func<Task> action, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
            {
                await action();
            }

            public Task CleanupLocksAsync(string callId)
            {
                return Task.CompletedTask;
            }
        }
    }
}