using System;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Provides stream identification and port information for RTP streams.
    /// </summary>
    public class RtpStreamInfo
    {
        /// <summary>
        /// Identifier of the RTP input stream.
        /// </summary>
        public string streamId { get; set; }

        /// <summary>
        /// RTP port number.
        /// </summary>
        public int port { get; set; }

        /// <summary>
        /// Allows deconstruction into (streamId, port).
        /// </summary>
        public void Deconstruct(out string streamId, out int port)
        {
            streamId = this.streamId;
            port = this.port;
        }
    }
}