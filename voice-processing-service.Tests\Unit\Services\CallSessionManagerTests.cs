using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class CallSessionManagerTests
    {
        // Dummy implementations for IAudioInputReceiver and IAudioProcessor
        private class DummyAudioReceiver : IAudioInputReceiver, IDisposable
        {
            public bool WasDisposed { get; private set; }
            public IPEndPoint RtpLocalEndPoint => new IPEndPoint(IPAddress.Loopback, 0);
            public IPEndPoint RtcpLocalEndPoint => new IPEndPoint(IPAddress.Loopback, 1);
            public Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken) =>
                Task.CompletedTask;
            public Task UpdateConfigurationAsync(IPEndPoint newRemoteEndpoint, string[] supportedCodecs) =>
                Task.CompletedTask;
            public void Dispose() => WasDisposed = true;
        }

        private class DummyAudioProcessor : IAudioProcessor, IDisposable
        {
            public bool WasDisposed { get; private set; }
            public string ProcessorId => "DUMMY_PROC";
            public Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken) =>
                Task.CompletedTask;
            public void Dispose() => WasDisposed = true;
        }

        private SIPServerUserAgent CreateUserAgent() =>
            (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));

        private SIPRequest CreateInvite(string callId)
        {
            var invite = (SIPRequest)FormatterServices.GetUninitializedObject(typeof(SIPRequest));
            var header = (SIPHeader)FormatterServices.GetUninitializedObject(typeof(SIPHeader));
            header.CallId = callId;
            invite.Header = header;
            return invite;
        }

        [Fact]
        public void Constructor_ShouldCreateInstance()
        {
            var manager = new CallSessionManager(NullLogger<CallSessionManager>.Instance, NullLoggerFactory.Instance);
            Assert.NotNull(manager);
        }

        [Fact]
        public async Task CreateSessionAsync_ShouldAddSession()
        {
            var manager = new CallSessionManager(NullLogger<CallSessionManager>.Instance, NullLoggerFactory.Instance);
            var ua = CreateUserAgent();
            var invite = CreateInvite("CALL1");

            var session = await manager.CreateSessionAsync(
                ua,
                invite,
                () => new DummyAudioReceiver(),
                () => new DummyAudioProcessor());

            Assert.NotNull(session);
            Assert.Same(session, manager.GetSession("CALL1"));
        }

        [Fact]
        public async Task CreateSessionAsync_DuplicateCallId_ReturnsExistingSession()
        {
            var manager = new CallSessionManager(NullLogger<CallSessionManager>.Instance, NullLoggerFactory.Instance);
            var ua = CreateUserAgent();
            var invite1 = CreateInvite("DUP");
            var invite2 = CreateInvite("DUP");

            var session1 = await manager.CreateSessionAsync(
                ua,
                invite1,
                () => new DummyAudioReceiver(),
                () => new DummyAudioProcessor());

            var session2 = await manager.CreateSessionAsync(
                ua,
                invite2,
                () => new DummyAudioReceiver(),
                () => new DummyAudioProcessor());

            Assert.Same(session1, session2);
        }

        [Fact]
        public void GetSession_NonExisting_ReturnsNull()
        {
            var manager = new CallSessionManager(NullLogger<CallSessionManager>.Instance, NullLoggerFactory.Instance);
            Assert.Null(manager.GetSession("NO_SUCH_CALL"));
        }

        [Fact]
        public async Task TerminateSessionAsync_RemovesSession()
        {
            var manager = new CallSessionManager(NullLogger<CallSessionManager>.Instance, NullLoggerFactory.Instance);
            var ua = CreateUserAgent();
            var invite = CreateInvite("T1");

            var session = await manager.CreateSessionAsync(
                ua,
                invite,
                () => new DummyAudioReceiver(),
                () => new DummyAudioProcessor());

            // confirm session exists
            Assert.NotNull(manager.GetSession("T1"));

            await manager.TerminateSessionAsync("T1");

            // after termination and cleanup callback, session removed
            Assert.Null(manager.GetSession("T1"));
        }

        [Fact]
        public async Task TerminateSessionAsync_NonExisting_DoesNotThrow()
        {
            var manager = new CallSessionManager(NullLogger<CallSessionManager>.Instance, NullLoggerFactory.Instance);
            await manager.TerminateSessionAsync("UNKNOWN");
        }

        [Fact]
        public async Task GetAllSessions_ReturnsActiveSessions()
        {
            var manager = new CallSessionManager(NullLogger<CallSessionManager>.Instance, NullLoggerFactory.Instance);
            var ua = CreateUserAgent();

            var inviteA = CreateInvite("A");
            var inviteB = CreateInvite("B");
            await manager.CreateSessionAsync(
                ua,
                inviteA,
                () => new DummyAudioReceiver(),
                () => new DummyAudioProcessor());
            await manager.CreateSessionAsync(
                ua,
                inviteB,
                () => new DummyAudioReceiver(),
                () => new DummyAudioProcessor());

            var all = manager.GetAllSessions().Select(s => s.CallId).ToList();
            Assert.Contains("A", all);
            Assert.Contains("B", all);

            await manager.TerminateSessionAsync("A");
            var remaining = manager.GetAllSessions().Select(s => s.CallId).ToList();
            Assert.DoesNotContain("A", remaining);
            Assert.Contains("B", remaining);
        }

        [Fact]
        public async Task CreateSessionAsync_ReInviteWithMatchingTags_ReturnsSameSession()
        {
            var manager = new CallSessionManager(NullLogger<CallSessionManager>.Instance, NullLoggerFactory.Instance);
            var ua = CreateUserAgent();
            var invite1 = CreateInvite("TAGCALL");
            var session1 = await manager.CreateSessionAsync(
                ua,
                invite1,
                () => new DummyAudioReceiver(),
                () => new DummyAudioProcessor());

            // simulate in-dialog INVITE with tags
            var invite2 = CreateInvite("TAGCALL");
            var header2 = invite2.Header;
            header2.From = (SIPFromHeader)FormatterServices.GetUninitializedObject(typeof(SIPFromHeader));
            header2.From.FromTag = "fromTag";
            header2.To = (SIPToHeader)FormatterServices.GetUninitializedObject(typeof(SIPToHeader));
            header2.To.ToTag = "toTag";

            var session2 = await manager.CreateSessionAsync(
                ua,
                invite2,
                () => new DummyAudioReceiver(),
                () => new DummyAudioProcessor());

            Assert.Same(session1, session2);
        }
    }
}