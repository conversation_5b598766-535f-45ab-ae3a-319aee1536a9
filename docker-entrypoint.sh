#!/bin/bash
set -e

# Docker entrypoint script for voice-processing-service
# This script handles container initialization and environment setup

echo "Starting Voice Processing Service..."
echo "Environment: ${ASPNETCORE_ENVIRONMENT:-Production}"
echo "Log Path: ${LOG_PATH:-/app/logs}"
echo "Recordings Path: ${RECORDINGS_PATH:-/app/recordings}"

# Create necessary directories
mkdir -p "${LOG_PATH:-/app/logs}"
mkdir -p "${RECORDINGS_PATH:-/app/recordings}"

# Set proper permissions
chmod 755 "${LOG_PATH:-/app/logs}"
chmod 755 "${RECORDINGS_PATH:-/app/recordings}"

# Display configuration summary
echo "=== Configuration Summary ==="
echo "SIP Listen IP: ${SIP_LISTEN_IP:-0.0.0.0}"
echo "SIP Listen Port: ${SIP_LISTEN_PORT:-5060}"
echo "RTP Port Range: ${RTP_PORT_MIN:-10000}-${RTP_PORT_MAX:-19998}"
echo "Phonexia API URL: ${PHONEXIA_API_URL:-http://localhost:8600}"
echo "Phonexia Language: ${PHONEXIA_LANGUAGE:-cs-CZ}"
echo "Phonexia Model: ${PHONEXIA_MODEL:-CS_CZ_O2_6}"
echo "============================="

# Wait for Phonexia service if configured
if [ ! -z "${PHONEXIA_API_URL}" ] && [ "${PHONEXIA_API_URL}" != "http://localhost:8600" ]; then
    echo "Waiting for Phonexia service at ${PHONEXIA_API_URL}..."
    
    # Extract host and port from URL
    PHONEXIA_HOST=$(echo "${PHONEXIA_API_URL}" | sed -e 's|^[^/]*//||' -e 's|:[0-9]*||' -e 's|/.*||')
    PHONEXIA_PORT=$(echo "${PHONEXIA_API_URL}" | sed -e 's|^[^/]*//[^:]*:||' -e 's|/.*||')
    
    # Default port if not specified
    if [ "${PHONEXIA_PORT}" = "${PHONEXIA_HOST}" ]; then
        PHONEXIA_PORT=8600
    fi
    
    echo "Checking connectivity to ${PHONEXIA_HOST}:${PHONEXIA_PORT}..."
    
    # Wait up to 60 seconds for Phonexia to be available
    timeout=60
    while [ $timeout -gt 0 ]; do
        if nc -z "${PHONEXIA_HOST}" "${PHONEXIA_PORT}" 2>/dev/null; then
            echo "Phonexia service is available!"
            break
        fi
        echo "Waiting for Phonexia service... (${timeout}s remaining)"
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        echo "Warning: Phonexia service is not available after 60 seconds. Continuing anyway..."
    fi
fi

# Log startup information
echo "Starting application with the following environment:"
env | grep -E '^(ASPNETCORE_|SIP_|PHONEXIA_|LOG_|RECORDINGS_)' | sort

# Execute the main application
echo "Executing: $@"
exec "$@"
