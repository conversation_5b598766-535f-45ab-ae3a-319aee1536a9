# Implementace správy hovor<PERSON>

## Popis úkolu

Tento úkol zahrnuje implementaci správy ho<PERSON>, která propojuje všechny komponenty (RTP přijímač, audio buffer, WAV procesor) a říd<PERSON> jejich životní cyklus. Správa hovorů se skládá ze dvou hlavních tříd:

1. **CallSession** - Reprezentuje jeden aktivní hovor a dr<PERSON><PERSON> pohromadě všechny jeho komponenty
2. **CallSessionManager** - Spravuje všechny aktivní hovory a poskytuje rozhraní pro jejich vytváření a ukončování

## Technické detaily

### Implementace CallSession

Třída CallSession implementuje rozhraní ICallSession a je zodpovědná za řízení životního cyklu jednoho hovoru.

```csharp
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace session jednoho hovoru.
    /// </summary>
    public class CallSession : ICallSession
    {
        private readonly ILogger<CallSession> _logger;
        private readonly IAudioInputReceiver _audioReceiver;
        private readonly IAudioBuffer _audioBuffer;
        private readonly IAudioProcessor _audioProcessor;
        private readonly Func<Task> _cleanupCallback;
        private CancellationTokenSource _sessionCts;
        private Task _receiverTask;
        private Task _processorTask;
        private bool _disposed = false;

        /// <summary>
        /// Identifikátor hovoru (Call-ID).
        /// </summary>
        public string CallId { get; }

        /// <summary>
        /// SIP User Agent pro tento hovor.
        /// </summary>
        public SIPUserAgent UserAgent { get; }

        /// <summary>
        /// Původní INVITE požadavek, který zahájil hovor.
        /// </summary>
        public SIPRequest InitialInviteRequest { get; }

        /// <summary>
        /// Čas zahájení hovoru.
        /// </summary>
        public DateTime StartTime { get; }

        /// <summary>
        /// Vytvoří novou instanci CallSession.
        /// </summary>
        /// <param name="userAgent">SIP User Agent pro tento hovor.</param>
        /// <param name="inviteRequest">INVITE požadavek, který zahájil hovor.</param>
        /// <param name="audioReceiver">Přijímač audio dat.</param>
        /// <param name="audioBuffer">Buffer pro audio data.</param>
        /// <param name="audioProcessor">Procesor audio dat.</param>
        /// <param name="cleanupCallback">Callback pro úklid po ukončení session.</param>
        /// <param name="logger">Logger.</param>
        public CallSession(
            SIPUserAgent userAgent,
            SIPRequest inviteRequest,
            IAudioInputReceiver audioReceiver,
            IAudioBuffer audioBuffer,
            IAudioProcessor audioProcessor,
            Func<Task> cleanupCallback,
            ILogger<CallSession> logger)
        {
            UserAgent = userAgent ?? throw new ArgumentNullException(nameof(userAgent));
            InitialInviteRequest = inviteRequest ?? throw new ArgumentNullException(nameof(inviteRequest));
            _audioReceiver = audioReceiver ?? throw new ArgumentNullException(nameof(audioReceiver));
            _audioBuffer = audioBuffer ?? throw new ArgumentNullException(nameof(audioBuffer));
            _audioProcessor = audioProcessor ?? throw new ArgumentNullException(nameof(audioProcessor));
            _cleanupCallback = cleanupCallback ?? throw new ArgumentNullException(nameof(cleanupCallback));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            CallId = inviteRequest.Header.CallId;
            StartTime = DateTime.UtcNow;

            // Handler pro zavěšení hovoru
            UserAgent.OnCallHungup += async (dialog) =>
            {
                _logger.LogInformation($"[{CallId}] Call hung up event received (Reason: {dialog?.HangupReason}). Initiating stop.");
                await StopAsync();
            };

            _logger.LogInformation($"[{CallId}] CallSession created. Processor: {_audioProcessor.ProcessorId}, Receiver RTP: {_audioReceiver.RtpLocalEndPoint}");
        }

        /// <summary>
        /// Spustí session (zahájí příjem a zpracování audio dat).
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StartAsync(CancellationToken cancellationToken)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(CallSession));
            }

            _logger.LogInformation($"[{CallId}] Starting call session.");

            // Vytvoření CancellationTokenSource pro tuto session
            _sessionCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            try
            {
                // Spuštění přijímače
                _receiverTask = _audioReceiver.StartListeningAsync(_audioBuffer, _sessionCts.Token);

                // Spuštění procesoru
                _processorTask = _audioProcessor.StartProcessingAsync(_audioBuffer, _sessionCts.Token);

                _logger.LogInformation($"[{CallId}] Call session started successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{CallId}] Error starting call session.");
                await StopAsync();
                throw;
            }
        }

        /// <summary>
        /// Zastaví session (ukončí příjem a zpracování audio dat).
        /// </summary>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StopAsync()
        {
            if (_disposed)
            {
                return;
            }

            _logger.LogInformation($"[{CallId}] Stopping call session.");

            try
            {
                // Zrušení všech operací
                if (_sessionCts != null && !_sessionCts.IsCancellationRequested)
                {
                    _sessionCts.Cancel();
                }

                // Ukončení přidávání do bufferu
                _audioBuffer.CompleteAdding();

                // Čekání na dokončení úloh
                if (_receiverTask != null)
                {
                    try
                    {
                        await _receiverTask;
                    }
                    catch (OperationCanceledException)
                    {
                        // Očekávaná výjimka při zrušení
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"[{CallId}] Error waiting for receiver task to complete.");
                    }
                }

                if (_processorTask != null)
                {
                    try
                    {
                        await _processorTask;
                    }
                    catch (OperationCanceledException)
                    {
                        // Očekávaná výjimka při zrušení
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"[{CallId}] Error waiting for processor task to complete.");
                    }
                }

                // Hangup SIP dialogu, pokud je aktivní
                if (UserAgent.IsCallActive)
                {
                    try
                    {
                        _logger.LogInformation($"[{CallId}] Hanging up active call.");
                        await UserAgent.Hangup();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"[{CallId}] Error hanging up call.");
                    }
                }

                // Volání cleanup callbacku
                try
                {
                    await _cleanupCallback();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"[{CallId}] Error in cleanup callback.");
                }

                _logger.LogInformation($"[{CallId}] Call session stopped successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{CallId}] Error stopping call session.");
                throw;
            }
        }

        /// <summary>
        /// Uvolní prostředky.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Uvolní prostředky.
        /// </summary>
        /// <param name="disposing">True, pokud je volána z Dispose(), false pokud je volána z finalizeru.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
            {
                return;
            }

            if (disposing)
            {
                _logger.LogInformation($"[{CallId}] Disposing call session.");

                // Zastavení session
                StopAsync().GetAwaiter().GetResult();

                // Uvolnění prostředků
                _sessionCts?.Dispose();
                _audioReceiver?.Dispose();
                _audioBuffer?.Dispose();
                _audioProcessor?.Dispose();
            }

            _disposed = true;
        }
    }
}
```

### Implementace CallSessionManager

Třída CallSessionManager implementuje rozhraní ICallSessionManager a je zodpovědná za správu všech aktivních hovorů.

```csharp
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace správce sessions hovorů.
    /// </summary>
    public class CallSessionManager : ICallSessionManager
    {
        private readonly ILogger<CallSessionManager> _logger;
        private readonly ILoggerFactory _loggerFactory;
        private readonly ConcurrentDictionary<string, ICallSession> _sessions = new ConcurrentDictionary<string, ICallSession>();

        /// <summary>
        /// Vytvoří novou instanci CallSessionManager.
        /// </summary>
        /// <param name="loggerFactory">Továrna pro vytváření loggerů.</param>
        public CallSessionManager(ILoggerFactory loggerFactory)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _logger = loggerFactory.CreateLogger<CallSessionManager>();
            _logger.LogInformation("CallSessionManager created.");
        }

        /// <summary>
        /// Vytvoří novou session pro hovor.
        /// </summary>
        /// <param name="userAgent">SIP User Agent pro tento hovor.</param>
        /// <param name="inviteRequest">INVITE požadavek, který zahájil hovor.</param>
        /// <param name="inputReceiverFactory">Tovární metoda pro vytvoření přijímače audio dat.</param>
        /// <param name="audioProcessorFactory">Tovární metoda pro vytvoření procesoru audio dat.</param>
        /// <returns>Task reprezentující asynchronní operaci, která vrací vytvořenou session.</returns>
        public Task<ICallSession> CreateSessionAsync(
            SIPUserAgent userAgent,
            SIPRequest inviteRequest,
            Func<IAudioInputReceiver> inputReceiverFactory,
            Func<IAudioProcessor> audioProcessorFactory)
        {
            if (userAgent == null) throw new ArgumentNullException(nameof(userAgent));
            if (inviteRequest == null) throw new ArgumentNullException(nameof(inviteRequest));
            if (inputReceiverFactory == null) throw new ArgumentNullException(nameof(inputReceiverFactory));
            if (audioProcessorFactory == null) throw new ArgumentNullException(nameof(audioProcessorFactory));

            string callId = inviteRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Creating new call session.");

            // Vytvoření komponent pro session
            IAudioInputReceiver receiver = null;
            IAudioBuffer buffer = null;
            IAudioProcessor processor = null;

            try
            {
                // Vytvoření přijímače
                receiver = inputReceiverFactory();
                if (receiver == null)
                {
                    throw new InvalidOperationException($"[{callId}] Input receiver factory returned null.");
                }

                // Vytvoření bufferu
                buffer = new BlockingCollectionAudioBuffer(_loggerFactory.CreateLogger<BlockingCollectionAudioBuffer>());

                // Vytvoření procesoru
                processor = audioProcessorFactory();
                if (processor == null)
                {
                    throw new InvalidOperationException($"[{callId}] Audio processor factory returned null.");
                }

                // Callback pro úklid v manažeru, až session skončí
                Func<Task> cleanupCallback = async () =>
                {
                    _logger.LogInformation($"[{callId}] Cleanup callback invoked. Removing session from manager.");
                    if (_sessions.TryRemove(callId, out _))
                    {
                        _logger.LogDebug($"[{callId}] Session successfully removed from manager.");
                    }
                    else
                    {
                        _logger.LogWarning($"[{callId}] Session was not found in manager during cleanup callback.");
                    }
                    await Task.CompletedTask;
                };

                // Vytvoření session
                var session = new CallSession(
                    userAgent,
                    inviteRequest,
                    receiver,
                    buffer,
                    processor,
                    cleanupCallback,
                    _loggerFactory.CreateLogger<CallSession>()
                );

                // Přidání session do slovníku
                if (_sessions.TryAdd(callId, session))
                {
                    _logger.LogInformation($"[{callId}] Session created and added to manager.");
                    return Task.FromResult<ICallSession>(session);
                }
                else
                {
                    _logger.LogWarning($"[{callId}] Failed to add session to manager (duplicate Call-ID?). Disposing created session components.");
                    session.Dispose();
                    throw new InvalidOperationException($"[{callId}] Failed to add session to manager (duplicate Call-ID?).");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error creating call session.");

                // Úklid v případě chyby
                processor?.Dispose();
                buffer?.Dispose();
                receiver?.Dispose();

                throw;
            }
        }

        /// <summary>
        /// Získá session podle identifikátoru hovoru.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <returns>Session hovoru, nebo null, pokud session neexistuje.</returns>
        public ICallSession GetSession(string callId)
        {
            if (string.IsNullOrEmpty(callId))
            {
                throw new ArgumentException("Call ID cannot be null or empty.", nameof(callId));
            }

            _sessions.TryGetValue(callId, out var session);
            return session;
        }

        /// <summary>
        /// Ukončí session hovoru.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task TerminateSessionAsync(string callId)
        {
            if (string.IsNullOrEmpty(callId))
            {
                throw new ArgumentException("Call ID cannot be null or empty.", nameof(callId));
            }

            _logger.LogInformation($"[{callId}] Terminating call session.");

            if (_sessions.TryGetValue(callId, out var session))
            {
                try
                {
                    await session.StopAsync();
                    _logger.LogInformation($"[{callId}] Call session terminated successfully.");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{callId}] Error terminating call session.");
                    throw;
                }
            }
            else
            {
                _logger.LogWarning($"[{callId}] Call session not found for termination.");
            }
        }

        /// <summary>
        /// Získá všechny aktivní session hovorů.
        /// </summary>
        /// <returns>Kolekce aktivních session hovorů.</returns>
        public IEnumerable<ICallSession> GetAllSessions()
        {
            return _sessions.Values.ToList();
        }
    }
}
```

## Testovací scénáře

### Unit testy pro CallSession

1. **Test konstruktoru**
   - Ověřit, že instance CallSession je vytvořena bez chyb
   - Ověřit, že všechny závislosti jsou správně nastaveny
   - Ověřit, že vlastnosti CallId, UserAgent, InitialInviteRequest a StartTime jsou správně nastaveny

2. **Test metody StartAsync**
   - Ověřit, že metoda StartAsync spustí přijímač a procesor
   - Ověřit, že metoda StartAsync respektuje cancellation token
   - Ověřit, že metoda StartAsync vyhodí výjimku, pokud je session již ukončena (disposed)

3. **Test metody StopAsync**
   - Ověřit, že metoda StopAsync zastaví přijímač a procesor
   - Ověřit, že metoda StopAsync ukončí SIP dialog, pokud je aktivní
   - Ověřit, že metoda StopAsync zavolá cleanup callback

4. **Test metody Dispose**
   - Ověřit, že po zavolání Dispose jsou všechny prostředky správně uvolněny
   - Ověřit, že po zavolání Dispose nelze spustit session

### Unit testy pro CallSessionManager

1. **Test konstruktoru**
   - Ověřit, že instance CallSessionManager je vytvořena bez chyb

2. **Test metody CreateSessionAsync**
   - Ověřit, že metoda CreateSessionAsync vytvoří instanci CallSession
   - Ověřit, že metoda CreateSessionAsync přidá session do slovníku
   - Ověřit, že metoda CreateSessionAsync vyhodí výjimku, pokud je Call-ID duplicitní

3. **Test metody GetSession**
   - Ověřit, že metoda GetSession vrátí správnou session podle Call-ID
   - Ověřit, že metoda GetSession vrátí null, pokud session neexistuje

4. **Test metody TerminateSessionAsync**
   - Ověřit, že metoda TerminateSessionAsync zavolá StopAsync na session
   - Ověřit, že metoda TerminateSessionAsync neselže, pokud session neexistuje

5. **Test metody GetAllSessions**
   - Ověřit, že metoda GetAllSessions vrátí všechny aktivní session

### Integrační testy pro CallSession a CallSessionManager

1. **Test vytvoření a ukončení session**
   - Vytvořit instanci CallSessionManager
   - Vytvořit session pomocí CreateSessionAsync
   - Spustit session pomocí StartAsync
   - Ukončit session pomocí TerminateSessionAsync
   - Ověřit, že session byla správně ukončena a odstraněna ze slovníku

2. **Test více souběžných sessions**
   - Vytvořit instanci CallSessionManager
   - Vytvořit několik sessions s různými Call-ID
   - Spustit všechny sessions
   - Ukončit některé sessions
   - Ověřit, že ukončené sessions byly odstraněny ze slovníku a ostatní zůstaly aktivní

## Implementační kroky

1. Implementovat třídu CallSession
2. Implementovat třídu CallSessionManager
3. Implementovat unit testy pro CallSession
4. Implementovat unit testy pro CallSessionManager
5. Implementovat integrační testy pro CallSession a CallSessionManager
6. Registrovat CallSessionManager v DI kontejneru

## Simulace pro testování

Pro testování CallSession a CallSessionManager bude vytvořena jednoduchá konzolová aplikace, která simuluje vytvoření a ukončení hovorů:

```csharp
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;

namespace voice_processing_service.Simulation
{
    class Program
    {
        static async Task Main(string[] args)
        {
            // Vytvoření loggeru
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddFilter("Microsoft", LogLevel.Warning)
                    .AddFilter("System", LogLevel.Warning)
                    .AddFilter("voice_processing_service", LogLevel.Debug)
                    .AddConsole();
            });
            var logger = loggerFactory.CreateLogger<Program>();

            logger.LogInformation("Starting CallSession and CallSessionManager simulation.");

            // Vytvoření CallSessionManager
            var sessionManager = new CallSessionManager(loggerFactory);

            // Simulace vytvoření a ukončení hovorů
            await SimulateCallsAsync(sessionManager, loggerFactory, logger);

            logger.LogInformation("Simulation completed.");
        }

        static async Task SimulateCallsAsync(
            ICallSessionManager sessionManager,
            ILoggerFactory loggerFactory,
            ILogger logger)
        {
            // Vytvoření CancellationTokenSource pro ukončení simulace
            using var cts = new CancellationTokenSource();
            cts.CancelAfter(TimeSpan.FromSeconds(30)); // Simulace bude běžet 30 sekund

            // Simulace vytvoření několika hovorů
            var call1Task = CreateAndRunCallAsync(sessionManager, "call1", loggerFactory, logger, cts.Token);
            await Task.Delay(2000); // Pauza mezi hovory
            var call2Task = CreateAndRunCallAsync(sessionManager, "call2", loggerFactory, logger, cts.Token);
            await Task.Delay(2000); // Pauza mezi hovory
            var call3Task = CreateAndRunCallAsync(sessionManager, "call3", loggerFactory, logger, cts.Token);

            // Výpis aktivních hovorů
            logger.LogInformation("Active calls:");
            foreach (var session in sessionManager.GetAllSessions())
            {
                logger.LogInformation($"- {session.CallId} (started at {session.StartTime})");
            }

            // Ukončení hovoru call2 po 5 sekundách
            await Task.Delay(5000);
            logger.LogInformation("Terminating call2...");
            await sessionManager.TerminateSessionAsync("call2");

            // Výpis aktivních hovorů po ukončení call2
            logger.LogInformation("Active calls after terminating call2:");
            foreach (var session in sessionManager.GetAllSessions())
            {
                logger.LogInformation($"- {session.CallId} (started at {session.StartTime})");
            }

            // Čekání na ukončení všech hovorů
            try
            {
                await Task.WhenAll(call1Task, call2Task, call3Task);
                logger.LogInformation("All calls completed.");
            }
            catch (OperationCanceledException)
            {
                logger.LogInformation("Simulation was cancelled.");
            }
        }

        static async Task CreateAndRunCallAsync(
            ICallSessionManager sessionManager,
            string callId,
            ILoggerFactory loggerFactory,
            ILogger logger,
            CancellationToken cancellationToken)
        {
            logger.LogInformation($"Creating call {callId}...");

            // Vytvoření mock SIPUserAgent
            var userAgent = new SIPUserAgent();

            // Vytvoření mock INVITE požadavku
            var inviteRequest = new SIPRequest(SIPMethodsEnum.INVITE, new SIPURI(SIPSchemesEnum.sip, "dummy.com"));
            inviteRequest.Header.CallId = callId;

            // Tovární metody pro vytvoření přijímače a procesoru
            Func<IAudioInputReceiver> receiverFactory = () =>
            {
                // Vytvoření mock přijímače
                var mockReceiver = new MockAudioInputReceiver(callId, loggerFactory.CreateLogger<MockAudioInputReceiver>());
                return mockReceiver;
            };

            Func<IAudioProcessor> processorFactory = () =>
            {
                // Vytvoření mock procesoru
                var mockProcessor = new MockAudioProcessor(callId, loggerFactory.CreateLogger<MockAudioProcessor>());
                return mockProcessor;
            };

            // Vytvoření session
            var session = await sessionManager.CreateSessionAsync(userAgent, inviteRequest, receiverFactory, processorFactory);

            // Spuštění session
            await session.StartAsync(cancellationToken);

            logger.LogInformation($"Call {callId} started.");

            // Simulace trvání hovoru (10-20 sekund)
            var callDuration = new Random().Next(10, 20);
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(callDuration), cancellationToken);
                logger.LogInformation($"Call {callId} duration ({callDuration}s) elapsed, terminating...");
                await sessionManager.TerminateSessionAsync(callId);
            }
            catch (OperationCanceledException)
            {
                logger.LogInformation($"Call {callId} was cancelled.");
            }
        }
    }

    // Mock třídy pro testování

    class MockAudioInputReceiver : IAudioInputReceiver
    {
        private readonly string _callId;
        private readonly ILogger<MockAudioInputReceiver> _logger;
        private bool _disposed = false;

        public IPEndPoint RtpLocalEndPoint => new IPEndPoint(IPAddress.Loopback, 10000);
        public IPEndPoint RtcpLocalEndPoint => new IPEndPoint(IPAddress.Loopback, 10001);

        public MockAudioInputReceiver(string callId, ILogger<MockAudioInputReceiver> logger)
        {
            _callId = callId;
            _logger = logger;
            _logger.LogInformation($"[{_callId}] MockAudioInputReceiver created.");
        }

        public Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(MockAudioInputReceiver));
            }

            _logger.LogInformation($"[{_callId}] MockAudioInputReceiver started listening.");

            // Simulace příjmu audio dat
            return Task.Run(async () =>
            {
                try
                {
                    while (!cancellationToken.IsCancellationRequested)
                    {
                        // Simulace příjmu audio dat každých 20ms
                        await Task.Delay(20, cancellationToken);

                        // Vytvoření náhodných audio dat (160 bytů = 20ms G.711 při 8kHz)
                        byte[] audioData = new byte[160];
                        new Random().NextBytes(audioData);

                        // Přidání dat do bufferu
                        buffer.Add(audioData);
                    }
                }
                catch (OperationCanceledException)
                {
                    // Očekávaná výjimka při zrušení
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{_callId}] Error in MockAudioInputReceiver.");
                }
                finally
                {
                    _logger.LogInformation($"[{_callId}] MockAudioInputReceiver stopped listening.");
                }
            });
        }

        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            _logger.LogInformation($"[{_callId}] MockAudioInputReceiver disposed.");
            _disposed = true;
        }
    }

    class MockAudioProcessor : IAudioProcessor
    {
        private readonly string _callId;
        private readonly ILogger<MockAudioProcessor> _logger;
        private bool _disposed = false;

        public string ProcessorId => $"MOCK_{_callId}";

        public MockAudioProcessor(string callId, ILogger<MockAudioProcessor> logger)
        {
            _callId = callId;
            _logger = logger;
            _logger.LogInformation($"[{_callId}] MockAudioProcessor created.");
        }

        public Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(MockAudioProcessor));
            }

            _logger.LogInformation($"[{_callId}] MockAudioProcessor started processing.");

            // Simulace zpracování audio dat
            return Task.Run(async () =>
            {
                try
                {
                    int totalBytesProcessed = 0;

                    while (!cancellationToken.IsCancellationRequested)
                    {
                        // Pokus o získání audio dat z bufferu
                        if (buffer.TryTake(out byte[] audioData, 100, cancellationToken))
                        {
                            // Simulace zpracování audio dat
                            totalBytesProcessed += audioData.Length;
                            _logger.LogDebug($"[{_callId}] Processed {audioData.Length} bytes (total: {totalBytesProcessed}).");
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    // Očekávaná výjimka při zrušení
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{_callId}] Error in MockAudioProcessor.");
                }
                finally
                {
                    _logger.LogInformation($"[{_callId}] MockAudioProcessor stopped processing.");
                }
            });
        }

        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            _logger.LogInformation($"[{_callId}] MockAudioProcessor disposed.");
            _disposed = true;
        }
    }
}
```
