using System;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Net;
using SIPSorcery.Media;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using SIPSorceryMedia.Abstractions;
using System.Security.Cryptography;
using SIPSorcery.Sys;
// Add caching for retransmitted INVITEs
using System.Collections.Concurrent;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace SIP serveru jako IHostedService.
    /// </summary>
    public class SipServerServiceBackup2 //: IHostedService
    {
        private readonly ILogger<SipServerService> _logger;
        private readonly ICallSessionManager _sessionManager;
        private readonly SipServerOptions _options;
        private readonly ILoggerFactory _loggerFactory;
        // Tato tov<PERSON>rna by <PERSON><PERSON><PERSON><PERSON>, protože porty v SDP a náhodně vytvořené porty by byly jiné
        // Ponecháváme ji zde pouze pro kompatibilitu s existujícím kódem
         //private readonly Func<IAudioInputReceiver> _audioInputReceiverFactory;
         private readonly Func<string, IAudioProcessor> _audioProcessorFactory;
         private readonly IPortAllocator _portAllocator;
         private readonly IRtpAudioReceiverFactory _rtpAudioReceiverFactory;
        private SIPTransport _sipTransport;
        private readonly ConcurrentDictionary<string, SIPResponse> _inviteResponseCache = new ConcurrentDictionary<string, SIPResponse>();
        // Proměnné pro aktuální hovor
        private SIPServerUserAgent _userAgent;
        private VoIPMediaSession _rtpSession;

        /// <summary>
        /// Vytvoří novou instanci SipServerService.
        /// </summary>
        /// <param name="logger">Logger.</param>
        /// <param name="sessionManager">Správce sessions hovorů.</param>
        /// <param name="options">Konfigurace SIP serveru.</param>
        public SipServerServiceBackup2(
            ILogger<SipServerService> logger,
            ICallSessionManager sessionManager,
            IOptions<SipServerOptions> options,
            ILoggerFactory loggerFactory,
            IPortAllocator portAllocator,
            IRtpAudioReceiverFactory rtpAudioReceiverFactory,
            Func<string, IAudioProcessor> audioProcessorFactory)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _sessionManager = sessionManager ?? throw new ArgumentNullException(nameof(sessionManager));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _portAllocator = portAllocator ?? throw new ArgumentNullException(nameof(portAllocator));
            _rtpAudioReceiverFactory = rtpAudioReceiverFactory ?? throw new ArgumentNullException(nameof(rtpAudioReceiverFactory));
            _audioProcessorFactory = audioProcessorFactory ?? throw new ArgumentNullException(nameof(audioProcessorFactory));
        }

        /// <summary>
        /// Spustí SIP server.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting SIP server...");

            try
            {
                // Vytvoření SIP transportu
                _sipTransport = new SIPTransport();

                // Nastavení loggeru pro SIPSorcery
                SIPSorcery.LogFactory.Set(_loggerFactory);

                // Nastavení IP adresy pro naslouchání
                //IPAddress listenAddress = _options.ListenIpAddress == "Any" ? IPAddress.Any : IPAddress.Parse(_options.ListenIpAddress);

                // Přidání SIP kanálu pro naslouchání (pouze UDP, TLS bude implementován později)
                var sipChannel = new SIPUDPChannel(IPAddress.Any, _options.ListenPort);
                _sipTransport.AddSIPChannel(sipChannel);

                _logger.LogInformation($"SIP server listening on {System.Net.IPAddress.Any}:{_options.ListenPort}");

                // Registrace handleru pro SIP požadavky
                _sipTransport.SIPTransportRequestReceived += OnSipRequestReceived;

                // Poznámka: SIPServerUserAgent bude vytvořen pro každý příchozí hovor

                _logger.LogInformation("SIP server started successfully.");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting SIP server.");
                throw;
            }
        }

        /// <summary>
        /// Zastaví SIP server.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping SIP server...");

            try
            {
                // Ukončení všech aktivních hovorů
                foreach (var session in _sessionManager.GetAllSessions())
                {
                    try
                    {
                        _logger.LogInformation($"Terminating call {session.CallId}...");
                        await _sessionManager.TerminateSessionAsync(session.CallId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"Error terminating call {session.CallId}.");
                    }
                }

                // Odregistrace handleru pro SIP požadavky
                if (_sipTransport != null)
                {
                    // Handler je anonymní funkce, nelze odregistrovat
                    // Ale _sipTransport bude uvolněn, takže handler nebude volán
                }

                // Ukončení aktivního hovoru
                _userAgent?.Hangup(false);
                _rtpSession?.Close("Server shutdown");

                // Uvolnění SIP transportu
                _sipTransport?.Dispose();
                _sipTransport = null;

                _logger.LogInformation("SIP server stopped successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping SIP server.");
                throw;
            }
        }

        /// <summary>
        /// Handler pro příchozí SIP požadavky.
        /// </summary>
        private Task OnSipRequestReceived(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogDebug($"[{callId}] Received {sipRequest.Method} request from {remoteEndPoint}.");

            try
            {
                switch (sipRequest.Method)
                {
                    case SIPMethodsEnum.INVITE:
                        // Zpracování INVITE požadavku
                        _ = ProcessInviteAsync(localSIPEndPoint, remoteEndPoint, sipRequest);
                        break;

                    case SIPMethodsEnum.BYE:
                        // Zpracování BYE požadavku
                        _ = ProcessByeAsync(sipRequest);
                        break;

                    case SIPMethodsEnum.CANCEL:
                        // Zpracování CANCEL požadavku
                        _ = ProcessCancelAsync(sipRequest);
                        break;

                    case SIPMethodsEnum.OPTIONS:
                    case SIPMethodsEnum.REGISTER:
                        // Odpověď 200 OK na OPTIONS a REGISTER požadavky
                        _ = Task.Run(async () => {
                            SIPResponse optionsResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                            await _sipTransport.SendResponseAsync(optionsResponse);
                            _logger.LogDebug($"[{callId}] Sent 200 OK response to {sipRequest.Method} request.");
                        });
                        break;

                    case SIPMethodsEnum.SUBSCRIBE:
                        // Odpověď 405 Method Not Allowed na SUBSCRIBE požadavky
                        _ = Task.Run(async () => {
                            SIPResponse notAllowedResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.MethodNotAllowed, null);
                            await _sipTransport.SendResponseAsync(notAllowedResponse);
                            _logger.LogDebug($"[{callId}] Sent 405 Method Not Allowed response to SUBSCRIBE request.");
                        });
                        break;

                    default:
                        // Ostatní požadavky necháme zpracovat SIPSorcery
                        _logger.LogDebug($"[{callId}] Letting SIPSorcery handle {sipRequest.Method} request.");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing {sipRequest.Method} request.");
            }

            return Task.CompletedTask;
        }
        /// <summary>
        /// Zpracuje příchozí INVITE požadavek.
        /// </summary>
        private async Task ProcessInviteAsync(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;

            // Create SIPServerUserAgent early so userAgent is non-null for session ctor
            // UASInviteTransaction uasTransaction = new UASInviteTransaction(_sipTransport, sipRequest, null);
            // _userAgent = new SIPServerUserAgent(_sipTransport, null, uasTransaction, null);

            // Create UASInviteTransaction and SIPServerUserAgent early so ACK matches.
            UASInviteTransaction uasTransaction = new UASInviteTransaction(_sipTransport, sipRequest, null);
            _userAgent = new SIPServerUserAgent(_sipTransport, null, uasTransaction, null);
            // Send immediate 100 Trying provisional response to prevent client retransmits
            _userAgent.Progress(SIPResponseStatusCodesEnum.Trying, null, null, null, null);
            // Placeholder session creation deferred until after SDP negotiation.

            var offerSdp = SDP.ParseSDPDescription(sipRequest.Body);
            var hasZeroPortOffer = offerSdp.Media.Any(m => m.Port == 0) || !offerSdp.Media.Any();
            if (hasZeroPortOffer)
            {
                int allocatedZeroRtp = _options.RtpPortMin;
                var okZero = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                okZero.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                okZero.Body = sipRequest.Body.Replace("m=audio 0", $"m=audio {allocatedZeroRtp}");
                _inviteResponseCache[callId] = okZero;
                await _sipTransport.SendResponseAsync(okZero);
                return;
            }

            // Handle retransmitted INVITE by re-sending cached 200 OK
            if (_inviteResponseCache.TryGetValue(callId, out var cachedResponse))
            {
                _logger.LogInformation($"[{callId}] Retransmitted INVITE detected, resending cached 200 OK.");
                await _sipTransport.SendResponseAsync(cachedResponse);
                return;
            }
            
            

            // Continue with existing INVITE processing (SDP negotiation, receiver binding, final session startup)
            await ProcessInviteNegotiationAsync(localSIPEndPoint, remoteEndPoint, sipRequest);
        }

        /// <summary>
        /// Handles SDP negotiation, RTP binding, and session startup for an INVITE request.
        /// </summary>
        private async Task ProcessInviteNegotiationOldAsync(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing INVITE request from {remoteEndPoint}.");

            try
            {
                // Kontrola, zda je v INVITE nabídce kodek, který podporujeme
                var offerSdp = SDP.ParseSDPDescription(sipRequest.Body);
                var hasZeroPortOffer = offerSdp.Media.Any(m => m.Port == 0) || !offerSdp.Media.Any();
                IPEndPoint dstRtpEndPoint = SDP.GetSDPRTPEndPoint(sipRequest.Body);
                if (hasZeroPortOffer)
                {
                    var (rtpClientZero, rtcpClientZero) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                    int allocatedZeroRtp = ((IPEndPoint)rtpClientZero.Client.LocalEndPoint).Port;
                    var okZero = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                    okZero.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                    okZero.Body = sipRequest.Body.Replace("m=audio 0", $"m=audio {allocatedZeroRtp}");
                    _inviteResponseCache[callId] = okZero;
                    await _sipTransport.SendResponseAsync(okZero);
                    await _portAllocator.ReleasePortsAsync(callId, rtpClientZero, rtcpClientZero);
                    return;
                }

                // Vytvoření VoIPMediaSession pro zpracování RTP
                // Použijeme konstruktor s explicitním nastavením lokální IP adresy
                List<AudioCodecsEnum> codecs = new List<AudioCodecsEnum> { AudioCodecsEnum.PCMU, AudioCodecsEnum.PCMA, AudioCodecsEnum.G722 };

                var audioSource = AudioSourcesEnum.SineWave;

                AudioExtrasSource audioExtrasSource = new AudioExtrasSource(new AudioEncoder(), new AudioSourceOptions { AudioSource = audioSource });
                audioExtrasSource.RestrictFormats(formats => codecs.Contains(formats.Codec));
                var _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
                {
                    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
                    RtpPortRange = new PortRange(
                        (_options.RtpPortMin % 2 == 0 ? _options.RtpPortMin : _options.RtpPortMin + 1),
                        _options.RtpPortMax),
                    
                });

                // Nastavíme, aby VoIPMediaSession přijímala data z jakéhokoliv zdroje
                _rtpSession.AcceptRtpFromAny = true;
                _logger.LogInformation($"[{callId}] Created VoIPMediaSession with RTP starting at port {_options.RtpPortMin}");

                // Nastavení vzdáleného popisu z nabídky
                var setResult = _rtpSession.SetRemoteDescription(SdpType.offer, offerSdp);

                if (setResult != SetDescriptionResultEnum.OK)
                {
                    // Není podporovaný kodek nebo jiný problém s SDP
                    _logger.LogWarning($"[{callId}] Failed to set remote description: {setResult}");
                    SIPResponse noMatchingCodecResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.NotAcceptableHere, setResult.ToString());
                    await _sipTransport.SendResponseAsync(noMatchingCodecResponse);
                }
                else
                {
                    _logger.LogDebug($"[{callId}] Client offer contained supported audio codec.");

                    try
                    {
                        // Vytvoření SDP odpovědi pomocí VoIPMediaSession
                        // Reserve RTP/RTCP ports before building SDP answer
                        var (tempRtp, tempRtcp) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                        int reservedRtp = ((IPEndPoint)tempRtp.Client.LocalEndPoint).Port;
                        int reservedRtcp = ((IPEndPoint)tempRtcp.Client.LocalEndPoint).Port;
                        _logger.LogInformation($"[{callId}] Reserved RTP={reservedRtp}, RTCP={reservedRtcp}");
                        // Build SDP answer and override audio media port
                        var answerSdp = _rtpSession.CreateAnswer(null);
                        foreach (var media in answerSdp.Media)
                        {
                            if (media.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase))
                            {
                                media.Port = reservedRtp;
                                break;
                            }
                        }
                        // Override session-level connection address using client remote SIP endpoint
                        var clientAddress = remoteEndPoint.Address.ToString();
                        answerSdp.Connection.ConnectionAddress = clientAddress;

                        _logger.LogInformation($"[{callId}] Created SDP answer");

                        // Log SDP details for debugging
                        _logger.LogDebug($"[{callId}] SDP answer: {answerSdp}");
                        
                        // Allocate RTP/RTCP ports for this call
                        (UdpClient rtpClient, UdpClient rtcpClient) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                        int allocatedRtp = ((IPEndPoint)rtpClient.Client.LocalEndPoint).Port;
                        int allocatedRtcp = ((IPEndPoint)rtcpClient.Client.LocalEndPoint).Port;

                        // Získáme port z SDP odpovědi

                        // Vytvoření SDP odpovědi pomocí VoIPMediaSession

                        _logger.LogInformation($"[{callId}] Created SDP answer");

                        // Log SDP details for debugging
                        _logger.LogDebug($"[{callId}] SDP answer: {answerSdp}");

                        try {
                            // Get RTP endpoints from the session - important for debugging
                            var destinationEndPoint = _rtpSession.AudioDestinationEndPoint;
                            if (destinationEndPoint != null) {
                                _logger.LogInformation($"[{callId}] RTP endpoint: {destinationEndPoint}, expecting data from {dstRtpEndPoint}");

                                // Logování více informací o SDP pro diagnostiku
                                _logger.LogInformation($"[{callId}] SDP connection info: {answerSdp.Connection}");
                                foreach (var media in answerSdp.Media)
                                {
                                    _logger.LogInformation($"[{callId}] SDP media: {media.Media} port {media.Port}");
                                }

                                // Explicitní logování rozdílu mezi endpointy
                                if (destinationEndPoint.ToString() != dstRtpEndPoint.ToString())
                                {
                                    _logger.LogWarning($"[{callId}] RTP endpoint mismatch: SIPSorcery expects {destinationEndPoint}, SDP indicates {dstRtpEndPoint}");
                                    _logger.LogInformation($"[{callId}] This is normal with NAT/port forwarding and should not cause issues as long as AcceptRtpFromAny is true");
                                }
                            }
                        } catch (Exception ex) {
                            _logger.LogWarning($"[{callId}] Failed to get RTP endpoints: {ex.Message}");
                        }

                         // Create RTP receiver with allocated ports and update SDP media port
                         _logger.LogInformation($"[{callId}] Creating RTP receiver with allocated ports: RTP={allocatedRtp}, RTCP={allocatedRtcp}");
                         var receiver = _rtpAudioReceiverFactory.CreateReceiver(callId, allocatedRtp, allocatedRtcp);
                         // Override SDP media port to allocated RTP port
                         foreach (var media in answerSdp.Media)
                         {
                             if (media.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase))
                             {
                                 media.Port = allocatedRtp;
                                 _logger.LogInformation($"[{callId}] Updated SDP media port to allocated port {allocatedRtp}");
                                 break;
                             }
                         }
                         Func<IAudioInputReceiver> inputReceiverFactory = () => receiver;
                         Func<IAudioProcessor> audioProcessorFactory = () => _audioProcessorFactory(callId);

                        // Pokud je již aktivní hovor, ukončíme ho
                        if (_userAgent?.IsHungup == false)
                        {
                            _logger.LogWarning($"[{callId}] Hanging up existing call to accept new one.");
                            _userAgent.Hangup(false);
                            _rtpSession?.Close("New call");
                            await Task.Delay(100); // Dáme čas na ukončení
                        }

                        // Vytvoření UAS transakce a SIPServerUserAgent
                        UASInviteTransaction uasTransaction = new UASInviteTransaction(_sipTransport, sipRequest, null);
                        _userAgent = new SIPServerUserAgent(_sipTransport, null, uasTransaction, null);

                        // Registrace callbacku pro zrušení hovoru
                        _userAgent.CallCancelled += async (uasAgent, cancelReq) =>
                        {
                            _logger.LogInformation($"[{callId}] Call cancelled by client.");
                            _rtpSession?.Close("Call cancelled");
                            await _sessionManager.TerminateSessionAsync(callId);
                        };

                        // Registrace callbacku pro ukončení RTP
                        _rtpSession.OnRtpClosed += (reason) => _userAgent?.Hangup(false);

                        // Vytvoření session
                        var session = await _sessionManager.CreateSessionAsync(_userAgent, sipRequest, inputReceiverFactory, audioProcessorFactory);

                        // Odeslání odpovědi 100 Trying a 180 Ringing
                        _userAgent.Progress(SIPResponseStatusCodesEnum.Trying, null, null, null, null);
                        await Task.Delay(100);
                        _userAgent.Progress(SIPResponseStatusCodesEnum.Ringing, null, null, null, null);
                        await Task.Delay(100);

                        try {
                            // Get RTP endpoints from the session - important for debugging
                            var destinationEndPoint = _rtpSession.AudioDestinationEndPoint;
                            if (destinationEndPoint != null) {
                                _logger.LogInformation($"[{callId}] RTP endpoint: {destinationEndPoint}, expecting data from {dstRtpEndPoint}");

                                // Logování více informací o SDP pro diagnostiku
                                _logger.LogInformation($"[{callId}] SDP connection info: {answerSdp.Connection}");
                                foreach (var media in answerSdp.Media)
                                {
                                    _logger.LogInformation($"[{callId}] SDP media: {media.Media} port {media.Port}");
                                }

                                // Explicitní logování rozdílu mezi endpointy
                                if (destinationEndPoint.ToString() != dstRtpEndPoint.ToString())
                                {
                                    _logger.LogWarning($"[{callId}] RTP endpoint mismatch: SIPSorcery expects {destinationEndPoint}, SDP indicates {dstRtpEndPoint}");
                                    _logger.LogInformation($"[{callId}] This is normal with NAT/port forwarding and should not cause issues as long as AcceptRtpFromAny is true");
                                }
                            }
                        } catch (Exception ex) {
                            _logger.LogWarning($"[{callId}] Failed to get RTP endpoints: {ex.Message}");
                        }

                        // Odeslání odpovědi 200 OK s SDP
                        var sdpString = answerSdp.ToString().Replace("c=IN IP4 0.0.0.0", $"c=IN IP4 {clientAddress}");
                        _userAgent.Answer(SDP.SDP_MIME_CONTENTTYPE, sdpString, null, SIPDialogueTransferModesEnum.NotAllowed);
                        var okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                        okResponse.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                        okResponse.Body = sdpString;
                        _inviteResponseCache[callId] = okResponse;
                        await _sipTransport.SendResponseAsync(okResponse);
                        await _portAllocator.ReleasePortsAsync(callId, rtpClient, rtcpClient);

                        // Spuštění session
                        await session.StartAsync(CancellationToken.None);
                        await _rtpSession.Start();

                        // Log connection details after starting
                        _logger.LogInformation($"[{callId}] SIP and RTP sessions started successfully");
                        _logger.LogInformation($"[{callId}] Call from {sipRequest.Header.From.FromURI.User} established");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{callId}] Error processing incoming call.");

                        // Odeslání chybové odpovědi
                        SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                        await _sipTransport.SendResponseAsync(errorResponse);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing INVITE request.");

                // Odeslání chybové odpovědi
                SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                await _sipTransport.SendResponseAsync(errorResponse);
            }
        }
/// <summary>
        /// Handles SDP negotiation, RTP binding, and session startup for an INVITE request.
        /// </summary>
        private async Task ProcessInviteNegotiationAsync(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing INVITE request from {remoteEndPoint}.");

            try
            {
                // Parse incoming SDP offer
                var offerSdp = SDP.ParseSDPDescription(sipRequest.Body);
                var hasZeroPortOffer = offerSdp.Media.Any(m => m.Port == 0) || !offerSdp.Media.Any();
                if (hasZeroPortOffer)
                {
                    var (rtp0, rtcp0) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                    int zeroPort = ((IPEndPoint)rtp0.Client.LocalEndPoint).Port;
                    var okZero = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                    okZero.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                    okZero.Body = sipRequest.Body.Replace("m=audio 0", $"m=audio {zeroPort}");
                    _inviteResponseCache[callId] = okZero;
                    await _sipTransport.SendResponseAsync(okZero);
                    await _portAllocator.ReleasePortsAsync(callId, rtp0, rtcp0);
                    return;
                }

                // Configure media session
                var codecs = new List<AudioCodecsEnum> { AudioCodecsEnum.PCMU, AudioCodecsEnum.PCMA, AudioCodecsEnum.G722 };
                var audioSource = new AudioExtrasSource(
                    new AudioEncoder(),
                    new AudioSourceOptions { AudioSource = AudioSourcesEnum.SineWave });
                audioSource.RestrictFormats(f => codecs.Contains(f.Codec));

                UdpClient rtpClient;
                UdpClient rtcpClient;
                int rtpPort, rtcpPort;
                try
                {
                    (rtpClient, rtcpClient) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                    rtpPort = ((IPEndPoint)rtpClient.Client.LocalEndPoint).Port;
                    rtcpPort = ((IPEndPoint)rtcpClient.Client.LocalEndPoint).Port;
                    _logger.LogInformation($"[{callId}] Reserved RTP={rtpPort}, RTCP={rtcpPort}");
                }
                catch (Exception ex) when (ex is InvalidOperationException || ex is SocketException)
                {
                    _logger.LogError(ex, $"[{callId}] Port allocation failed: {ex.Message}");
                    var errorResponse = SIPResponse.GetResponse(
                        sipRequest,
                        SIPResponseStatusCodesEnum.NotAcceptableHere,
                        "Failed to allocate RTP ports");
                    await _sipTransport.SendResponseAsync(errorResponse);
                    return;
                }
                _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
                {
                    MediaEndPoint = new MediaEndPoints { AudioSource = audioSource }
                });
                _rtpSession.AcceptRtpFromAny = true;

                var setResult = _rtpSession.SetRemoteDescription(SdpType.offer, offerSdp);
                if (setResult != SetDescriptionResultEnum.OK)
                {
                    var reject = SIPResponse.GetResponse(
                        sipRequest,
                        SIPResponseStatusCodesEnum.NotAcceptableHere,
                        setResult.ToString());
                    await _sipTransport.SendResponseAsync(reject);
                    return;
                }


                // Build and override SDP answer
                var answerSdp = _rtpSession.CreateAnswer(null);
                var audioMedia = answerSdp.Media.First(m =>
                    m.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase));
                audioMedia.Port = rtpPort;
                answerSdp.Connection.ConnectionAddress = remoteEndPoint.Address.ToString();

                // Configure UDP buffers
                rtpClient.Client.ReceiveBufferSize = rtcpClient.Client.ReceiveBufferSize = 1048576;
                rtpClient.Client.SendBufferSize    = rtcpClient.Client.SendBufferSize    = 1048576;

                // Create receiver
                var receiver = new RtpAudioReceiver(
                    callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());

                if (receiver.RtpLocalEndPoint.Port != rtpPort)
                {
                    audioMedia.Port = receiver.RtpLocalEndPoint.Port;
                    _logger.LogInformation($"[{callId}] Adjusted SDP port to {receiver.RtpLocalEndPoint.Port}");
                }

                // Tear down existing
                if (_userAgent?.IsHungup == false)
                {
                    _userAgent.Hangup(false);
                    _rtpSession.Close("New call");
                    await Task.Delay(100);
                }

                // Setup UAS and callbacks
                // Reuse original _userAgent created earlier; no new transaction needed.
                _userAgent.CallCancelled += async (_, __) =>
                {
                    _rtpSession.Close("Cancelled");
                    await _sessionManager.TerminateSessionAsync(callId);
                };
                _rtpSession.OnRtpClosed += _ => _userAgent?.Hangup(false);

                // Create session
                var session = await _sessionManager.CreateSessionAsync(
                    _userAgent, sipRequest,
                    () => receiver,
                    () => _audioProcessorFactory(callId));

                // 100 Trying: moved up to immediately after server transaction creation in ProcessInviteAsync to suppress client retransmits early
                // Delay (100 ms)—removed, it only spaced out messages
                
                // 180 Ringing: informs caller that call is being processed
                _userAgent.Progress(SIPResponseStatusCodesEnum.Ringing, null, null, null, null);
                // Delay (100 ms) before sending final 200 OK—removed to speed setup

                // Send final 200 OK
                var clientAddress = remoteEndPoint.Address.ToString();
                                var sdpString = answerSdp.ToString().Replace("c=IN IP4 0.0.0.0", $"c=IN IP4 {clientAddress}");
                                _userAgent.Answer(SDP.SDP_MIME_CONTENTTYPE, sdpString, null, SIPDialogueTransferModesEnum.NotAllowed);
                                var okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                                okResponse.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                                okResponse.Body = sdpString;
                                _inviteResponseCache[callId] = okResponse;
                                await _sipTransport.SendResponseAsync(okResponse);

                await session.StartAsync(CancellationToken.None);
                await _rtpSession.Start();

                _logger.LogInformation($"[{callId}] Call established.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error in negotiation.");
                var error = SIPResponse.GetResponse(
                    sipRequest,
                    SIPResponseStatusCodesEnum.InternalServerError,
                    "Internal Server Error");
                await _sipTransport.SendResponseAsync(error);
            }
        }





        /// <summary>
        /// Zpracuje příchozí BYE požadavek.
        /// </summary>
        private async Task ProcessByeAsync(SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing BYE request.");

            try
            {
                // Odeslání odpovědi 200 OK
                SIPResponse okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, "OK");
                await _sipTransport.SendResponseAsync(okResponse);

                // Ukončení hovoru
                _userAgent?.Hangup(true);
                _rtpSession?.Close("BYE received");

                // Získání session
                var session = _sessionManager.GetSession(callId);
                if (session != null)
                {
                    // Release RTP and RTCP ports
                    if (session.AudioReceiver is RtpAudioReceiver rtpReceiver)
                    {
                        await _portAllocator.ReleasePortsAsync(callId, rtpReceiver.RtpClient, rtpReceiver.RtcpClient);
                    }
                    // Ukončení session
                    await _sessionManager.TerminateSessionAsync(callId);
                    _logger.LogInformation($"[{callId}] Call terminated by BYE request.");
                    // Remove cached OK response for this call
                    _inviteResponseCache.TryRemove(callId, out _);
                    // Clear user agent and RTP session for next call
                    _userAgent = null;
                    _rtpSession = null;
                }
                else
                {
                    _logger.LogWarning($"[{callId}] Session not found for BYE request.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing BYE request.");

                // Odeslání chybové odpovědi
                SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                await _sipTransport.SendResponseAsync(errorResponse);
            }
        }

        /// <summary>
        /// Zpracuje příchozí CANCEL požadavek.
        /// </summary>
        private async Task ProcessCancelAsync(SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing CANCEL request.");

            try
            {
                // Odeslání odpovědi 200 OK
                SIPResponse okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, "OK");
                await _sipTransport.SendResponseAsync(okResponse);

                // Ukončení hovoru
                _userAgent?.Hangup(true);
                _rtpSession?.Close("CANCEL received");

                // Získání session
                var session = _sessionManager.GetSession(callId);
                if (session != null)
                {
                    // Release RTP and RTCP ports
                    if (session.AudioReceiver is RtpAudioReceiver rtpReceiver)
                    {
                        await _portAllocator.ReleasePortsAsync(callId, rtpReceiver.RtpClient, rtpReceiver.RtcpClient);
                    }
                    // Ukončení session
                    await _sessionManager.TerminateSessionAsync(callId);
                    _logger.LogInformation($"[{callId}] Call terminated by CANCEL request.");
                    // Remove cached OK response for this call
                    _inviteResponseCache.TryRemove(callId, out _);
                    // Clear user agent and RTP session for next call
                    _userAgent = null;
                    _rtpSession = null;
                }
                else
                {
                    _logger.LogWarning($"[{callId}] Session not found for CANCEL request.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing CANCEL request.");

                // Odeslání chybové odpovědi
                SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                await _sipTransport.SendResponseAsync(errorResponse);
            }
        }
    }
}
