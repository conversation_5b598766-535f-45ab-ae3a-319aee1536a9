using System;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;


var builder = WebApplication.CreateBuilder(args);

// Standard ASP.NET Core environment variable support is automatically enabled

// Configure ASP.NET Core logging with compact console format
builder.Logging.ClearProviders();
builder.Logging.AddSimpleConsole();

// Add services to the container.
builder.Services.AddOpenApi();
builder.Services.AddControllers();
builder.Services.AddLogging();

// Configuration
builder.Services.Configure<SipServerOptions>(
    builder.Configuration.GetSection("SipServer"));
builder.Services.Configure<PhonexiaOptions>(
    builder.Configuration.GetSection("Phonexia"));
builder.Services.Configure<WavRecordingOptions>(
    builder.Configuration.GetSection("WavRecording"));
builder.Services.Configure<RtpReceiverOptions>(
    builder.Configuration.GetSection("RtpReceiver"));

// Core services
builder.Services.AddSingleton<ICallSessionManager>(sp =>
{
    var logger = sp.GetRequiredService<ILogger<CallSessionManager>>();
    var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
    return new CallSessionManager(logger, loggerFactory);
});

// Port allocator and RTP receiver factory
builder.Services.AddSingleton<IPortAllocator, PortAllocator>();
builder.Services.AddSingleton<IRtpAudioReceiverFactory, RtpAudioReceiverFactory>();

// SIP registration manager
builder.Services.AddSingleton<ISipRegistrationManager, SipRegistrationManager>();

// Call synchronization service for per-Call-ID locking
builder.Services.AddSingleton<ICallSynchronizationService, CallSynchronizationService>();

// Audio processor factories
builder.Services.AddSingleton<WavAudioProcessorFactory>();
builder.Services.AddSingleton<PhonexiaSttProcessorFactory>();
builder.Services.AddSingleton<CompositeAudioProcessorFactory>();

// Register enhanced audio processor factory that composes STT + optional WAV
builder.Services.AddTransient<Func<string, string, string, IAudioProcessor>>(sp =>
{
    var compositeFactory = sp.GetRequiredService<CompositeAudioProcessorFactory>();
    return (callId, callerParty, calledParty) => compositeFactory.CreateProcessor(callId, callerParty, calledParty);
});

// Register backward-compatible audio processor factory (STT-only)
builder.Services.AddTransient<Func<string, IAudioProcessor>>(sp =>
{
    var sttFactory = sp.GetRequiredService<PhonexiaSttProcessorFactory>();
    return callId => sttFactory.CreateProcessor(callId);
});

// Register audio input receiver factory delegate
builder.Services.AddSingleton<Func<string, int, int, IAudioInputReceiver>>(sp =>
{
    var rtpFactory = sp.GetRequiredService<IRtpAudioReceiverFactory>();
    return (callId, rtpPort, rtcpPort) => rtpFactory.CreateReceiver(callId, rtpPort, rtcpPort);
});
// Register SIP server as hosted service
builder.Services.AddHostedService<SipServerService>();

// HttpClient for Phonexia API
builder.Services.AddHttpClient();
builder.Services.AddHttpClient("PhonexiaApi");

// WavFileStreamers
//builder.Services.AddScoped<WavFileStreamer3>();

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseHttpsRedirection();
}

app.MapControllers();

var summaries = new[]
{
    "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
};

app.MapGet("/weatherforecast", () =>
{
    var forecast = Enumerable.Range(1, 5).Select(index =>
        new WeatherForecast(
            DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            Random.Shared.Next(-20, 55),
            summaries[Random.Shared.Next(summaries.Length)]
        ))
        .ToArray();
    return forecast;
})
.WithName("GetWeatherForecast");

// Get logger for startup messages
var logger = app.Services.GetRequiredService<ILogger<Program>>();

logger.LogInformation("=== Voice Processing Service Starting ===");
logger.LogInformation("Environment: {Environment}", app.Environment.EnvironmentName);
logger.LogInformation("SIP Listen Port: {SipPort}", app.Configuration["SipServer:ListenPort"]);
logger.LogInformation("HTTP API Port: {HttpPort}", app.Configuration["ASPNETCORE_URLS"]);
logger.LogInformation("Phonexia API URL: {PhonexiaUrl}", app.Configuration["Phonexia:ApiUrl"]);

logger.LogInformation("=== Voice Processing Service Started Successfully ===");

app.Run();

record WeatherForecast(DateOnly Date, int TemperatureC, string? Summary)
{
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
}
