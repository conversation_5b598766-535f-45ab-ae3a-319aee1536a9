{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "SIPSorcery": "Information", "voice_processing_service": "Debug"}, "Console": {"FormatterName": "simple", "FormatterOptions": {"SingleLine": false, "IncludeScopes": false, "TimestampFormat": "[HH:mm:ss] ", "UseUtcTimestamp": false}}}, "AllowedHosts": "*", "SipServer": {"ListenIpAddress": "Any", "ListenPort": 5090, "RtpPortMin": 10000, "RtpPortMax": 19998, "WavRecordingDirectory": "RecordedCalls", "DefaultRegistrationExpiry": 3600, "MaxRegistrationExpiry": 7200, "MinRegistrationExpiry": 60, "AllowedRegistrationEndpoints": ["17999"], "OverrideSdpConnectionAddress": ""}, "Phonexia": {"ApiUrl": "http://localhost:8600", "Username": "admin", "Password": "phonexia", "ApiKey": "your-api-key-here", "Language": "cs-CZ", "Model": "CS_CZ_O2_6", "ChunkSizeBytes": 8000, "SessionPoolSize": 0}, "WavRecording": {"Enabled": false, "Directory": "/app/recordings", "SplitPerDirection": true, "SplitOnReinvite": true, "MaxFileDurationSeconds": 3600, "FilenameTemplate": "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav", "QueueCapacityFrames": 200, "SafeFinalize": true, "IncludeNumbersInFilename": false}, "RtpReceiver": {"InactivityTimeoutMs": 5000}}