# Repository Properties

This repository was created using the [GitOps](https://network.git.cz.o2/ntwcl/gitopsntw/) CI/CD Pipeline [gitlab-ci-repository.yml](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/templates/cicd/gitlab-ci-repository.yml). Following things were deployed for your convenience:

# Repository Tokens

There were two GitLab Tokens created:
1. Project **Access Token**
2. Project **Deploy Token**

Documentation and implications of these tokens is available here: [GitOps Deployment Repository Tokens](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/DEPLOYMENT_TOKENS.md) 

# Repository Structure

This repository was (originally) deployed sporting following structure:

``` text
root
  |___.gitops/
  |___charts/
  |___docs/
  |___.gitignore
  |___.gitlab-ci.yml
  |___README.md
```

## Folders 

| filename  | description                                                                                                                                                                                                                    |
|-----------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `.gitops` | This is where all your K8s YAML configuration files are stored: handle with care                                                                                                                                               |
| `charts`  | This is where Helm Charts of your Main Application and [NGINX Demo Application](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/NGINX_DEMO_APPLICATION.md) and [Helm Starters](/docs/HELM_STARTERS.md)  are stored |
| `docs`    | This is where Documentation of your project should reside                                                                                                                                                                      |


## Files

| filename         | description                                                                                                                                                                                                                                                     |
|------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `.gitignore`     | Generic git-ignore file for your repository, sourced from [gitops/templates](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/templates/repository/.gitignore)                                                                                             |
| `.gitlab-ci.yml` | GitLab CI/CD configuration for managing your project into the main [GitOps NTW](https://network.git.cz.o2/ntwcl/gitopsntw/) Project, sourced from [gitops/templates](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/templates/repository/.gitlab-ci.yml) |
| `README.md`      | Starter (templated) README file for your GitLab project, sourced from [gitops/templates](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/templates/repository/README.md)                                                                                  |

