using System;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Interfaces;
using voice_processing_service.Configuration;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace přijímače audio dat z RTP paketů.
    /// </summary>
    public class RtpAudioReceiver : IAudioInputReceiver
    {
        public event Action<byte[]> AudioFrameReceived;
        private readonly ILogger<RtpAudioReceiver> _logger;
        private readonly UdpClient _rtpClient;
        private readonly UdpClient _rtcpClient;
        private readonly string _callId; // Pro logování
        private readonly RtpReceiverOptions _opts;
        private bool _disposed = false;

        /// <inheritdoc/>
        public IPEndPoint RtpLocalEndPoint { get; }

        /// <inheritdoc/>
        public IPEndPoint RtcpLocalEndPoint { get; }
/// <summary>
        /// Exposes the RTP UDP client for port release.
        /// </summary>
        public UdpClient RtpClient => _rtpClient;

        /// <summary>
        /// Exposes the RTCP UDP client for port release.
        /// </summary>
        public UdpClient RtcpClient => _rtcpClient;

        /// <summary>
        /// Získá port, na kterém přijímač naslouchá pro RTP pakety.
        /// </summary>
        public int RtpPort => RtpLocalEndPoint?.Port ?? 0;

        /// <summary>
        /// Získá port, na kterém přijímač naslouchá pro RTCP pakety.
        /// </summary>
        public int RtcpPort => RtcpLocalEndPoint?.Port ?? 0;

        /// <summary>
        /// Inicializuje novou instanci třídy <see cref="RtpAudioReceiver"/>.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru pro logování.</param>
        /// <param name="rtpClient">UDP klient pro příjem RTP paketů.</param>
        /// <param name="rtcpClient">UDP klient pro příjem RTCP paketů.</param>
        /// <param name="logger">Logger pro logování událostí.</param>
        public RtpAudioReceiver(string callId, UdpClient rtpClient, UdpClient rtcpClient, IOptions<RtpReceiverOptions> opts, ILogger<RtpAudioReceiver> logger)
        {
            _callId = callId ?? throw new ArgumentNullException(nameof(callId));
            _rtpClient = rtpClient ?? throw new ArgumentNullException(nameof(rtpClient));
            _rtcpClient = rtcpClient ?? throw new ArgumentNullException(nameof(rtcpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            if (opts == null) throw new ArgumentNullException(nameof(opts));
            _opts = opts.Value ?? new RtpReceiverOptions();

            RtpLocalEndPoint = (IPEndPoint)_rtpClient.Client.LocalEndPoint!;
            RtcpLocalEndPoint = (IPEndPoint)_rtcpClient.Client.LocalEndPoint!;

            _logger.LogInformation($"[{_callId}] RtpAudioReceiver created for {RtpLocalEndPoint} (RTCP: {RtcpLocalEndPoint}).");
            _logger.LogInformation($"[{_callId}] UDP buffer sizes - RTP Receive: {_rtpClient.Client.ReceiveBufferSize}, RTP Send: {_rtpClient.Client.SendBufferSize}");
        }

        // Backward-compatible constructor: uses default options (app-level config not available here)
        public RtpAudioReceiver(string callId, UdpClient rtpClient, UdpClient rtcpClient, ILogger<RtpAudioReceiver> logger)
            : this(callId, rtpClient, rtcpClient, Options.Create(new RtpReceiverOptions()), logger)
        {
        }

        /// <inheritdoc/>
        public async Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(RtpAudioReceiver));
            }

            _logger.LogInformation($"[{_callId}] Starting RTP/RTCP listeners...");
            _logger.LogInformation($"[{_callId}] RTP Listening on {RtpLocalEndPoint}, RTCP on {RtcpLocalEndPoint}");
            _logger.LogInformation($"[{_callId}] Starting RTP receiver with inactivity timeout {Math.Max(0, _opts.InactivityTimeoutMs)}ms");

            // Configure UDP client to increase buffer size
            _rtpClient.Client.ReceiveBufferSize = 1048576; // 1MB buffer
            _logger.LogInformation($"[{_callId}] UDP receive buffer size set to: {_rtpClient.Client.ReceiveBufferSize} bytes");

            var rtpTask = Task.Run(() => ListenRtpLoopAsync(buffer, cancellationToken), cancellationToken);
            var rtcpTask = Task.Run(() => ListenRtcpLoopAsync(cancellationToken), cancellationToken);

            try
            {
                await Task.WhenAll(rtpTask, rtcpTask);
                _logger.LogInformation($"[{_callId}] RTP/RTCP listeners completed.");
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] RTP/RTCP listeners cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error in RTP/RTCP listeners.");
            }
            finally
            {
                buffer.CompleteAdding();
            }
        }

        private async Task ListenRtpLoopAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{_callId}] RTP listener started on {RtpLocalEndPoint}.");

            try
            {
                int packetCount = 0;
                int totalBytesProcessed = 0;
                DateTime lastLogTime = DateTime.Now;
                DateTime lastPacketTime = DateTime.Now;
                bool receivedFirstPacket = false;
                int noPacketWarningCount = 0;

                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // Use a short timeout for packet receive to allow periodic inactivity checks
                        using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                        timeoutCts.CancelAfter(1000); // 1 second timeout for individual packet receive

                        try
                            {
                                // Try to receive a packet with short timeout
                                UdpReceiveResult result;
                                try
                                {
                                    result = await _rtpClient.ReceiveAsync(timeoutCts.Token);
                                    _logger.LogTrace($"[{_callId}] UDP packet received from {result.RemoteEndPoint}, size: {result.Buffer.Length} bytes");

                                    // Update last packet time when we receive a packet
                                    lastPacketTime = DateTime.Now;
                                }
                                catch (OperationCanceledException)
                                {
                                    // Check if we've exceeded the inactivity timeout
                                    var inactivityTimeoutMs = Math.Max(0, _opts.InactivityTimeoutMs);
                                    var inactiveForMs = (int)(DateTime.Now - lastPacketTime).TotalMilliseconds;

                                    if (inactiveForMs >= inactivityTimeoutMs && inactivityTimeoutMs > 0)
                                    {
                                        _logger.LogInformation($"[{_callId}] RTP inactivity timeout reached after {inactiveForMs}ms (threshold {inactivityTimeoutMs}ms).");
                                        buffer.CompleteAdding();
                                        break;
                                    }

                                    // Continue the loop to check for more packets
                                    continue;
                                }
                                catch (ObjectDisposedException)
                                {
                                    _logger.LogInformation($"[{_callId}] RTP client disposed, exiting RTP loop.");
                                    break;
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, $"[{_callId}] Error receiving RTP packet");
                                    continue;
                                }
                                var rtpData = result.Buffer;
                            _logger.LogTrace($"[{_callId}] Received RTP payload {rtpData.Length} bytes, starting parse");

                            // Aktualizace času posledního paketu
                            lastPacketTime = DateTime.Now;
                            noPacketWarningCount = 0; // Reset počítadla varování

                            if (!receivedFirstPacket)
                            {
                                receivedFirstPacket = true;
                                _logger.LogInformation($"[{_callId}] Received first RTP packet from {result.RemoteEndPoint}");
                            }

                            if (rtpData.Length > 12) // Minimální velikost RTP hlavičky
                            {
                                // Extrakce sequence number a timestamp z RTP hlavičky
                                ushort sequenceNumber = (ushort)((rtpData[2] << 8) | rtpData[3]);
                                uint timestamp = (uint)((rtpData[4] << 24) | (rtpData[5] << 16) | (rtpData[6] << 8) | rtpData[7]);
                                byte payloadType = (byte)(rtpData[1] & 0x7F);
                                bool marker = (rtpData[1] & 0x80) != 0;

                                // Kontrola payload type - přijímáme pouze PCMU (0) a PCMA (8)
                                if (payloadType != 0 && payloadType != 8)
                                {
                                    _logger.LogTrace($"[{_callId}] Ignoring RTP packet with unsupported payload type {payloadType} from {result.RemoteEndPoint}.");
                                    continue;
                                }

                                // Extrakce audio dat z RTP paketu (přeskočení 12 bytů RTP hlavičky)
                                _logger.LogTrace($"[{_callId}] Extracting RTP payload of length {rtpData.Length - 12} bytes");
                                var audioData = new byte[rtpData.Length - 12];
                                Array.Copy(rtpData, 12, audioData, 0, audioData.Length);
                                _logger.LogTrace($"[{_callId}] Extracted audio payload length {audioData.Length} bytes");

                                // Přidání audio dat do bufferu a explicitně logujeme úspěšnost této operace
                                AudioFrameReceived?.Invoke(audioData);
                                //buffer.Add(audioData);
                                int bufferId = buffer.GetHashCode();
                                try
                                {
                                    _logger.LogTrace($"[{_callId}] Buffer instance {bufferId}: added {audioData.Length} bytes");
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, $"[{_callId}] Error adding {audioData.Length} bytes to buffer instance {bufferId}");
                                }
                                totalBytesProcessed += audioData.Length;

                                _logger.LogTrace($"[{_callId}] Added {audioData.Length} bytes to audio buffer. Total bytes in session: {totalBytesProcessed}");

                                packetCount++;

                                // Logování každou sekundu nebo po přijetí prvního paketu
                                if (packetCount == 1 || (DateTime.Now - lastLogTime).TotalSeconds >= 1)
                                {
                                    _logger.LogInformation($"[{_callId}] Received RTP packet #{packetCount} from {result.RemoteEndPoint}, seq: {sequenceNumber}, timestamp: {timestamp}, pt: {payloadType}, marker: {marker}, size: {rtpData.Length} bytes");
                                    lastLogTime = DateTime.Now;

                                    // Logování prvních několika bytů audio dat pro diagnostiku
                                    if (audioData.Length > 0)
                                    {
                                        int bytesToLog = Math.Min(16, audioData.Length);
                                        var hexBytes = BitConverter.ToString(audioData, 0, bytesToLog).Replace("-", " ");
                                        _logger.LogDebug($"[{_callId}] Audio data sample: {hexBytes}...");
                                    }
                                }
                                else
                                {
                                    _logger.LogTrace($"[{_callId}] Received {audioData.Length} bytes of audio data from {result.RemoteEndPoint}, seq: {sequenceNumber}.");
                                }
                            }
                            else
                            {
                                _logger.LogWarning($"[{_callId}] Received too small RTP packet ({rtpData.Length} bytes) from {result.RemoteEndPoint}.");
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            // Timeout - žádný paket nepřišel v časovém limitu
                            if (receivedFirstPacket && (DateTime.Now - lastPacketTime).TotalSeconds > 3)
                            {
                                noPacketWarningCount++;
                                if (noPacketWarningCount % 5 == 1) // Logujeme jen každých 5 sekund, aby se log nezaplnil
                                {
                                    _logger.LogWarning($"[{_callId}] No RTP packets received for {(int)(DateTime.Now - lastPacketTime).TotalSeconds} seconds.");
                                }
                            }
                        }
                    }
                    catch (SocketException ex)
                    {
                        _logger.LogWarning($"[{_callId}] Socket exception in RTP listener: {ex.Message}");
                        // Krátká pauza před dalším pokusem
                        await Task.Delay(100, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        throw; // Znovu vyhodit pro zpracování ve vnějším catch bloku
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{_callId}] Error processing RTP packet.");
                        // Krátká pauza před dalším pokusem
                        await Task.Delay(100, cancellationToken);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] RTP listener cancelled.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error in RTP listener.");
            }
            finally
            {
                _logger.LogInformation($"[{_callId}] RTP listener stopped (configured inactivity timeout {_opts.InactivityTimeoutMs}ms).");
            }
        }

        private async Task ListenRtcpLoopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{_callId}] RTCP listener started on {RtcpLocalEndPoint}.");
        
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    UdpReceiveResult result;
                    try
                    {
                        result = await _rtcpClient.ReceiveAsync(cancellationToken);
                        _logger.LogTrace($"[{_callId}] Received {result.Buffer.Length} bytes of RTCP data from {result.RemoteEndPoint}.");
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.LogInformation($"[{_callId}] RTCP listener cancelled.");
                        break;
                    }
                    catch (ObjectDisposedException)
                    {
                        _logger.LogInformation($"[{_callId}] RTCP client disposed, exiting RTCP loop.");
                        break;
                    }
                    catch (SocketException ex) when (ex.ErrorCode == 995)
                    {
                        _logger.LogInformation($"[{_callId}] RTCP receive aborted: {ex.Message}");
                        break;
                    }
                    catch (SocketException ex)
                    {
                        _logger.LogWarning($"[{_callId}] RTCP socket exception: {ex.Message}");
                        continue;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{_callId}] Error receiving RTCP packet.");
                        continue;
                    }
                    // RTCP data nejsou zpracovávána, pouze logována
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] RTCP listener cancelled.");
            }
            catch (ObjectDisposedException)
            {
                _logger.LogInformation($"[{_callId}] RTCP listener encountered disposed client, exiting.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error in RTCP listener.");
            }
            finally
            {
                _logger.LogInformation($"[{_callId}] RTCP listener stopped.");
            }
        }

        /// <inheritdoc/>
        public async Task UpdateConfigurationAsync(IPEndPoint newRemoteEndpoint, string[] supportedCodecs)
        {
            _logger.LogInformation($"[{_callId}] Updating RTP receiver configuration");
            _logger.LogInformation($"[{_callId}] New remote endpoint: {newRemoteEndpoint}");
            _logger.LogInformation($"[{_callId}] Supported codecs: {string.Join(", ", supportedCodecs ?? new string[0])}");

            // For the RTP receiver, we don't need to change anything since:
            // 1. We're still listening on the same local ports (reusing existing allocation)
            // 2. We accept RTP from any source (AcceptRtpFromAny pattern)
            // 3. Codec filtering happens at the RTP packet level in the existing logic
            
            // The key insight: The RTP receiver continues to work without changes
            // because it's designed to accept packets from any remote endpoint
            // and filter by payload type (codec) in the packet processing loop
            
            _logger.LogInformation($"[{_callId}] RTP receiver configuration updated successfully - no restart required");
            
            // Return completed task
            await Task.CompletedTask;
        }

        /// <inheritdoc/>
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            _logger.LogInformation($"[{_callId}] Disposing RtpAudioReceiver for {RtpLocalEndPoint}.");

            try
            {
                _rtpClient?.Close();
                _rtpClient?.Dispose();
                _rtcpClient?.Close();
                _rtcpClient?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error disposing RtpAudioReceiver.");
            }

            _disposed = true;
            GC.SuppressFinalize(this);
        }
    }
}
