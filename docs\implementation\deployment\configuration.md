# Configuration Guide

This guide provides detailed information about configuring the CCCZ Buddy Realtime Phonexia Server for optimal performance and functionality.

## Configuration File

The primary configuration file is `appsettings.json`, located in the root directory of the application. This file contains all the settings needed to configure the server.

## SIP Server Configuration

The SIP server configuration is defined in the `SipServer` section of the configuration file:

```json
"SipServer": {
  "ListenIpAddress": "Any",
  "ListenPort": 5060,
  "RtpPortMin": 10000,
  "RtpPortMax": 19998,
  "WavRecordingDirectory": "RecordedCalls"
}
```

### Settings

| Setting | Description | Default | Recommended |
|---------|-------------|---------|------------|
| `ListenIpAddress` | IP address to listen on. Use "Any" to listen on all interfaces, or specify a specific IP address. | "Any" | Use specific IP in production |
| `ListenPort` | Port to listen for SIP traffic. | 5060 | 5060 (standard SIP port) |
| `RtpPortMin` | Minimum port in the RTP port range. | 10000 | 10000 |
| `RtpPortMax` | Maximum port in the RTP port range. | 19998 | 19998 |
| `WavRecordingDirectory` | Directory where WAV recordings will be stored. | "RecordedCalls" | Absolute path to storage directory |

### RTP Port Range Calculation

The number of concurrent calls the server can handle is limited by the RTP port range. Each call requires 2 ports (RTP and RTCP), so the maximum number of concurrent calls can be calculated as:

```
Max Concurrent Calls = (RtpPortMax - RtpPortMin + 1) / 2
```

For the default configuration (10000-19998), this allows for approximately 5,000 concurrent calls.

## Phonexia STT Configuration

The Phonexia speech-to-text configuration is defined in the `Phonexia` section of the configuration file:

```json
"Phonexia": {
  "ApiUrl": "https://api.phonexia.com/v1",
  "ApiKey": "your-api-key-here",
  "Language": "cs-CZ",
  "Model": "default",
  "ChunkSizeBytes": 8000
}
```

### Settings

| Setting | Description | Default | Recommended |
|---------|-------------|---------|------------|
| `ApiUrl` | URL of the Phonexia API. | "https://api.phonexia.com/v1" | Use provided URL |
| `ApiKey` | Your Phonexia API key. | - | Valid API key |
| `Language` | Language model to use for STT. | "cs-CZ" | Match your use case |
| `Model` | Model name to use for STT. | "default" | Match your use case |
| `ChunkSizeBytes` | Size of audio chunks sent to Phonexia API. | 8000 | 8000 (1 second of audio at 8kHz) |

## Logging Configuration

The logging configuration is defined in the `Logging` section of the configuration file:

```json
"Logging": {
  "LogLevel": {
    "Default": "Information",
    "Microsoft": "Warning",
    "Microsoft.Hosting.Lifetime": "Information"
  },
  "Console": {
    "IncludeScopes": true
  },
  "File": {
    "Path": "logs/log-.txt",
    "Append": true,
    "FileSizeLimitBytes": 10485760,
    "MaxRollingFiles": 10
  }
}
```

### Settings

| Setting | Description | Default | Recommended |
|---------|-------------|---------|------------|
| `LogLevel:Default` | Default log level for all categories. | "Information" | "Information" for normal operation, "Debug" for troubleshooting |
| `LogLevel:Microsoft` | Log level for Microsoft namespaces. | "Warning" | "Warning" |
| `LogLevel:Microsoft.Hosting.Lifetime` | Log level for hosting lifetime events. | "Information" | "Information" |
| `Console:IncludeScopes` | Whether to include scopes in console logs. | true | true |
| `File:Path` | Path to log file. | "logs/log-.txt" | Absolute path with date format |
| `File:Append` | Whether to append to existing log file. | true | true |
| `File:FileSizeLimitBytes` | Maximum size of log file before rolling. | 10485760 (10MB) | 10-50MB |
| `File:MaxRollingFiles` | Maximum number of rolling log files to keep. | 10 | 10-30 |

## Advanced Configuration

### Kafka Integration

If you want to send transcription results to Kafka, add the following section to the configuration file:

```json
"Kafka": {
  "BootstrapServers": "kafka1:9092,kafka2:9092",
  "Topic": "transcription-results",
  "ClientId": "ccczbuddy-phonexia",
  "SecurityProtocol": "Plaintext"
}
```

### Settings

| Setting | Description | Default | Recommended |
|---------|-------------|---------|------------|
| `BootstrapServers` | Comma-separated list of Kafka bootstrap servers. | - | Valid Kafka servers |
| `Topic` | Kafka topic to publish transcription results to. | - | Valid topic name |
| `ClientId` | Client ID to use when connecting to Kafka. | - | Unique identifier |
| `SecurityProtocol` | Security protocol to use when connecting to Kafka. | "Plaintext" | "Ssl" for production |

### Performance Tuning

For high-load environments, add the following section to the configuration file:

```json
"Performance": {
  "MaxConcurrentCalls": 100,
  "AudioBufferSize": 1000,
  "ProcessorThreads": 4
}
```

### Settings

| Setting | Description | Default | Recommended |
|---------|-------------|---------|------------|
| `MaxConcurrentCalls` | Maximum number of concurrent calls to accept. | 0 (unlimited) | Based on server capacity |
| `AudioBufferSize` | Size of the audio buffer in number of chunks. | 1000 | 1000-5000 |
| `ProcessorThreads` | Number of threads to use for audio processing. | 0 (auto) | Number of CPU cores |

## Environment-Specific Configuration

ASP.NET Core supports environment-specific configuration files. You can create environment-specific configuration files using the naming convention `appsettings.{Environment}.json`, where `{Environment}` is the value of the `ASPNETCORE_ENVIRONMENT` environment variable.

For example:
- `appsettings.Development.json` - Used when `ASPNETCORE_ENVIRONMENT` is set to `Development`
- `appsettings.Production.json` - Used when `ASPNETCORE_ENVIRONMENT` is set to `Production`

Settings in environment-specific files override settings in the base `appsettings.json` file.

## Configuration Validation

The application validates the configuration at startup and logs any validation errors. If there are critical configuration errors, the application will fail to start.

Common validation errors include:
- Missing required settings (e.g., Phonexia API key)
- Invalid port ranges
- Non-existent directories
- Invalid URLs

## Applying Configuration Changes

To apply configuration changes:

1. Edit the configuration file
2. Save the changes
3. Restart the application or service

```bash
# Windows (NSSM)
nssm restart CCCZBuddyRealtimePhonexiaServer

# Linux (systemd)
sudo systemctl restart ccczbuddy-phonexia
```

## Configuration Best Practices

1. **Security**
   - Store sensitive information (e.g., API keys) in secure storage like Azure Key Vault or environment variables
   - Use specific IP addresses instead of "Any" in production
   - Use TLS for all external communication

2. **Performance**
   - Adjust RTP port range based on expected concurrent calls
   - Configure logging levels appropriately to avoid excessive logging
   - Tune performance settings based on server capacity

3. **Monitoring**
   - Enable detailed logging during initial deployment
   - Reduce logging verbosity in production
   - Configure log rotation to prevent disk space issues

4. **Backup**
   - Backup configuration files before making changes
   - Document all configuration changes
   - Use version control for configuration files

## Next Steps

After configuring the application, proceed to the [Operations Guide](../operations/monitoring.md) for information on monitoring and maintaining the system.
