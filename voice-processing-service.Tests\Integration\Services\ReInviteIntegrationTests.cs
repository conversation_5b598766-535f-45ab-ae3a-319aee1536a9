using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using SIPSorcery.Net;
using SIPSorcery.SIP;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;
using Xunit;
using System.Collections.Concurrent;

namespace voice_processing_service.Tests.Integration.Services
{
    /// <summary>
    /// Comprehensive integration tests for end-to-end re-INVITE handling.
    /// Tests the complete flow from SIP message reception to session update using real services.
    /// </summary>
    public class ReInviteIntegrationTests : IDisposable
    {
        private readonly List<IDisposable> _disposables = new();
        private readonly SipServerOptions _sipOptions;

        public ReInviteIntegrationTests()
        {
            _sipOptions = new SipServerOptions
            {
                ListenPort = 0, // Use random port for testing
                WavRecordingDirectory = "test_wav_reinvite",
                RtpPortMin = 50000,
                RtpPortMax = 50020
            };
        }

        #region Helper Methods and Test Infrastructure

        private SipServerService CreateSipServerService(
            CallSessionManager? sessionManager = null,
            PortAllocator? portAllocator = null,
            CallSynchronizationService? synchronizationService = null)
        {
            var logger = new NullLogger<SipServerService>();
            var optionsWrapper = Options.Create(_sipOptions);
            var loggerFactory = new NullLoggerFactory();
            
            // Use real services or create defaults
            var actualPortAllocator = portAllocator ?? CreatePortAllocator();
            var actualSyncService = synchronizationService ?? CreateSynchronizationService();
            var actualSessionManager = sessionManager ?? new CallSessionManager(
                new NullLogger<CallSessionManager>(),
                loggerFactory,
                actualPortAllocator,
                actualSyncService);
            var audioReceiverFactory = new TestRtpAudioReceiverFactory();
            
            Func<string, IAudioProcessor> simpleAudioProcessorFactory = callId => new TestAudioProcessor();
            Func<string, string, string, IAudioProcessor> enhancedAudioProcessorFactory =
                (callId, caller, called) => new TestAudioProcessor();
            var registrationManager = new TestSipRegistrationManager();

            var service = new SipServerService(
                logger,
                actualSessionManager,
                optionsWrapper,
                loggerFactory,
                actualPortAllocator,
                audioReceiverFactory,
                simpleAudioProcessorFactory,
                enhancedAudioProcessorFactory,
                registrationManager,
                actualSyncService);

            // Note: SipServerService implements IHostedService, not IDisposable directly
            // We'll manage its lifecycle through Start/Stop methods
            _disposables.Add(actualPortAllocator);
            _disposables.Add(actualSyncService);
            
            return service;
        }

        private PortAllocator CreatePortAllocator()
        {
            var options = Options.Create(_sipOptions);
            var logger = new NullLogger<PortAllocator>();
            return new PortAllocator(options, logger);
        }

        private CallSynchronizationService CreateSynchronizationService()
        {
            var logger = new NullLogger<CallSynchronizationService>();
            return new CallSynchronizationService(logger);
        }

        private SIPRequest CreateInviteRequest(string callId, string sdpBody, string? fromTag = null, string? toTag = null)
        {
            var fromHeader = $"<sip:tester@localhost>;tag={fromTag ?? "from-tag-123"}";
            var toHeader = toTag != null ? $"<sip:service@localhost>;tag={toTag}" : "<sip:service@localhost>";
            
            string invite =
                $"INVITE sip:service@localhost SIP/2.0\r\n" +
                $"Via: SIP/2.0/UDP 127.0.0.1:5060;branch=z9hG4bK{Guid.NewGuid()}\r\n" +
                $"From: {fromHeader}\r\n" +
                $"To: {toHeader}\r\n" +
                $"Call-ID: {callId}\r\n" +
                $"CSeq: 1 INVITE\r\n" +
                $"Content-Type: application/sdp\r\n" +
                $"Content-Length: {sdpBody.Length}\r\n\r\n" +
                sdpBody;
            return SIPRequest.ParseSIPRequest(invite);
        }

        private SIPRequest CreateReInviteRequest(string callId, string sdpBody, string fromTag, string toTag, int cseq = 2)
        {
            var fromHeader = $"<sip:tester@localhost>;tag={fromTag}";
            var toHeader = $"<sip:service@localhost>;tag={toTag}";
            
            string reInvite =
                $"INVITE sip:service@localhost SIP/2.0\r\n" +
                $"Via: SIP/2.0/UDP 127.0.0.1:5060;branch=z9hG4bK{Guid.NewGuid()}\r\n" +
                $"From: {fromHeader}\r\n" +
                $"To: {toHeader}\r\n" +
                $"Call-ID: {callId}\r\n" +
                $"CSeq: {cseq} INVITE\r\n" +
                $"Content-Type: application/sdp\r\n" +
                $"Content-Length: {sdpBody.Length}\r\n\r\n" +
                sdpBody;
            return SIPRequest.ParseSIPRequest(reInvite);
        }

        private string CreateSdpOffer(string connectionAddress = "127.0.0.1", int port = 40000, string[]? codecs = null)
        {
            var codecList = codecs ?? new[] { "0" };
            var codecLines = string.Join("\r\n", codecList.Select(c => 
                c == "0" ? "a=rtpmap:0 PCMU/8000" :
                c == "8" ? "a=rtpmap:8 PCMA/8000" :
                c == "18" ? "a=rtpmap:18 G729/8000" :
                $"a=rtpmap:{c} unknown/8000"));

            return $"v=0\r\n" +
                   $"o=- {DateTimeOffset.UtcNow.ToUnixTimeSeconds()} 0 IN IP4 {connectionAddress}\r\n" +
                   $"s=Test Session\r\n" +
                   $"c=IN IP4 {connectionAddress}\r\n" +
                   $"t=0 0\r\n" +
                   $"m=audio {port} RTP/AVP {string.Join(" ", codecList)}\r\n" +
                   codecLines;
        }

        private async Task SendSipRequest(SipServerService service, SIPRequest request)
        {
            var serviceType = typeof(SipServerService);
            var local = new SIPEndPoint(SIPProtocolsEnum.udp, new IPEndPoint(IPAddress.Loopback, 5060));
            var remote = new SIPEndPoint(SIPProtocolsEnum.udp, new IPEndPoint(IPAddress.Loopback, 5060));

            if (request.Method == SIPMethodsEnum.INVITE)
            {
                var method = serviceType.GetMethod("ProcessInviteAsync", BindingFlags.NonPublic | BindingFlags.Instance);
                var task = (Task)method.Invoke(service, new object[] { local, remote, request });
                await task.ConfigureAwait(false);
            }
            else
            {
                var method = serviceType.GetMethod("OnSipRequestReceived", BindingFlags.NonPublic | BindingFlags.Instance);
                var task = (Task)method.Invoke(service, new object[] { local, remote, request });
                await task.ConfigureAwait(false);
            }
        }

        private SIPResponse? GetCachedResponse(SipServerService service, string callId)
        {
            var cacheField = typeof(SipServerService)
                .GetField("_inviteResponseCache", BindingFlags.Instance | BindingFlags.NonPublic);
            var cache = (ConcurrentDictionary<string, SIPResponse>?)cacheField?.GetValue(service);
            if (cache != null && cache.TryGetValue(callId, out var response))
            {
                return response;
            }
            return null;
        }

        #endregion

        #region Test Classes

        private class TestRtpAudioReceiverFactory : IRtpAudioReceiverFactory
        {
            public IAudioInputReceiver CreateReceiver(string callId, int? rtpPort, int? rtcpPort)
            {
                return new TestAudioInputReceiver(rtpPort ?? 40000, rtcpPort ?? 40001);
            }

            public IAudioInputReceiver CreateReceiver(string callId, IPAddress localAddress)
            {
                return new TestAudioInputReceiver(40000, 40001);
            }
        }

        private class TestAudioInputReceiver : IAudioInputReceiver
        {
            public IPEndPoint RtpLocalEndPoint { get; }
            public IPEndPoint RtcpLocalEndPoint { get; }
            public bool UpdateConfigurationCalled { get; private set; }
            public IPEndPoint? LastRemoteEndpoint { get; private set; }
            public string[]? LastSupportedCodecs { get; private set; }
            public int UpdateConfigurationCallCount { get; private set; }

            public TestAudioInputReceiver(int rtpPort, int rtcpPort)
            {
                RtpLocalEndPoint = new IPEndPoint(IPAddress.Loopback, rtpPort);
                RtcpLocalEndPoint = new IPEndPoint(IPAddress.Loopback, rtcpPort);
            }

            public void Dispose() { }

            public Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
            {
                return Task.CompletedTask;
            }

            public Task UpdateConfigurationAsync(IPEndPoint newRemoteEndpoint, string[] supportedCodecs)
            {
                UpdateConfigurationCalled = true;
                LastRemoteEndpoint = newRemoteEndpoint;
                LastSupportedCodecs = supportedCodecs;
                UpdateConfigurationCallCount++;
                return Task.CompletedTask;
            }
        }

        private class TestAudioBuffer : IAudioBuffer
        {
            public void Add(byte[] data) { }
            public void Add(byte[] data, CancellationToken cancellationToken) { }
            public bool TryTake(out byte[] data, int timeout, CancellationToken cancellationToken)
            {
                data = null!;
                return false;
            }
            public byte[] Take(CancellationToken cancellationToken) { return null!; }
            public void CompleteAdding() { }
            public bool IsAddingCompleted => false;
            public bool IsCompleted => false;
            public void Dispose() { }
        }

        private class TestAudioProcessor : IAudioProcessor
        {
            public string ProcessorId => $"TEST_PROCESSOR_{Guid.NewGuid()}";
            public void Dispose() { }
            public Task InitializeAsync(CancellationToken cancellationToken) { return Task.CompletedTask; }
            public Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken) 
            { 
                return Task.CompletedTask; 
            }
        }

        private class TestSipRegistrationManager : ISipRegistrationManager
        {
            public void Dispose() { }
            public Task<SIPResponse> ProcessRegistrationAsync(SIPRequest request) { return Task.FromResult<SIPResponse>(null!); }
            public IEnumerable<voice_processing_service.Models.SipRegistration> GetActiveRegistrations() { return Enumerable.Empty<voice_processing_service.Models.SipRegistration>(); }
            public voice_processing_service.Models.SipRegistration? GetRegistration(string userUri) { return null; }
            public Task<int> CleanupExpiredRegistrationsAsync() { return Task.FromResult(0); }
            public int ActiveRegistrationCount => 0;
        }

        #endregion

        #region 1. End-to-End re-INVITE Flow Tests

        [Fact]
        public async Task EndToEnd_InitialInviteFollowedByReInvite_UpdatesSessionCorrectly()
        {
            // Arrange
            var sessionManager = new CallSessionManager(
                new NullLogger<CallSessionManager>(), 
                new NullLoggerFactory());
            var service = CreateSipServerService(sessionManager);
            await service.StartAsync(CancellationToken.None);

            var callId = Guid.NewGuid().ToString();
            var fromTag = "from-tag-123";
            
            // Initial INVITE
            var initialSdp = CreateSdpOffer("*************", 5000);
            var initialInvite = CreateInviteRequest(callId, initialSdp, fromTag);

            // Act - Send initial INVITE
            await SendSipRequest(service, initialInvite);
            await Task.Delay(500); // Allow processing

            // Verify initial session creation
            Assert.Single(sessionManager.GetAllSessions());
            var session = sessionManager.GetSession(callId);
            Assert.NotNull(session);
            
            // Get the To-tag from response for re-INVITE
            var initialResponse = GetCachedResponse(service, callId);
            Assert.NotNull(initialResponse);
            Assert.Equal(SIPResponseStatusCodesEnum.Ok, initialResponse.Status);
            var toTag = initialResponse.Header.To.ToTag;
            Assert.NotNull(toTag);

            // Store original RTP port for comparison
            var originalRtpPort = session.CurrentRtpPort;
            var originalRtcpPort = session.CurrentRtcpPort;

            // re-INVITE with updated SDP
            var updatedSdp = CreateSdpOffer("*************", 6000, new[] { "0", "8" });
            var reInvite = CreateReInviteRequest(callId, updatedSdp, fromTag, toTag);

            // Act - Send re-INVITE
            await SendSipRequest(service, reInvite);
            await Task.Delay(500); // Allow processing

            // Assert
            // Session should still exist (not recreated)
            Assert.Single(sessionManager.GetAllSessions());
            var updatedSession = sessionManager.GetSession(callId);
            Assert.NotNull(updatedSession);
            Assert.Same(session, updatedSession); // Same instance, not recreated

            // RTP ports should remain the same (not reallocated)
            Assert.Equal(originalRtpPort, updatedSession.CurrentRtpPort);
            Assert.Equal(originalRtcpPort, updatedSession.CurrentRtcpPort);

            // Verify response to re-INVITE
            var reInviteResponse = GetCachedResponse(service, callId);
            Assert.NotNull(reInviteResponse);
            Assert.Equal(SIPResponseStatusCodesEnum.Ok, reInviteResponse.Status);

            await service.StopAsync(CancellationToken.None);
        }

        [Fact]
        public async Task EndToEnd_MultipleSequentialReInvites_UpdatesSessionCorrectly()
        {
            // Arrange
            var sessionManager = new CallSessionManager(
                new NullLogger<CallSessionManager>(), 
                new NullLoggerFactory());
            var service = CreateSipServerService(sessionManager);
            await service.StartAsync(CancellationToken.None);

            var callId = Guid.NewGuid().ToString();
            var fromTag = "from-tag-456";

            // Initial INVITE
            var initialSdp = CreateSdpOffer("*************", 5000);
            var initialInvite = CreateInviteRequest(callId, initialSdp, fromTag);

            await SendSipRequest(service, initialInvite);
            await Task.Delay(500);

            var initialResponse = GetCachedResponse(service, callId);
            var toTag = initialResponse.Header.To.ToTag;
            var session = sessionManager.GetSession(callId);
            var originalRtpPort = session.CurrentRtpPort;

            // Act - Send multiple re-INVITEs
            for (int i = 2; i <= 4; i++)
            {
                var updatedSdp = CreateSdpOffer($"192.168.1.{100 + i}", 5000 + i * 100);
                var reInvite = CreateReInviteRequest(callId, updatedSdp, fromTag, toTag, i);
                
                await SendSipRequest(service, reInvite);
                await Task.Delay(300);
            }

            // Assert
            Assert.Single(sessionManager.GetAllSessions());
            var finalSession = sessionManager.GetSession(callId);
            Assert.Same(session, finalSession); // Same instance throughout
            Assert.Equal(originalRtpPort, finalSession.CurrentRtpPort); // Port unchanged

            await service.StopAsync(CancellationToken.None);
        }

        #endregion

        #region 2. Real Component Integration Tests

        [Fact]
        public async Task RealComponents_ReInviteWithPortAllocator_VerifiesPortReuse()
        {
            // Arrange
            var portAllocator = CreatePortAllocator();
            var syncService = CreateSynchronizationService();
            var sessionManager = new CallSessionManager(
                new NullLogger<CallSessionManager>(), 
                new NullLoggerFactory(), 
                portAllocator, 
                syncService);
            var service = CreateSipServerService(sessionManager, portAllocator, syncService);
            
            await service.StartAsync(CancellationToken.None);

            var callId = Guid.NewGuid().ToString();
            var fromTag = "from-tag-789";

            // Get initial port allocation count
            var initialAllocationInfo = portAllocator.GetDetailedAllocationInfo();
            var initialCount = (int)initialAllocationInfo["TotalAllocations"];

            // Act - Initial INVITE
            var initialSdp = CreateSdpOffer("*************", 5000);
            var initialInvite = CreateInviteRequest(callId, initialSdp, fromTag);
            await SendSipRequest(service, initialInvite);
            await Task.Delay(500);

            // Verify port allocation increased
            var afterInviteInfo = portAllocator.GetDetailedAllocationInfo();
            var afterInviteCount = (int)afterInviteInfo["TotalAllocations"];
            Assert.Equal(initialCount + 1, afterInviteCount);

            // Get session and port info
            var session = sessionManager.GetSession(callId);
            var allocatedRtpPort = session.CurrentRtpPort;
            var allocatedRtcpPort = session.CurrentRtcpPort;

            // re-INVITE
            var initialResponse = GetCachedResponse(service, callId);
            var toTag = initialResponse.Header.To.ToTag;
            var updatedSdp = CreateSdpOffer("*************", 6000);
            var reInvite = CreateReInviteRequest(callId, updatedSdp, fromTag, toTag);
            
            await SendSipRequest(service, reInvite);
            await Task.Delay(500);

            // Assert - Port allocation count should remain the same (reuse)
            var afterReInviteInfo = portAllocator.GetDetailedAllocationInfo();
            var afterReInviteCount = (int)afterReInviteInfo["TotalAllocations"];
            Assert.Equal(afterInviteCount, afterReInviteCount); // No new allocations

            // Ports should remain the same
            Assert.Equal(allocatedRtpPort, session.CurrentRtpPort);
            Assert.Equal(allocatedRtcpPort, session.CurrentRtcpPort);

            await service.StopAsync(CancellationToken.None);
        }

        [Fact]
        public async Task RealComponents_SipServerServiceWithRealSdpHandling_ParsesCorrectly()
        {
            // Arrange
            var service = CreateSipServerService();
            await service.StartAsync(CancellationToken.None);

            var callId = Guid.NewGuid().ToString();
            var fromTag = "from-tag-sdp";

            // Complex SDP with multiple codecs
            var complexSdp = CreateSdpOffer("**********", 8000, new[] { "0", "8", "18" });
            var initialInvite = CreateInviteRequest(callId, complexSdp, fromTag);

            // Act
            await SendSipRequest(service, initialInvite);
            await Task.Delay(500);

            var response = GetCachedResponse(service, callId);
            Assert.NotNull(response);
            Assert.Equal(SIPResponseStatusCodesEnum.Ok, response.Status);

            // Verify SDP parsing and response
            var responseSdp = SDP.ParseSDPDescription(response.Body);
            Assert.NotNull(responseSdp);
            Assert.NotEmpty(responseSdp.Media);
            
            var audioMedia = responseSdp.Media.First(m => m.Media == SDPMediaTypesEnum.audio);
            Assert.True(audioMedia.Port >= _sipOptions.RtpPortMin);
            Assert.True(audioMedia.Port <= _sipOptions.RtpPortMax);

            await service.StopAsync(CancellationToken.None);
        }

        #endregion

        #region 3. Concurrency Testing

        [Fact]
        public async Task Concurrency_MultipleReInvitesSameCallId_AreProperlySerializer()
        {
            // Arrange
            var syncService = CreateSynchronizationService();
            var sessionManager = new CallSessionManager(
                new NullLogger<CallSessionManager>(), 
                new NullLoggerFactory(), 
                null, 
                syncService);
            var service = CreateSipServerService(sessionManager, null, syncService);
            
            await service.StartAsync(CancellationToken.None);

            var callId = Guid.NewGuid().ToString();
            var fromTag = "from-tag-concurrent";

            // Initial INVITE
            var initialSdp = CreateSdpOffer("*************", 5000);
            var initialInvite = CreateInviteRequest(callId, initialSdp, fromTag);
            await SendSipRequest(service, initialInvite);
            await Task.Delay(500);

            var initialResponse = GetCachedResponse(service, callId);
            var toTag = initialResponse.Header.To.ToTag;

            // Act - Send multiple concurrent re-INVITEs
            var concurrentTasks = new List<Task>();
            var results = new ConcurrentBag<bool>();

            for (int i = 0; i < 5; i++)
            {
                var taskIndex = i;
                var task = Task.Run(async () =>
                {
                    try
                    {
                        var sdp = CreateSdpOffer($"192.168.1.{200 + taskIndex}", 6000 + taskIndex);
                        var reInvite = CreateReInviteRequest(callId, sdp, fromTag, toTag, 2 + taskIndex);
                        await SendSipRequest(service, reInvite);
                        results.Add(true);
                    }
                    catch
                    {
                        results.Add(false);
                    }
                });
                concurrentTasks.Add(task);
            }

            await Task.WhenAll(concurrentTasks);
            await Task.Delay(1000); // Allow all processing to complete

            // Assert
            Assert.Equal(5, results.Count);
            Assert.All(results, result => Assert.True(result)); // All should succeed
            
            // Session should still exist and be in valid state
            var session = sessionManager.GetSession(callId);
            Assert.NotNull(session);

            // Synchronization service should have handled the concurrency
            Assert.Equal(0, syncService.ActiveLockCount); // All locks should be released

            await service.StopAsync(CancellationToken.None);
        }

        [Fact]
        public async Task Concurrency_DifferentCallIds_ProcessConcurrently()
        {
            // Arrange
            var service = CreateSipServerService();
            await service.StartAsync(CancellationToken.None);

            var callIds = Enumerable.Range(1, 3).Select(i => $"call-{i}").ToArray();
            var fromTags = callIds.Select(id => $"from-{id}").ToArray();

            // Act - Send concurrent INVITEs for different Call-IDs
            var inviteTasks = callIds.Select((callId, index) =>
            {
                var sdp = CreateSdpOffer("*************", 5000 + index * 100);
                var invite = CreateInviteRequest(callId, sdp, fromTags[index]);
                return SendSipRequest(service, invite);
            });

            await Task.WhenAll(inviteTasks);
            await Task.Delay(1000);

            // Send concurrent re-INVITEs
            var reInviteTasks = callIds.Select(async (callId, index) =>
            {
                var response = GetCachedResponse(service, callId);
                if (response?.Header.To.ToTag != null)
                {
                    var sdp = CreateSdpOffer("*************", 6000 + index * 100);
                    var reInvite = CreateReInviteRequest(callId, sdp, fromTags[index], response.Header.To.ToTag);
                    await SendSipRequest(service, reInvite);
                }
            });

            await Task.WhenAll(reInviteTasks);
            await Task.Delay(500);

            // Assert - All sessions should exist
            var sessionManager = GetSessionManager(service);
            var allSessions = sessionManager.GetAllSessions().ToList();
            Assert.Equal(3, allSessions.Count);
            
            foreach (var callId in callIds)
            {
                Assert.NotNull(sessionManager.GetSession(callId));
            }

            await service.StopAsync(CancellationToken.None);
        }

        #endregion

        #region 4. Resource Management Verification

        [Fact]
        public async Task ResourceManagement_ReInviteDoesNotLeakPorts()
        {
            // Arrange
            var portAllocator = CreatePortAllocator();
            var service = CreateSipServerService(null, portAllocator);
            await service.StartAsync(CancellationToken.None);

            var callId = Guid.NewGuid().ToString();
            var fromTag = "from-tag-resource";

            // Capture initial state
            var initialInfo = portAllocator.GetDetailedAllocationInfo();
            var initialAllocations = (int)initialInfo["TotalAllocations"];

            // Act - Create session and perform multiple re-INVITEs
            var initialSdp = CreateSdpOffer("*************", 5000);
            var initialInvite = CreateInviteRequest(callId, initialSdp, fromTag);
            await SendSipRequest(service, initialInvite);
            await Task.Delay(500);

            var response = GetCachedResponse(service, callId);
            var toTag = response.Header.To.ToTag;

            // Multiple re-INVITEs
            for (int i = 0; i < 3; i++)
            {
                var sdp = CreateSdpOffer($"192.168.1.{200 + i}", 6000 + i);
                var reInvite = CreateReInviteRequest(callId, sdp, fromTag, toTag, 2 + i);
                await SendSipRequest(service, reInvite);
                await Task.Delay(300);
            }

            // Check allocations after re-INVITEs
            var afterReInvitesInfo = portAllocator.GetDetailedAllocationInfo();
            var afterReInvitesAllocations = (int)afterReInvitesInfo["TotalAllocations"];

            // Terminate session
            var sessionManager = GetSessionManager(service);
            await sessionManager.TerminateSessionAsync(callId);
            await Task.Delay(500);

            // Final check
            var finalInfo = portAllocator.GetDetailedAllocationInfo();
            var finalAllocations = (int)finalInfo["TotalAllocations"];

            // Assert
            Assert.Equal(initialAllocations + 1, afterReInvitesAllocations); // Only one allocation for the session
            Assert.Equal(initialAllocations, finalAllocations); // Back to initial state after cleanup

            await service.StopAsync(CancellationToken.None);
        }

        [Fact]
        public async Task ResourceManagement_ProperCleanupOnServiceStop()
        {
            // Arrange
            var portAllocator = CreatePortAllocator();
            var syncService = CreateSynchronizationService();
            var service = CreateSipServerService(null, portAllocator, syncService);
            
            await service.StartAsync(CancellationToken.None);

            // Create multiple sessions
            for (int i = 1; i <= 3; i++)
            {
                var callId = $"cleanup-test-{i}";
                var sdp = CreateSdpOffer("*************", 5000 + i);
                var invite = CreateInviteRequest(callId, sdp, $"from-{i}");
                await SendSipRequest(service, invite);
                await Task.Delay(200);
            }

            var beforeStopInfo = portAllocator.GetDetailedAllocationInfo();
            var beforeStopAllocations = (int)beforeStopInfo["TotalAllocations"];
            var beforeStopLocks = syncService.ActiveLockCount;

            // Act - Stop service
            await service.StopAsync(CancellationToken.None);
            await Task.Delay(1000); // Allow cleanup

            // Assert - Resources should be cleaned up
            var afterStopInfo = portAllocator.GetDetailedAllocationInfo();
            var afterStopAllocations = (int)afterStopInfo["TotalAllocations"];
            var afterStopLocks = syncService.ActiveLockCount;

            Assert.True(beforeStopAllocations > 0); // We had allocations
            Assert.Equal(0, afterStopLocks); // All locks cleaned up
        }

        #endregion

        #region 5. Error Scenarios

        [Fact]
        public async Task ErrorScenarios_ReInviteForNonExistentSession_ReturnsError()
        {
            // Arrange
            var service = CreateSipServerService();
            await service.StartAsync(CancellationToken.None);

            var nonExistentCallId = "non-existent-call";
            var sdp = CreateSdpOffer("*************", 5000);
            var reInvite = CreateReInviteRequest(nonExistentCallId, sdp, "from-tag", "to-tag");

            // Act
            await SendSipRequest(service, reInvite);
            await Task.Delay(500);

            // Assert - Should get error response
            var response = GetCachedResponse(service, nonExistentCallId);
            if (response != null)
            {
                // If we get a response, it should be an error
                Assert.NotEqual(SIPResponseStatusCodesEnum.Ok, response.Status);
            }
            // If no response, that's also acceptable as the session doesn't exist

            await service.StopAsync(CancellationToken.None);
        }

        [Fact]
        public async Task ErrorScenarios_InvalidSdpInReInvite_HandledGracefully()
        {
            // Arrange
            var service = CreateSipServerService();
            await service.StartAsync(CancellationToken.None);

            var callId = Guid.NewGuid().ToString();
            var fromTag = "from-tag-invalid";

            // Initial valid INVITE
            var validSdp = CreateSdpOffer("*************", 5000);
            var initialInvite = CreateInviteRequest(callId, validSdp, fromTag);
            await SendSipRequest(service, initialInvite);
            await Task.Delay(500);

            var response = GetCachedResponse(service, callId);
            var toTag = response.Header.To.ToTag;

            // Act - Send re-INVITE with invalid SDP
            var invalidSdp = "v=0\r\no=invalid sdp\r\n"; // Malformed SDP
            var reInvite = CreateReInviteRequest(callId, invalidSdp, fromTag, toTag);

            // Should not throw exception
            await SendSipRequest(service, reInvite);
            await Task.Delay(500);

            // Assert - Session should still exist and be functional
            var sessionManager = GetSessionManager(service);
            var session = sessionManager.GetSession(callId);
            Assert.NotNull(session);

            // Send another valid re-INVITE to verify session is still functional
            var validReInviteSdp = CreateSdpOffer("*************", 6000);
            var validReInvite = CreateReInviteRequest(callId, validReInviteSdp, fromTag, toTag, 3);
            await SendSipRequest(service, validReInvite);
            await Task.Delay(500);

            // Should still work
            Assert.NotNull(sessionManager.GetSession(callId));

            await service.StopAsync(CancellationToken.None);
        }

        #endregion

        #region Helper Methods for Test Infrastructure

        private CallSessionManager? GetSessionManager(SipServerService service)
        {
            var field = typeof(SipServerService).GetField("_sessionManager", BindingFlags.NonPublic | BindingFlags.Instance);
            return (CallSessionManager?)field?.GetValue(service);
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            foreach (var disposable in _disposables)
            {
                try
                {
                    disposable?.Dispose();
                }
                catch
                {
                    // Ignore disposal errors in tests
                }
            }
            _disposables.Clear();
        }

        #endregion
    }
}