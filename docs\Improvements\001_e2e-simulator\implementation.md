# Implementační detaily pro umožnění běhu serveru a simulátoru na jedné stanici

Tento dokument obsahuje konkrétní implementační detaily a ukázky kódu, kter<PERSON> je potřeba upravit, aby bylo možn<PERSON> spustit server a simulátor na jedné stanici.

## 1. Úpravy v simulátoru (voice-processing-simulator)

### 1.1 Úprava Program.cs - zpracování parametrů příkazové řádky

```csharp
// Původní kód
case "simulate":
    if (args.Length < 4)
    {
        Console.WriteLine("Usage: voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds]");
        Console.WriteLine("Example: voice-processing-simulator simulate test.wav 127.0.0.1 5060 30");
        return;
    }
    string wavFile = args[1];
    string serverIp = args[2];
    int serverPort = int.Parse(args[3]);
    int callDurationSeconds = args.Length > 4 ? int.Parse(args[4]) : 30; // Výchozí hodnota 30 sekund
    await SimulateCallAsync(wavFile, serverIp, serverPort, callDurationSeconds);
    break;

// Nový kód
case "simulate":
    if (args.Length < 4)
    {
        Console.WriteLine("Usage: voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds] [local_sip_port] [rtp_port_min] [rtp_port_max]");
        Console.WriteLine("Example: voice-processing-simulator simulate test.wav 127.0.0.1 5060 30 5070 25000 25010");
        return;
    }
    string wavFile = args[1];
    string serverIp = args[2];
    int serverPort = int.Parse(args[3]);
    int callDurationSeconds = args.Length > 4 ? int.Parse(args[4]) : 30; // Výchozí hodnota 30 sekund
    int localSipPort = args.Length > 5 ? int.Parse(args[5]) : 5070; // Výchozí hodnota 5070
    int rtpPortMin = args.Length > 6 ? int.Parse(args[6]) : 25000; // Výchozí hodnota 25000
    int rtpPortMax = args.Length > 7 ? int.Parse(args[7]) : 25010; // Výchozí hodnota 25010
    await SimulateCallAsync(wavFile, serverIp, serverPort, callDurationSeconds, localSipPort, rtpPortMin, rtpPortMax);
    break;
```

### 1.2 Úprava Program.cs - metoda SimulateCallAsync

```csharp
// Původní signatura metody
private static async Task SimulateCallAsync(string wavFile, string serverIp, int serverPort, int callDurationSeconds)

// Nová signatura metody
private static async Task SimulateCallAsync(string wavFile, string serverIp, int serverPort, int callDurationSeconds, int localSipPort = 5070, int rtpPortMin = 25000, int rtpPortMax = 25010)
```

```csharp
// Původní kód pro vytvoření SIP kanálu
var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, 5070);
sipTransport.AddSIPChannel(sipChannel);
_logger.LogInformation($"SIP UDP channel created on {sipChannel.ListeningEndPoint}");

// Nový kód pro vytvoření SIP kanálu s konfigurovatelným portem
var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, localSipPort);
sipTransport.AddSIPChannel(sipChannel);
_logger.LogInformation($"SIP UDP channel created on {sipChannel.ListeningEndPoint}");
```

```csharp
// Původní kód pro vytvoření VoIP media session
_rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(25000, 25010),
});

// Nový kód pro vytvoření VoIP media session s konfigurovatelným rozsahem portů
_rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(rtpPortMin, rtpPortMax),
});
```

### 1.3 Úprava Program.cs - metoda ShowUsage

```csharp
// Původní kód
private static void ShowUsage()
{
    Console.WriteLine("Usage:");
    Console.WriteLine("  voice-processing-simulator generate <output_file> <duration_seconds> <tone_frequency>");
    Console.WriteLine("  voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds]");
    Console.WriteLine("Examples:");
    Console.WriteLine("  voice-processing-simulator generate test.wav 10 440");
    Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5060 30");
}

// Nový kód
private static void ShowUsage()
{
    Console.WriteLine("Usage:");
    Console.WriteLine("  voice-processing-simulator generate <output_file> <duration_seconds> <tone_frequency>");
    Console.WriteLine("  voice-processing-simulator simulate <wav_file> <server_ip> <server_port> [call_duration_seconds] [local_sip_port] [rtp_port_min] [rtp_port_max]");
    Console.WriteLine("Examples:");
    Console.WriteLine("  voice-processing-simulator generate test.wav 10 440");
    Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5060 30");
    Console.WriteLine("  voice-processing-simulator simulate test.wav 127.0.0.1 5060 30 5070 25000 25010");
}
```

### 1.4 Úprava Properties/launchSettings.json

```json
// Původní kód
{
  "profiles": {
    "voice-processing-simulator": {
      "commandName": "Project",
      "commandLineArgs": "simulate sage.wav 127.0.0.1 5060 20"
    }
  }
}

// Nový kód
{
  "profiles": {
    "voice-processing-simulator": {
      "commandName": "Project",
      "commandLineArgs": "simulate sage.wav 127.0.0.1 5060 20 5070 25000 25010"
    }
  }
}
```

## 2. Úpravy v serveru (voice-processing-service)

### 2.1 Konfigurace pomocí appsettings.json a proměnných prostředí

Server používá konfiguraci z appsettings.json, kterou lze přepsat pomocí proměnných prostředí. ASP.NET Core a Kestrel nativně podporují tuto funkcionalitu, takže není potřeba přidávat vlastní kód pro zpracování parametrů příkazové řádky.

Konfigurace v appsettings.json:

```json
"SipServer": {
  "ListenIpAddress": "Any",
  "ListenPort": 5060,
  "RtpPortMin": 30002,
  "RtpPortMax": 30010,
  "WavRecordingDirectory": "RecordedCalls"
}
```

Přepsání konfigurace pomocí proměnných prostředí:

```bash
# Windows
set SIPSERVER__LISTENPORT=5061
set SIPSERVER__RTPPORTMIN=31000
set SIPSERVER__RTPPORTMAX=31010

# Linux/macOS
export SIPSERVER__LISTENPORT=5061
export SIPSERVER__RTPPORTMIN=31000
export SIPSERVER__RTPPORTMAX=31010
```

ASP.NET Core automaticky nahradí dvojtečky v názvech sekcí konfigurace dvěma podtržítky v názvech proměnných prostředí.
```

### 2.2 Úprava RtpAudioReceiverFactory

Třída `RtpAudioReceiverFactory` obsahuje metodu `CreateReceiverWithSpecificPorts`, která má několik problémů, které je potřeba vyřešit. Podrobný popis problémů a navrhované řešení je v dokumentu [rtpaudioreceiverfactory_update.md](rtpaudioreceiverfactory_update.md).

Hlavní problém je, že metoda obsahuje pevně nastavené porty (40002 a 40003) pro vytvoření UDP klientů, což je v rozporu s parametry metody. Tato implementace ignoruje parametry `rtpPort` a `rtcpPort` a místo toho používá pevně nastavené porty, což může způsobit konflikty portů a znemožnit běh více instancí serveru na jedné stanici.

```csharp
// Původní kód
public IAudioInputReceiver CreateReceiverWithSpecificPorts(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
{
    try
    {
        var aaa = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40002));
        var bbb = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40003));

        // ...

        // Vytvořit UDP klienty
        var rtpClient = aaa;
        var rtcpClient = bbb;

        // ...
    }
    // ...
}

// Nový kód
public IAudioInputReceiver CreateReceiverWithSpecificPorts(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
{
    try
    {
        _logger.LogInformation($"[{callId}] Attempting to create RTP receiver with specific ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit UDP klienty s požadovanými porty
        var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
        var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));

        // ...
    }
    // ...
}
```

### 2.3 Úprava Properties/launchSettings.json

```json
// Původní kód
{
  "profiles": {
    "voice_processing_service": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7001;http://localhost:5001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}

// Nový kód - přidání profilu s vlastními porty pomocí proměnných prostředí
{
  "profiles": {
    "voice_processing_service": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7001;http://localhost:5001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "voice_processing_service_custom_ports": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7001;http://localhost:5001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "SIPSERVER__LISTENPORT": "5061",
        "SIPSERVER__RTPPORTMIN": "31000",
        "SIPSERVER__RTPPORTMAX": "31010"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
```

## 3. Příklady spuštění

### 3.1 Spuštění serveru s vlastními porty

```bash
# Windows - nastavení proměnných prostředí před spuštěním
set SIPSERVER__LISTENPORT=5061
set SIPSERVER__RTPPORTMIN=31000
set SIPSERVER__RTPPORTMAX=31010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Linux/macOS - nastavení proměnných prostředí před spuštěním
export SIPSERVER__LISTENPORT=5061
export SIPSERVER__RTPPORTMIN=31000
export SIPSERVER__RTPPORTMAX=31010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Alternativně lze použít příkaz s proměnnými prostředí v jednom řádku
# Windows
set SIPSERVER__LISTENPORT=5061 && set SIPSERVER__RTPPORTMIN=31000 && set SIPSERVER__RTPPORTMAX=31010 && dotnet run --project voice-processing-service/voice-processing-service.csproj

# Linux/macOS
SIPSERVER__LISTENPORT=5061 SIPSERVER__RTPPORTMIN=31000 SIPSERVER__RTPPORTMAX=31010 dotnet run --project voice-processing-service/voice-processing-service.csproj
```

### 3.2 Spuštění simulátoru s vlastními porty

```bash
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30 5070 25000 25010
```

### 3.3 Spuštění serveru a simulátoru na jedné stanici

```bash
# Terminál 1 - Server (Windows)
set SIPSERVER__LISTENPORT=5060
set SIPSERVER__RTPPORTMIN=30000
set SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 1 - Server (Linux/macOS)
export SIPSERVER__LISTENPORT=5060
export SIPSERVER__RTPPORTMIN=30000
export SIPSERVER__RTPPORTMAX=30010
dotnet run --project voice-processing-service/voice-processing-service.csproj

# Terminál 2 - Simulátor
dotnet run --project voice-processing-simulator/voice-processing-simulator.csproj -- simulate test.wav 127.0.0.1 5060 30 5070 25000 25010
```

## 4. Ověření funkčnosti

Po implementaci změn je potřeba ověřit, že server a simulátor mohou běžet na jedné stanici a správně komunikovat. Doporučený postup:

1. Spusťte server s vlastními porty
2. Spusťte simulátor s vlastními porty
3. Ověřte, že simulátor se úspěšně připojí k serveru
4. Ověřte, že RTP stream je správně přenášen
5. Ověřte, že hovor je správně ukončen

Pokud některý z kroků selže, zkontrolujte:

1. Logy serveru a simulátoru pro identifikaci problému
2. Zda jsou porty správně nakonfigurovány a nejsou v konfliktu
3. Zda jsou porty povoleny ve firewallu
4. Zda jsou obě komponenty kompatibilní s použitou verzí SIPSorcery
