# Implementace RTP přijímače

## Popis úkolu

Tento úkol zahrnuje implementaci přijímače RTP paketů, k<PERSON><PERSON> bude přijímat audio data z SIP klienta a předávat je do audio bufferu. P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> musí být schopen parsovat RTP pakety, extrahovat audio data a správně je předávat do bufferu.

## Technické detaily

### Implementace RtpAudioReceiver

Implementace bude využívat `UdpClient` z .NET pro příjem RTP a RTCP paketů.

```csharp
using System;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace přijímače audio dat z RTP paketů.
    /// </summary>
    public class RtpAudioReceiver : IAudioInputReceiver
    {
        private readonly ILogger<RtpAudioReceiver> _logger;
        private readonly UdpClient _rtpClient;
        private readonly UdpClient _rtcpClient;
        private readonly string _callId; // Pro logování

        /// <inheritdoc/>
        public IPEndPoint RtpLocalEndPoint { get; }

        /// <inheritdoc/>
        public IPEndPoint RtcpLocalEndPoint { get; }

        /// <summary>
        /// Inicializuje novou instanci třídy <see cref="RtpAudioReceiver"/>.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru pro logování.</param>
        /// <param name="rtpClient">UDP klient pro příjem RTP paketů.</param>
        /// <param name="rtcpClient">UDP klient pro příjem RTCP paketů.</param>
        /// <param name="logger">Logger pro logování událostí.</param>
        public RtpAudioReceiver(string callId, UdpClient rtpClient, UdpClient rtcpClient, ILogger<RtpAudioReceiver> logger)
        {
            _callId = callId ?? throw new ArgumentNullException(nameof(callId));
            _rtpClient = rtpClient ?? throw new ArgumentNullException(nameof(rtpClient));
            _rtcpClient = rtcpClient ?? throw new ArgumentNullException(nameof(rtcpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            RtpLocalEndPoint = (IPEndPoint)_rtpClient.Client.LocalEndPoint;
            RtcpLocalEndPoint = (IPEndPoint)_rtcpClient.Client.LocalEndPoint;

            _logger.LogInformation($"[{_callId}] RtpAudioReceiver created for RTP={RtpLocalEndPoint}, RTCP={RtcpLocalEndPoint}");
        }

        /// <inheritdoc/>
        public async Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{_callId}] Starting RTP/RTCP listeners...");

            var rtpTask = Task.Run(() => ListenLoopAsync(_rtpClient, "RTP", buffer.Add, cancellationToken), cancellationToken);
            var rtcpTask = Task.Run(() => ListenLoopAsync(_rtcpClient, "RTCP", ProcessRtcpPacket, cancellationToken), cancellationToken); // RTCP data nejdou do bufferu

            try
            {
                // Počkáme na dokončení obou listenerů nebo na zrušení
                // Nepoužíváme WhenAll, protože chceme, aby buffer.CompleteAdding bylo zavoláno i když jeden selže
                await Task.WhenAny(rtpTask, rtcpTask); // Počkáme, až jeden skončí (nebo oba budou zrušeny)
                _logger.LogInformation($"[{_callId}] One of the listeners finished or was cancelled.");

                // Počkáme chvíli, jestli neskončí i druhý
                await Task.WhenAll(rtpTask, rtcpTask).WaitAsync(TimeSpan.FromSeconds(1), CancellationToken.None); // Krátký timeout pro dokončení druhého
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] Listening cancelled.");
            }
            catch (TimeoutException) {
                _logger.LogWarning($"[{_callId}] Timeout waiting for the second listener task to complete.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Exception while waiting for listener tasks.");
            }
            finally
            {
                _logger.LogInformation($"[{_callId}] Signaling buffer completion.");
                buffer.CompleteAdding(); // Důležité: Signalizovat bufferu, že už nepřijdou žádná data
            }
            _logger.LogInformation($"[{_callId}] Listeners stopped.");
        }

        private async Task ListenLoopAsync(UdpClient client, string type, Action<byte[]> dataHandler, CancellationToken cancellationToken)
        {
            IPEndPoint remoteEndPoint = null; // Přesunuto ven z cyklu
            _logger.LogInformation($"[{_callId}] {type} listener started on {client.Client.LocalEndPoint}.");
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var result = await client.ReceiveAsync(cancellationToken);
                    remoteEndPoint = result.RemoteEndPoint; // Uložíme pro případnou chybu
                    if (result.Buffer.Length > 0)
                    {
                        // Voláme specifický handler pro RTP nebo RTCP
                        // Pro RTP parsujeme a voláme buffer.Add, pro RTCP jen logujeme
                        if(type == "RTP")
                        {
                            ParseAndHandleRtp(result.Buffer, remoteEndPoint, dataHandler);
                        }
                        else // RTCP
                        {
                            dataHandler(result.Buffer); // dataHandler je zde ProcessRtcpPacket
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] {type} listener loop cancelled.");
            }
            catch (ObjectDisposedException) {
                _logger.LogWarning($"[{_callId}] {type} listener UDP client was disposed while listening.");
            }
            catch (SocketException ex) when (ex.SocketErrorCode == SocketError.Interrupted || ex.SocketErrorCode == SocketError.OperationAborted){
                _logger.LogInformation($"[{_callId}] {type} listener socket operation interrupted/aborted.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error in {type} listener loop from {remoteEndPoint?.ToString() ?? "unknown"}");
            }
            finally
            {
                _logger.LogInformation($"[{_callId}] {type} listener loop finished on {client.Client?.LocalEndPoint?.ToString() ?? "closed socket"}.");
            }
        }

        // Parsování RTP a volání handleru (buffer.Add)
        private void ParseAndHandleRtp(byte[] buffer, IPEndPoint remoteEndPoint, Action<byte[]> bufferAddAction)
        {
            if (buffer.Length < 12) return;

            // Předpoklad: Payload Type 0 = PCMU/G.711u
            var payloadType = buffer[1] & 0x7F;
            if (payloadType != 0 && payloadType != 8) // Povolíme i A-law (PT=8), i když WAV ukládá jen u-law
            {
                // _logger.LogTrace($"[{_callId}] Ignoring RTP packet with unsupported payload type {payloadType} from {remoteEndPoint}.");
                return;
            }

            int headerLength = 12;
            // Zkontrolujeme, zda je nastaven extension bit (X)
            bool hasExtension = (buffer[0] & 0x10) > 0;
            if(hasExtension)
            {
                if (buffer.Length < headerLength + 4) return;
                int extensionHeaderLengthInWords = (buffer[headerLength + 2] << 8) | buffer[headerLength + 3];
                int extensionDataLength = extensionHeaderLengthInWords * 4;
                headerLength += 4 + extensionDataLength;
                if (buffer.Length < headerLength) return;
            }
            // Počítáme s případnými CSRC identifikátory (CC field)
            int csrcCount = buffer[0] & 0x0F;
            headerLength += csrcCount * 4;

            if (buffer.Length <= headerLength) return; // Jen hlavička

            var audioDataLength = buffer.Length - headerLength;
            byte[] audioData = new byte[audioDataLength];
            Buffer.BlockCopy(buffer, headerLength, audioData, 0, audioDataLength);

            // Přidání dat do bufferu
            bufferAddAction(audioData);
        }

        // Zpracování RTCP (zatím jen loguje)
        private void ProcessRtcpPacket(byte[] buffer)
        {
            if (buffer.Length >= 2)
            {
                var payloadType = buffer[1];
                // _logger.LogTrace($"[{_callId}] Received RTCP packet (PT={payloadType}, Length={buffer.Length}).");
            }
        }

        /// <inheritdoc/>
        public void Dispose()
        {
            _logger.LogInformation($"[{_callId}] Disposing RtpAudioReceiver for {RtpLocalEndPoint}.");
            _rtpClient?.Close();
            _rtpClient?.Dispose();
            _rtcpClient?.Close();
            _rtcpClient?.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
```

### Továrna pro vytváření RtpAudioReceiver

Pro vytváření instancí RtpAudioReceiver bude implementována tovární metoda, která bude zodpovědná za vytvoření a inicializaci UDP klientů.

```csharp
using System;
using System.Net;
using System.Net.Sockets;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Továrna pro vytváření instancí RtpAudioReceiver.
    /// </summary>
    public class RtpAudioReceiverFactory
    {
        private readonly ILoggerFactory _loggerFactory;
        private readonly Random _random = new Random();
        private readonly SipServerOptions _options;

        /// <summary>
        /// Inicializuje novou instanci třídy <see cref="RtpAudioReceiverFactory"/>.
        /// </summary>
        /// <param name="loggerFactory">Továrna pro vytváření loggerů.</param>
        /// <param name="options">Konfigurace SIP serveru.</param>
        public RtpAudioReceiverFactory(ILoggerFactory loggerFactory, IOptions<SipServerOptions> options)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }

        /// <summary>
        /// Vytvoří novou instanci RtpAudioReceiver.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru pro logování.</param>
        /// <param name="localAddress">Lokální IP adresa pro naslouchání.</param>
        /// <returns>Instance IAudioInputReceiver.</returns>
        public IAudioInputReceiver CreateReceiver(string callId, IPAddress localAddress)
        {
            // Najít volné porty pro RTP a RTCP
            if (!TryGetAvailableRtpPorts(localAddress, out int rtpPort, out int rtcpPort))
            {
                throw new InvalidOperationException("No available RTP/RTCP ports found.");
            }

            // Vytvořit UDP klienty
            var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
            var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));

            // Vytvořit a vrátit RtpAudioReceiver
            return new RtpAudioReceiver(callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());
        }

        private bool TryGetAvailableRtpPorts(IPAddress localAddress, out int rtpPort, out int rtcpPort)
        {
            // Výchozí hodnoty
            rtpPort = 0;
            rtcpPort = 0;

            // Maximální počet pokusů
            const int maxAttempts = 50;
            int attempts = 0;

            while (attempts < maxAttempts)
            {
                attempts++;

                // Vygenerovat náhodný sudý port v rozsahu
                int port = _random.Next(_options.RtpPortMin / 2, _options.RtpPortMax / 2) * 2;
                
                // Zkontrolovat, zda jsou porty volné
                if (IsPortAvailable(localAddress, port) && IsPortAvailable(localAddress, port + 1))
                {
                    rtpPort = port;
                    rtcpPort = port + 1;
                    return true;
                }
            }

            return false;
        }

        private bool IsPortAvailable(IPAddress address, int port)
        {
            try
            {
                using var socket = new Socket(address.AddressFamily, SocketType.Dgram, ProtocolType.Udp);
                socket.Bind(new IPEndPoint(address, port));
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
```

### Registrace v DI kontejneru

RtpAudioReceiverFactory bude registrována v DI kontejneru jako singleton služba:

```csharp
// V Program.cs
services.AddSingleton<RtpAudioReceiverFactory>();
```

## Testovací scénáře

### Unit testy pro RtpAudioReceiver

1. **Test konstruktoru**
   - Ověřit, že instance RtpAudioReceiver je vytvořena bez chyb
   - Ověřit, že RtpLocalEndPoint a RtcpLocalEndPoint jsou správně inicializovány

2. **Test metody StartListeningAsync**
   - Ověřit, že metoda StartListeningAsync spustí naslouchání na UDP portech
   - Ověřit, že metoda StartListeningAsync respektuje cancellation token

3. **Test parsování RTP paketů**
   - Ověřit, že metoda ParseAndHandleRtp správně parsuje RTP pakety
   - Ověřit, že metoda ParseAndHandleRtp správně extrahuje audio data
   - Ověřit, že metoda ParseAndHandleRtp správně filtruje nepodporované payload typy

4. **Test metody Dispose**
   - Ověřit, že po zavolání Dispose jsou UDP klienty správně uvolněny
   - Ověřit, že po zavolání Dispose nelze spustit naslouchání

### Unit testy pro RtpAudioReceiverFactory

1. **Test konstruktoru**
   - Ověřit, že instance RtpAudioReceiverFactory je vytvořena bez chyb

2. **Test metody CreateReceiver**
   - Ověřit, že metoda CreateReceiver vytvoří instanci RtpAudioReceiver
   - Ověřit, že metoda CreateReceiver najde volné porty pro RTP a RTCP
   - Ověřit, že metoda CreateReceiver vyhodí výjimku, pokud nejsou k dispozici žádné volné porty

3. **Test metody TryGetAvailableRtpPorts**
   - Ověřit, že metoda TryGetAvailableRtpPorts najde volné porty pro RTP a RTCP
   - Ověřit, že metoda TryGetAvailableRtpPorts vrátí false, pokud nejsou k dispozici žádné volné porty

### Integrační testy pro RtpAudioReceiver a AudioBuffer

1. **Test příjmu RTP paketů a předání do bufferu**
   - Vytvořit instanci RtpAudioReceiver a AudioBuffer
   - Poslat RTP pakety na porty RtpAudioReceiver
   - Ověřit, že audio data jsou správně extrahována a předána do bufferu

2. **Test ukončení naslouchání**
   - Vytvořit instanci RtpAudioReceiver a AudioBuffer
   - Spustit naslouchání a pak zrušit pomocí cancellation tokenu
   - Ověřit, že naslouchání je ukončeno a buffer je označen jako ukončený

## Implementační kroky

1. Implementovat třídu RtpAudioReceiver
2. Implementovat třídu RtpAudioReceiverFactory
3. Implementovat unit testy pro RtpAudioReceiver
4. Implementovat unit testy pro RtpAudioReceiverFactory
5. Implementovat integrační testy pro RtpAudioReceiver a AudioBuffer
6. Registrovat RtpAudioReceiverFactory v DI kontejneru

## Simulace pro testování

Pro testování RtpAudioReceiver bude vytvořena jednoduchá konzolová aplikace, která simuluje odesílání RTP paketů:

```csharp
using System;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;

namespace voice_processing_service.Tests
{
    class RtpSimulator
    {
        static async Task Main(string[] args)
        {
            if (args.Length < 3)
            {
                Console.WriteLine("Usage: RtpSimulator <destination_ip> <rtp_port> <wav_file>");
                return;
            }

            string destinationIp = args[0];
            int rtpPort = int.Parse(args[1]);
            string wavFile = args[2];

            // Vytvoření UDP klienta pro odesílání RTP paketů
            using var udpClient = new UdpClient();
            var destinationEndPoint = new IPEndPoint(IPAddress.Parse(destinationIp), rtpPort);

            // Načtení WAV souboru
            byte[] wavData = await File.ReadAllBytesAsync(wavFile);
            
            // Přeskočení WAV hlavičky (44 bytů)
            int offset = 44;
            
            // Parametry RTP
            ushort sequenceNumber = 0;
            uint timestamp = 0;
            uint ssrc = (uint)new Random().Next();
            
            // Velikost RTP paketu (20ms G.711 při 8kHz = 160 vzorků = 160 bytů)
            const int payloadSize = 160;
            
            Console.WriteLine($"Sending RTP packets to {destinationEndPoint}...");
            
            // Odesílání RTP paketů
            while (offset < wavData.Length)
            {
                // Vytvoření RTP hlavičky
                byte[] rtpHeader = new byte[12];
                rtpHeader[0] = 0x80; // Version=2, P=0, X=0, CC=0
                rtpHeader[1] = 0x00; // M=0, PT=0 (PCMU/G.711u)
                rtpHeader[2] = (byte)(sequenceNumber >> 8); // Sequence number (high byte)
                rtpHeader[3] = (byte)(sequenceNumber & 0xFF); // Sequence number (low byte)
                rtpHeader[4] = (byte)(timestamp >> 24); // Timestamp (highest byte)
                rtpHeader[5] = (byte)(timestamp >> 16); // Timestamp
                rtpHeader[6] = (byte)(timestamp >> 8); // Timestamp
                rtpHeader[7] = (byte)(timestamp & 0xFF); // Timestamp (lowest byte)
                rtpHeader[8] = (byte)(ssrc >> 24); // SSRC (highest byte)
                rtpHeader[9] = (byte)(ssrc >> 16); // SSRC
                rtpHeader[10] = (byte)(ssrc >> 8); // SSRC
                rtpHeader[11] = (byte)(ssrc & 0xFF); // SSRC (lowest byte)
                
                // Vytvoření RTP paketu (hlavička + payload)
                int bytesToSend = Math.Min(payloadSize, wavData.Length - offset);
                byte[] rtpPacket = new byte[rtpHeader.Length + bytesToSend];
                Buffer.BlockCopy(rtpHeader, 0, rtpPacket, 0, rtpHeader.Length);
                Buffer.BlockCopy(wavData, offset, rtpPacket, rtpHeader.Length, bytesToSend);
                
                // Odeslání RTP paketu
                await udpClient.SendAsync(rtpPacket, rtpPacket.Length, destinationEndPoint);
                Console.WriteLine($"Sent RTP packet: seq={sequenceNumber}, ts={timestamp}, size={rtpPacket.Length}");
                
                // Aktualizace parametrů
                sequenceNumber++;
                timestamp += (uint)bytesToSend; // Pro G.711 je timestamp = počet vzorků = počet bytů
                offset += bytesToSend;
                
                // Pauza 20ms mezi pakety
                await Task.Delay(20);
            }
            
            Console.WriteLine("All RTP packets sent.");
        }
    }
}
```

Tato simulace umožní otestovat příjem RTP paketů a jejich zpracování v RtpAudioReceiver.
