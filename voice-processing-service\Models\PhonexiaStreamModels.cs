using System.Text.Json.Serialization;

namespace voice_processing_service.Models
{
    /// <summary>
    /// Model pro odpověď z Phonexia API při přihlášení.
    /// </summary>
    public class PhonexiaLoginResponse
    {
        [JsonPropertyName("result")]
        public PhonexiaLoginResult Result { get; set; }
    }

    /// <summary>
    /// Model pro výsledek přihlášení z Phonexia API.
    /// </summary>
    public class PhonexiaLoginResult
    {
        [JsonPropertyName("session")]
        public PhonexiaSession Session { get; set; }
    }

    /// <summary>
    /// Model pro session z Phonexia API.
    /// </summary>
    public class PhonexiaSession
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    /// <summary>
    /// Model pro odpověď z Phonexia API při vytvoření streamu.
    /// </summary>
    public class PhonexiaStreamResponse
    {
        [JsonPropertyName("result")]
        public PhonexiaStreamResult Result { get; set; }
    }

    /// <summary>
    /// Model pro výsledek vytvoření streamu z Phonexia API.
    /// </summary>
    public class PhonexiaStreamResult
    {
        [JsonPropertyName("input_stream")]
        public string InputStream { get; set; }

        [JsonPropertyName("port")]
        public int Port { get; set; }
    }

    /// <summary>
    /// Model pro odpověď z Phonexia API při vytvoření STT úlohy.
    /// </summary>
    public class PhonexiaSttTaskResponse
    {
        [JsonPropertyName("result")]
        public PhonexiaSttTaskResult Result { get; set; }
    }

    /// <summary>
    /// Model pro výsledek vytvoření STT úlohy z Phonexia API.
    /// </summary>
    public class PhonexiaSttTaskResult
    {
        [JsonPropertyName("stream_task_info")]
        public PhonexiaStreamTaskInfo StreamTaskInfo { get; set; }
    }

    /// <summary>
    /// Model pro informace o STT úloze z Phonexia API.
    /// </summary>
    public class PhonexiaStreamTaskInfo
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    /// <summary>
    /// Model pro odpověď z Phonexia API přes WebSocket.
    /// </summary>
    public class PhonexiaSttWebSocketResponse
    {
        [JsonPropertyName("result")]
        public PhonexiaSttWebSocketResult Result { get; set; }
    }

    /// <summary>
    /// Model pro výsledek z Phonexia API přes WebSocket.
    /// </summary>
    public class PhonexiaSttWebSocketResult
    {
        [JsonPropertyName("one_best_result")]
        public PhonexiaOneBestResult OneBestResult { get; set; }

        [JsonPropertyName("n_best_result")]
        public PhonexiaNBestResult NBestResult { get; set; }

        [JsonPropertyName("confusion_network_result")]
        public PhonexiaConfusionNetworkResult[] ConfusionNetworkResult { get; set; }
    }

    /// <summary>
    /// Model pro výsledek one_best z Phonexia API.
    /// </summary>
    public class PhonexiaOneBestResult
    {
        [JsonPropertyName("segmentation")]
        public PhonexiaSegment[] Segmentation { get; set; }
    }

    /// <summary>
    /// Model pro segment z Phonexia API.
    /// </summary>
    public class PhonexiaSegment
    {
        [JsonPropertyName("word")]
        public string Word { get; set; }

        [JsonPropertyName("start")]
        public double Start { get; set; }

        [JsonPropertyName("end")]
        public double End { get; set; }

        [JsonPropertyName("confidence")]
        public double Confidence { get; set; }
    }

    /// <summary>
    /// Model pro výsledek n_best z Phonexia API.
    /// </summary>
    public class PhonexiaNBestResult
    {
        [JsonPropertyName("phrase_variants")]
        public PhonexiaPhraseVariant[] PhraseVariants { get; set; }
    }

    /// <summary>
    /// Model pro variantu fráze z Phonexia API.
    /// </summary>
    public class PhonexiaPhraseVariant
    {
        [JsonPropertyName("variant")]
        public PhonexiaVariant[] Variant { get; set; }
    }

    /// <summary>
    /// Model pro variantu z Phonexia API.
    /// </summary>
    public class PhonexiaVariant
    {
        [JsonPropertyName("phrase")]
        public string Phrase { get; set; }

        [JsonPropertyName("confidence")]
        public double Confidence { get; set; }
    }

    /// <summary>
    /// Model pro výsledek confusion_network z Phonexia API.
    /// </summary>
    public class PhonexiaConfusionNetworkResult
    {
        [JsonPropertyName("word")]
        public string Word { get; set; }

        [JsonPropertyName("confidence")]
        public double Confidence { get; set; }
    }
}
