# Copyright © O2 Czech Republic a.s. - All Rights Reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.
# Terms and Conditions of usage are defined in file 'LICENSE.txt', which is part of this source code package.

ARG PYTHON_VERSION="3.10"

FROM network.git.cz.o2:5005/ntwcl/gitopsntw/python:${PYTHON_VERSION}-slim-bookworm as baseimage
MAINTAINER <EMAIL>

ARG targetDir=/opt
ARG pythonPackages=${targetDir}/python-packages
ARG userName=su_netdata

COPY --chown=${userName}:root requirements.txt ${targetDir}

RUN python3 -m pip install --target ${pythonPackages} --quiet --upgrade pip setuptools wheel \
    && python3 -m pip install --target ${pythonPackages} --quiet --upgrade -r ${targetDir}/requirements.txt \
    && python3 -m pip cache purge
