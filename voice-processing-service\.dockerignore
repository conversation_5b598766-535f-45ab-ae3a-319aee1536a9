# Ignore build artifacts
bin/
obj/
out/
publish/

# Ignore IDE files
.vs/
.vscode/
*.user
*.suo
*.userosscache
*.sln.docstates

# Ignore logs and recordings
logs/
recordings/
RecordedCalls/
*.log
*.wav

# Ignore project-specific files
data/
*.ps1
*.py
*.tar
augmentrc
REPOSITORY_CLEANUP_SUMMARY.md

# Ignore temporary files
*.tmp
*.temp
*~

# Ignore OS files
.DS_Store
Thumbs.db

# Ignore Git files
.git/
.gitignore

# Ignore Docker files
Dockerfile*
.dockerignore

# Ignore test results
TestResults/
*.trx
*.coverage

# Ignore NuGet packages
packages/
*.nupkg

# Ignore documentation
README.md
docs/

# Ignore configuration overrides
appsettings.Development.json
appsettings.Local.json
