using System;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using System.Net;
using Microsoft.Extensions.Logging.Abstractions;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class CallSessionTests
    {
        // Dummy implementations for IAudioInputReceiver, IAudioBuffer and IAudioProcessor
        private class DummyAudioReceiver : IAudioInputReceiver, IDisposable
        {
            public bool WasStarted { get; private set; }
            public IPEndPoint RtpLocalEndPoint => null;
            public IPEndPoint RtcpLocalEndPoint => null;
            public Task StartListeningAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
            {
                WasStarted = true;
                return Task.CompletedTask;
            }
            public Task UpdateConfigurationAsync(IPEndPoint newRemoteEndpoint, string[] supportedCodecs)
            {
                return Task.CompletedTask;
            }
            public void Dispose() { }
        }

        private class DummyAudioBuffer : IAudioBuffer, IDisposable
        {
            public bool WasCompleted { get; private set; }
            public void Add(byte[] data) { }
            public void Add(byte[] data, CancellationToken cancellationToken) { }
            public bool TryTake(out byte[] data, int timeout, CancellationToken cancellationToken) { data = null; return false; }
            public byte[] Take(CancellationToken cancellationToken) { return null; }
            public void CompleteAdding() { WasCompleted = true; }
            public bool IsAddingCompleted => WasCompleted;
            public bool IsCompleted => WasCompleted;
            public void Dispose() { }
        }

        private class DummyAudioProcessor : IAudioProcessor, IDisposable
        {
            public bool WasStarted { get; private set; }
            public string ProcessorId => "DUMMY";
            public Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
            {
                WasStarted = true;
                return Task.CompletedTask;
            }
            public void Dispose() { }
        }

        [Fact]
        public void Constructor_NullUserAgent_ThrowsArgumentNullException()
        {
            var invite = (SIPRequest)FormatterServices.GetUninitializedObject(typeof(SIPRequest));
            var header = (SIPHeader)FormatterServices.GetUninitializedObject(typeof(SIPHeader));
            invite.Header = header;
            Assert.Throws<ArgumentNullException>(() =>
                new CallSession(
                    null,
                    invite,
                    new DummyAudioReceiver(),
                    new DummyAudioBuffer(),
                    new DummyAudioProcessor(),
                    () => Task.CompletedTask,
                    NullLogger<CallSession>.Instance));
        }

        [Fact]
        public void Constructor_NullInviteRequest_ThrowsArgumentNullException()
        {
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            Assert.Throws<ArgumentNullException>(() =>
                new CallSession(
                    userAgent,
                    null,
                    new DummyAudioReceiver(),
                    new DummyAudioBuffer(),
                    new DummyAudioProcessor(),
                    () => Task.CompletedTask,
                    NullLogger<CallSession>.Instance));
        }

        [Fact]
        public void Constructor_InvalidCallId_ThrowsArgumentException()
        {
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var invite = (SIPRequest)FormatterServices.GetUninitializedObject(typeof(SIPRequest));
            var header = (SIPHeader)FormatterServices.GetUninitializedObject(typeof(SIPHeader));
            invite.Header = header;
            // invite.Header.CallId is null by default
            Assert.Throws<ArgumentException>(() =>
                new CallSession(
                    userAgent,
                    invite,
                    new DummyAudioReceiver(),
                    new DummyAudioBuffer(),
                    new DummyAudioProcessor(),
                    () => Task.CompletedTask,
                    NullLogger<CallSession>.Instance));
        }

        [Fact]
        public void Constructor_ValidParameters_SetsProperties()
        {
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var invite = (SIPRequest)FormatterServices.GetUninitializedObject(typeof(SIPRequest));
            var header = (SIPHeader)FormatterServices.GetUninitializedObject(typeof(SIPHeader));
            invite.Header = header;
            invite.Header.CallId = "CALL123";
            var receiver = new DummyAudioReceiver();
            var buffer = new DummyAudioBuffer();
            var processor = new DummyAudioProcessor();
            bool cleanupCalled = false;
            Func<Task> cleanup = () =>
            {
                cleanupCalled = true;
                return Task.CompletedTask;
            };
            var session = new CallSession(
                userAgent,
                invite,
                receiver,
                buffer,
                processor,
                cleanup,
                NullLogger<CallSession>.Instance);

            Assert.Equal("CALL123", session.CallId);
            Assert.Same(userAgent, session.UserAgent);
            Assert.Same(invite, session.InitialInviteRequest);
            Assert.Same(receiver, session.AudioReceiver); // AudioReceiver should match the provided receiver
        }

        [Fact]
        public async Task StartAsync_StartsReceiverAndProcessor()
        {
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var invite = (SIPRequest)FormatterServices.GetUninitializedObject(typeof(SIPRequest));
            var header = (SIPHeader)FormatterServices.GetUninitializedObject(typeof(SIPHeader));
            invite.Header = header;
            invite.Header.CallId = "CALL_START";
            var receiver = new DummyAudioReceiver();
            var buffer = new DummyAudioBuffer();
            var processor = new DummyAudioProcessor();
            var session = new CallSession(
                userAgent,
                invite,
                receiver,
                buffer,
                processor,
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            await session.StartAsync(CancellationToken.None);

            Assert.True(receiver.WasStarted, "Receiver should have been started");
            Assert.True(processor.WasStarted, "Processor should have been started");
            Assert.InRange(session.StartTime, DateTime.UtcNow.AddSeconds(-1), DateTime.UtcNow);
        }

        [Fact]
        public async Task StopAsync_InvokesCleanupCallback()
        {
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var invite = (SIPRequest)FormatterServices.GetUninitializedObject(typeof(SIPRequest));
            var header = (SIPHeader)FormatterServices.GetUninitializedObject(typeof(SIPHeader));
            invite.Header = header;
            invite.Header.CallId = "CALL_STOP";
            var receiver = new DummyAudioReceiver();
            var buffer = new DummyAudioBuffer();
            var processor = new DummyAudioProcessor();
            bool cleanupCalled = false;
            Func<Task> cleanup = () =>
            {
                cleanupCalled = true;
                return Task.CompletedTask;
            };
            var session = new CallSession(
                userAgent,
                invite,
                receiver,
                buffer,
                processor,
                cleanup,
                NullLogger<CallSession>.Instance);

            await session.StartAsync(CancellationToken.None);
            await session.StopAsync();

            Assert.True(cleanupCalled, "Cleanup callback should have been invoked");
        }

        [Fact]
        public void Dispose_AfterDispose_CannotDisposeAgain()
        {
            var userAgent = (SIPServerUserAgent)FormatterServices.GetUninitializedObject(typeof(SIPServerUserAgent));
            var invite = (SIPRequest)FormatterServices.GetUninitializedObject(typeof(SIPRequest));
            var header = (SIPHeader)FormatterServices.GetUninitializedObject(typeof(SIPHeader));
            invite.Header = header;
            invite.Header.CallId = "CALL_DISPOSE";
            var receiver = new DummyAudioReceiver();
            var buffer = new DummyAudioBuffer();
            var processor = new DummyAudioProcessor();
            var session = new CallSession(
                userAgent,
                invite,
                receiver,
                buffer,
                processor,
                () => Task.CompletedTask,
                NullLogger<CallSession>.Instance);

            session.Dispose();
            session.Dispose(); // second call should not throw
        }
    }
}