# System Architecture

## Overview

The CCCZ Buddy Realtime Phonexia Server is designed as a modular SIP server that receives calls, processes audio streams in real-time, and integrates with Phonexia speech-to-text services. The architecture follows clean design principles with clear separation of concerns, making it maintainable and extensible.

## High-Level Architecture

The system is built on ASP.NET Core (.NET 9) and follows a modular architecture with the following key components:

1. **SIP Server** - Handles SIP signaling for call setup and teardown
2. **Call Session Management** - Manages the lifecycle of active calls
3. **Audio Processing Pipeline** - Processes audio data from RTP streams
4. **Speech-to-Text Integration** - Converts audio to text using Phonexia API
5. **Configuration and Monitoring** - Provides configuration options and monitoring capabilities

## Architecture Diagram

```mermaid
graph TD
    subgraph External
        SIP_Client[SIP Client]
    end

    subgraph SipApplication ["ASP.NET Core Application (.NET 9)"]
        A_Kestrel[Kestrel Web Server] --> B_ApiEndpoints(API Endpoints /api/calls)
        A_Kestrel --> C_SipServerService(SipServerService : IHostedService)

        subgraph DependencyInjection ["DI Container"]
            D_SIPTransport(SIPTransport - Singleton)
            E_CallSessionManager(CallSessionManager : ICallSessionManager - Singleton)
            F_LoggerFactory(ILoggerFactory - Singleton)
            G_AudioReceiverFactory(Factory for IAudioInputReceiver - Transient)
            H_AudioBufferFactory(Factory for IAudioBuffer - Transient)
            I_AudioProcessorFactory(Factory for IAudioProcessor - Transient)
            J_CallSessionFactory(Factory for ICallSession - Transient)
        end

        C_SipServerService -- Incoming INVITE --> E_CallSessionManager
        B_ApiEndpoints -- Call Management --> E_CallSessionManager

        E_CallSessionManager -- Creates / Manages --> K_CallSession(ICallSession - Instance per Call)

        subgraph CallSessionInstance [Instance for 1 call]
            direction LR
            K_CallSession --> L_SIPUserAgent(SIPUserAgent)
            K_CallSession --> M_AudioReceiver(IAudioInputReceiver)
            K_CallSession --> N_AudioBuffer(IAudioBuffer)
            K_CallSession --> O_AudioProcessor(IAudioProcessor)

            M_AudioReceiver -- byte[] audio --> N_AudioBuffer
            O_AudioProcessor -- reads byte[] --> N_AudioBuffer
        end

        M_AudioReceiver -- network data --> P_UdpListeners(UDP Listeners RTP/RTCP)
        O_AudioProcessor -- result (WAV/Text) --> Q_Output[Storage / STT Result]
    end

    SIP_Client -- SIP (UDP/TCP) --> C_SipServerService
    SIP_Client -- RTP/RTCP (UDP) --> P_UdpListeners
```

## Component Details

### SIP Server (SipServerService)

- Implemented as an IHostedService in ASP.NET Core
- Uses SIPSorcery library for SIP protocol handling
- Listens for incoming SIP requests (INVITE, BYE, CANCEL)
- Delegates call handling to CallSessionManager

### Call Session Management (CallSessionManager)

- Manages the lifecycle of active call sessions
- Creates new CallSession instances for incoming calls
- Provides methods to retrieve, terminate, and list active sessions
- Thread-safe implementation for concurrent call handling

### Audio Processing Pipeline

The audio processing pipeline consists of three main components:

1. **Audio Input Receiver (RtpAudioReceiver)**
   - Receives RTP packets from the network
   - Extracts audio data from RTP packets
   - Writes audio data to the audio buffer

2. **Audio Buffer (BlockingCollectionAudioBuffer)**
   - Thread-safe buffer for audio data
   - Decouples audio reception from processing
   - Implements producer-consumer pattern

3. **Audio Processor (SttAudioProcessor, PhonexiaSttProcessor)**
   - Reads audio data from the buffer
   - Processes audio data (e.g., saves to WAV file, performs STT)
   - Outputs results (e.g., WAV file, transcription text)

### Speech-to-Text Integration

- Integrates with Phonexia API for speech-to-text conversion
- Supports both batch and streaming STT modes
- Configurable language models and parameters
- Optional Kafka integration for publishing transcription results

### Configuration and Monitoring

- Configuration via appsettings.json
- Structured logging with configurable log levels
- Health checks for monitoring system status
- API endpoints for system management

## Data Flow

1. **Call Setup**
   - SIP client sends INVITE to SipServerService
   - SipServerService creates a new CallSession via CallSessionManager
   - CallSession initializes AudioInputReceiver, AudioBuffer, and AudioProcessor
   - SipServerService responds with 200 OK and SDP for RTP

2. **Audio Processing**
   - SIP client sends RTP packets to the allocated port
   - RtpAudioReceiver receives RTP packets and extracts audio data
   - RtpAudioReceiver writes audio data to AudioBuffer
   - AudioProcessor reads audio data from AudioBuffer
   - AudioProcessor processes audio data (e.g., saves to WAV, performs STT)

3. **Call Teardown**
   - SIP client sends BYE or CANCEL
   - SipServerService terminates the CallSession via CallSessionManager
   - CallSession stops AudioInputReceiver and AudioProcessor
   - CallSession releases resources

## Scalability and Performance Considerations

- Each call is processed in its own set of tasks
- Thread-safe implementation for concurrent call handling
- Configurable buffer sizes for audio data
- Configurable RTP port range for multiple concurrent calls
- Efficient memory management with buffer recycling

## Security Considerations

- No authentication implemented in the current version
- Network security should be handled at the infrastructure level
- Phonexia API key should be kept secure
- Logging does not include sensitive information

## Future Enhancements

- Authentication and authorization for SIP clients
- TLS support for SIP signaling
- SRTP support for secure RTP
- Clustering for high availability
- Advanced audio processing (e.g., noise reduction, speaker diarization)
- Real-time analytics and reporting
