apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-demo
spec:
  ingressClassName: nginx
  rules:
  - host: nginx-{{ .Release.Namespace }}.{{ .Values.ingressRoute.dnsSuffix }}
    http:
      paths:
      - backend:
          service:
            name: {{ .Release.Name }}
            port:
              name: http
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - nginx-{{ .Release.Namespace }}.{{ .Values.ingressRoute.dnsSuffix }}
