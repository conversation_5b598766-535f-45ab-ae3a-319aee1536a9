using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Net;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace session jednoho hovoru.
    /// </summary>
    public class CallSession : ICallSession
    {
        private readonly ILogger<CallSession> _logger;
        private readonly IAudioInputReceiver _audioReceiver;
        private readonly IAudioBuffer _audioBuffer;
        private readonly IAudioProcessor _audioProcessor;
        private readonly Func<Task> _cleanupCallback;
        private CancellationTokenSource _sessionCts;
        private Task _receiverTask;
        private Task _processorTask;
        private bool _disposed = false;

        /// <summary>
        /// Identifikátor hovoru (Call-ID).
        /// </summary>
        public string CallId { get; }

        /// <summary>
        /// SIP User Agent pro tento hovor.
        /// </summary>
        public SIPServerUserAgent UserAgent { get; }

        /// <summary>
        /// Původní INVITE požadavek, kter<PERSON> zahájil hovor.
        /// </summary>
        public SIPRequest InitialInviteRequest { get; }

        /// <summary>
        /// Přijímač audio dat.
        /// </summary>
        public IAudioInputReceiver AudioReceiver => _audioReceiver;

        /// <summary>
        /// Čas zahájení hovoru.
        /// </summary>
        public DateTime StartTime { get; private set; }

        /// <summary>
        /// Získá aktuální RTP port používaný session.
        /// </summary>
        public int CurrentRtpPort
        {
            get
            {
                if (_audioReceiver is RtpAudioReceiver rtpReceiver)
                {
                    return rtpReceiver.RtpPort;
                }
                return 0;
            }
        }

        /// <summary>
        /// Získá aktuální RTCP port používaný session.
        /// </summary>
        public int CurrentRtcpPort
        {
            get
            {
                if (_audioReceiver is RtpAudioReceiver rtpReceiver)
                {
                    return rtpReceiver.RtcpPort;
                }
                return 0;
            }
        }

        /// <summary>
        /// Vytvoří novou instanci CallSession.
        /// </summary>
        /// <param name="userAgent">SIP User Agent pro tento hovor.</param>
        /// <param name="inviteRequest">INVITE požadavek, který zahájil hovor.</param>
        /// <param name="audioReceiver">Přijímač audio dat.</param>
        /// <param name="audioBuffer">Buffer pro audio data.</param>
        /// <param name="audioProcessor">Procesor audio dat.</param>
        /// <param name="cleanupCallback">Callback pro úklid v manažeru, až session skončí.</param>
        /// <param name="logger">Logger.</param>
        public CallSession(
            SIPServerUserAgent userAgent,
            SIPRequest inviteRequest,
            IAudioInputReceiver audioReceiver,
            IAudioBuffer audioBuffer,
            IAudioProcessor audioProcessor,
            Func<Task> cleanupCallback,
            ILogger<CallSession> logger)
        {
            if (userAgent == null) throw new ArgumentNullException(nameof(userAgent));
            if (inviteRequest == null) throw new ArgumentNullException(nameof(inviteRequest));
            var callId = inviteRequest.Header?.CallId ?? throw new ArgumentException("INVITE request must have a Call-ID", nameof(inviteRequest));
            CallId = callId;
            UserAgent = userAgent;
            InitialInviteRequest = inviteRequest;
            _audioReceiver = audioReceiver ?? throw new ArgumentNullException(nameof(audioReceiver));
            _audioBuffer = audioBuffer ?? throw new ArgumentNullException(nameof(audioBuffer));
            _audioProcessor = audioProcessor ?? throw new ArgumentNullException(nameof(audioProcessor));
            _cleanupCallback = cleanupCallback ?? throw new ArgumentNullException(nameof(cleanupCallback));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _logger.LogInformation($"[{CallId}] CallSession created.");
        }

        /// <summary>
        /// Spustí session (zahájí příjem a zpracování audio dat).
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{CallId}] Starting session...");
            _sessionCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken); // Propojení s externím tokenem
            StartTime = DateTime.UtcNow;
            await _audioProcessor.InitializeAsync(_sessionCts.Token).ConfigureAwait(true);

            // Create single buffer instance
            var buffer = _audioBuffer;
            _logger.LogTrace($"[{CallId}] Audio buffer instance hash: {buffer.GetHashCode()}");

            // Spustit příjemce a procesor v samostatných úlohách
            _receiverTask = _audioReceiver.StartListeningAsync(buffer, _sessionCts.Token);
            _processorTask = _audioProcessor.StartProcessingAsync(buffer, _sessionCts.Token);

            _logger.LogInformation($"[{CallId}] Session started. Receiver and Processor tasks running.");
            // Nevracíme Tasky, běží na pozadí. Jejich ukončení řeší StopAsync.
            return; // Task.CompletedTask;
        }

        /// <summary>
        /// Zastaví session (ukončí příjem a zpracování audio dat).
        /// </summary>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StopAsync()
        {
            _logger.LogInformation($"[{CallId}] Stopping session...");

            try
            {
                // Zrušení všech operací
                if (_sessionCts != null && !_sessionCts.IsCancellationRequested)
                {
                    _sessionCts.Cancel();
                }

                // Signalizace, že už žádná data nebudou přidána do bufferu
                try
                {
                    _audioBuffer.CompleteAdding();
                }
                catch (ObjectDisposedException ex)
                {
                    _logger.LogWarning(ex, $"[{CallId}] Audio buffer already disposed when completing adding.");
                }

                // Čekání na dokončení úloh
                if (_receiverTask != null)
                {
                    try
                    {
                        await _receiverTask.ConfigureAwait(false);
                        _logger.LogDebug($"[{CallId}] Receiver task completed.");
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.LogDebug($"[{CallId}] Receiver task was cancelled.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{CallId}] Error waiting for receiver task to complete.");
                    }
                }

                if (_processorTask != null)
                {
                    try
                    {
                        await _processorTask.ConfigureAwait(false);
                        _logger.LogDebug($"[{CallId}] Processor task completed.");
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.LogDebug($"[{CallId}] Processor task was cancelled.");
                    }
                    catch (System.Net.WebSockets.WebSocketException wex)
                    {
                        _logger.LogWarning(wex, $"[{CallId}] WebSocketException while waiting for processor task; likely already closed.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{CallId}] Error waiting for processor task to complete.");
                    }
                }

                // Volání callback metody pro úklid v manažeru
                try
                {
                    await _cleanupCallback().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{CallId}] Error executing cleanup callback.");
                }

                _logger.LogInformation($"[{CallId}] Session stopped.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{CallId}] Error stopping session.");
                throw;
            }
        }

        /// <summary>
        /// Aktualizuje parametry session na základě re-INVITE požadavku.
        /// Umožňuje změnu media parametrů bez vytvoření nové session.
        /// </summary>
        /// <param name="reInviteRequest">Re-INVITE SIP požadavek s novými parametry.</param>
        /// <param name="newSdpOffer">Nová SDP nabídka z re-INVITE.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task UpdateSessionAsync(SIPRequest reInviteRequest, SDP newSdpOffer)
        {
            _logger.LogInformation($"[{CallId}] Updating session parameters based on re-INVITE");

            try
            {
                // Analyze SDP offer for changes
                var audioMedia = newSdpOffer.Media?.FirstOrDefault(m =>
                    m.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase));
                
                if (audioMedia == null)
                {
                    _logger.LogWarning($"[{CallId}] No audio media found in re-INVITE SDP offer");
                    return;
                }

                // Log the current and new parameters for comparison
                var currentRtpPort = CurrentRtpPort;
                var currentRtcpPort = CurrentRtcpPort;
                var newRemotePort = audioMedia.Port;
                var newRemoteAddress = newSdpOffer.Connection?.ConnectionAddress;

                _logger.LogInformation($"[{CallId}] Session update - Current ports: RTP={currentRtpPort}, RTCP={currentRtcpPort}");
                _logger.LogInformation($"[{CallId}] Session update - New remote: {newRemoteAddress}:{newRemotePort}");

                // Check if codec parameters have changed
                var supportedCodecs = new[] { "PCMU", "PCMA", "G722" };
                var offeredCodecs = audioMedia.MediaFormats?.Keys?.Where(pt =>
                {
                    var format = audioMedia.MediaFormats[pt];
                    return supportedCodecs.Any(codec =>
                        string.Equals(codec, format.Name(), StringComparison.OrdinalIgnoreCase));
                }).ToList();

                if (offeredCodecs?.Any() == true)
                {
                    _logger.LogInformation($"[{CallId}] Compatible codecs in re-INVITE: {string.Join(", ", offeredCodecs)}");
                }
                else
                {
                    _logger.LogWarning($"[{CallId}] No compatible codecs found in re-INVITE");
                }

                // Create remote endpoint from SDP offer for RTP receiver update
                var remoteAddress = newSdpOffer.Connection?.ConnectionAddress;
                var remotePort = audioMedia.Port;
                
                if (!string.IsNullOrEmpty(remoteAddress) && remotePort > 0)
                {
                    try
                    {
                        var remoteEndpoint = new System.Net.IPEndPoint(System.Net.IPAddress.Parse(remoteAddress), remotePort);
                        
                        // Update the RTP receiver configuration with new remote endpoint and codecs
                        await _audioReceiver.UpdateConfigurationAsync(remoteEndpoint, supportedCodecs);
                        
                        _logger.LogInformation($"[{CallId}] RTP receiver updated with remote endpoint {remoteEndpoint}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"[{CallId}] Could not parse remote endpoint {remoteAddress}:{remotePort}, using existing configuration");
                    }
                }

                // The key insight: we don't need to recreate the RTP receiver or audio processor
                // The existing RTP receiver continues listening on the same ports with updated configuration
                // The VoIPMediaSession in SipServerService will handle the SDP negotiation
                // The audio processor (Phonexia STT) continues uninterrupted ensuring stream continuity

                _logger.LogInformation($"[{CallId}] Session parameters updated successfully - maintaining existing resources and stream continuity");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{CallId}] Error updating session parameters");
                throw;
            }
        }

        /// <summary>
        /// Uvolní prostředky.
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            _logger.LogInformation($"[{CallId}] Disposing CallSession.");

            try
            {
                // Zrušení všech operací
                _sessionCts?.Cancel();
                _sessionCts?.Dispose();

                // Uvolnění prostředků
                _audioBuffer?.Dispose();
                _audioReceiver?.Dispose();
                _audioProcessor?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{CallId}] Error disposing CallSession.");
            }

            _disposed = true;
            GC.SuppressFinalize(this);
        }
    }
}
