# Úprava RtpAudioReceiverFactory pro správnou konfiguraci portů

Při analýze kódu jsem z<PERSON>il, že třída `RtpAudioReceiverFactory` obsahuje metodu `CreateReceiverWithSpecificPorts`, kter<PERSON> má několik problémů, kter<PERSON> je potřeba vyřešit v rámci našeho návrhu pro umožnění běhu serveru a simulátoru na jedné stanici.

## 1. Identifikované problémy

### 1.1 Pevně nastavené porty v implementaci

Aktuální implementace metody `CreateReceiverWithSpecificPorts` obsahuje pevně nastavené porty (40002 a 40003) pro vytvoření UDP klientů, což je v rozporu s parametry metody:

```csharp
public IAudioInputReceiver CreateReceiverWithSpecificPorts(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
{
    try
    {
        var aaa = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40002));
        var bbb = new UdpClient(new IPEndPoint(System.Net.IPAddress.Any, 40003));

        _logger.LogInformation($"[{callId}] Attempting to create RTP receiver with specific ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Pokusíme se uvolnit porty před jejich použitím
        _logger.LogInformation($"[{callId}] Attempting to release ports before use: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit UDP klienty
        var rtpClient = aaa;
        var rtcpClient = bbb;

        // ...
    }
    // ...
}
```

Tato implementace ignoruje parametry `rtpPort` a `rtcpPort` a místo toho používá pevně nastavené porty 40002 a 40003. To může způsobit konflikty portů a znemožnit běh více instancí serveru na jedné stanici.

### 1.2 Nekonzistentní chování při vytváření UDP klientů

Metoda obsahuje kód pro uvolnění portů a opětovné vytvoření UDP klientů v případě výjimky, ale tento kód používá správné parametry `rtpPort` a `rtcpPort`, zatímco hlavní část metody používá pevně nastavené porty.

## 2. Navrhované řešení

### 2.1 Úprava metody CreateReceiverWithSpecificPorts

Metodu `CreateReceiverWithSpecificPorts` je potřeba upravit tak, aby správně používala parametry `rtpPort` a `rtcpPort` pro vytvoření UDP klientů:

```csharp
public IAudioInputReceiver CreateReceiverWithSpecificPorts(string callId, IPAddress localAddress, int rtpPort, int rtcpPort)
{
    try
    {
        _logger.LogInformation($"[{callId}] Attempting to create RTP receiver with specific ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit UDP klienty s požadovanými porty
        var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
        var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));

        // Nastavení většího bufferu pro UDP klienty
        rtpClient.Client.ReceiveBufferSize = 1048576; // 1MB buffer
        rtpClient.Client.SendBufferSize = 1048576;
        rtcpClient.Client.ReceiveBufferSize = 1048576;
        rtcpClient.Client.SendBufferSize = 1048576;

        _logger.LogInformation($"[{callId}] Successfully created RTP receiver with ports: RTP={rtpPort}, RTCP={rtcpPort}");

        // Vytvořit a vrátit RtpAudioReceiver
        return new RtpAudioReceiver(callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());
    }
    catch (SocketException ex)
    {
        // Port je již obsazen nebo nemáme dostatečná oprávnění
        _logger.LogWarning($"[{callId}] Could not bind to RTP port {rtpPort} and RTCP port {rtcpPort}: {ex.Message} (Error code: {ex.SocketErrorCode})");

        // Zkusíme ještě jednou uvolnit porty s jiným přístupem
        _logger.LogInformation($"[{callId}] Attempting to force release ports with different approach");

        try
        {
            // Pokus o vytvoření socketů s nastavením ReuseAddress
            using (var socket1 = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp))
            {
                socket1.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                socket1.Bind(new IPEndPoint(localAddress, rtpPort));
                socket1.Close();
                _logger.LogInformation($"[{callId}] Successfully force-released RTP port {rtpPort}");
            }

            using (var socket2 = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp))
            {
                socket2.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                socket2.Bind(new IPEndPoint(localAddress, rtcpPort));
                socket2.Close();
                _logger.LogInformation($"[{callId}] Successfully force-released RTCP port {rtcpPort}");
            }

            // Zkusíme znovu vytvořit UDP klienty
            var rtpClient = new UdpClient(new IPEndPoint(localAddress, rtpPort));
            var rtcpClient = new UdpClient(new IPEndPoint(localAddress, rtcpPort));

            // Nastavení většího bufferu pro UDP klienty
            rtpClient.Client.ReceiveBufferSize = 1048576; // 1MB buffer
            rtpClient.Client.SendBufferSize = 1048576;
            rtcpClient.Client.ReceiveBufferSize = 1048576;
            rtcpClient.Client.SendBufferSize = 1048576;

            _logger.LogInformation($"[{callId}] Successfully created RTP receiver after force-releasing ports: RTP={rtpPort}, RTCP={rtcpPort}");

            // Vytvořit a vrátit RtpAudioReceiver
            return new RtpAudioReceiver(callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());
        }
        catch (Exception innerEx)
        {
            _logger.LogWarning($"[{callId}] Force-release of ports failed: {innerEx.Message}");
            throw new InvalidOperationException($"Could not create RTP receiver with ports: RTP={rtpPort}, RTCP={rtcpPort}", innerEx);
        }
    }
}
```

Tato úprava zajistí, že metoda `CreateReceiverWithSpecificPorts` bude správně používat parametry `rtpPort` a `rtcpPort` pro vytvoření UDP klientů, což umožní běh více instancí serveru na jedné stanici s různými porty.

## 3. Integrace s celkovým řešením

Tato úprava je důležitou součástí celkového řešení pro umožnění běhu serveru a simulátoru na jedné stanici. Správná implementace metody `CreateReceiverWithSpecificPorts` zajistí, že server bude správně vytvářet RTP přijímače s porty, které jsou specifikovány v konfiguraci.

V kombinaci s úpravami konfigurace portů pomocí proměnných prostředí, jak je navrženo v dokumentu `README.md`, bude možné spustit více instancí serveru na jedné stanici s různými porty.

## 4. Testování

Po implementaci této úpravy je potřeba otestovat, zda server správně vytváří RTP přijímače s porty, které jsou specifikovány v konfiguraci. Toto testování by mělo být součástí testovacího plánu, který je popsán v dokumentu `testing.md`.

Konkrétně je potřeba otestovat:

1. Zda server správně vytváří RTP přijímače s porty, které jsou specifikovány v konfiguraci
2. Zda je možné spustit více instancí serveru na jedné stanici s různými porty
3. Zda server správně komunikuje se simulátorem, když oba běží na jedné stanici s různými porty
