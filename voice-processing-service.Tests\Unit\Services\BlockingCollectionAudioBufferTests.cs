using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Reflection;
using Microsoft.Extensions.Logging.Abstractions;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class BlockingCollectionAudioBufferTests
    {
        [Fact]
        public void Constructor_InitializesBuffer()
        {
            var logger = NullLoggerFactory.Instance.CreateLogger<BlockingCollectionAudioBuffer>();
            using var buffer = new BlockingCollectionAudioBuffer(logger);
            Assert.False(buffer.IsAddingCompleted);
            Assert.False(buffer.IsCompleted);
        }

        [Fact]
        public void Add_Then_Dequeue_OrderIsPreserved()
        {
            var logger = NullLoggerFactory.Instance.CreateLogger<BlockingCollectionAudioBuffer>();
            using var buffer = new BlockingCollectionAudioBuffer(logger);
            var inputs = new[]
            {
                new byte[] { 1 },
                new byte[] { 2, 2 },
                new byte[] { 3, 3, 3 }
            };

            foreach (var item in inputs)
            {
                buffer.Add(item);
            }

            buffer.CompleteAdding();

            // Access underlying BlockingCollection via reflection
            var field = typeof(BlockingCollectionAudioBuffer)
                .GetField("_buffer", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.NotNull(field);
            var inner = (BlockingCollection<byte[]>)(field.GetValue(buffer)!);
            var outputs = inner.GetConsumingEnumerable().ToList();

            Assert.Equal(inputs.Length, outputs.Count);

            for (int i = 0; i < inputs.Length; i++)
            {
                Assert.Equal(inputs[i], outputs[i]);
            }
        }

        [Fact]
        public void CompleteAdding_PreventsFurtherAdds_ButAllowsExistingToBeConsumed()
        {
            var logger = NullLoggerFactory.Instance.CreateLogger<BlockingCollectionAudioBuffer>();
            using var buffer = new BlockingCollectionAudioBuffer(logger);
            buffer.Add(new byte[] { 4 });
            buffer.CompleteAdding();

            // Attempt to add after CompleteAdding
            buffer.Add(new byte[] { 5 });

            Assert.True(buffer.IsAddingCompleted);

            // Consume existing
            Assert.True(buffer.TryTake(out var data, 100, CancellationToken.None));
            Assert.Equal(4, data[0]);

            // No more items
            Assert.False(buffer.TryTake(out _, 100, CancellationToken.None));
            Assert.True(buffer.IsCompleted);
        }

        [Fact]
        public void Dispose_StopsEnumerationAndNoFurtherItems()
        {
            var logger = NullLoggerFactory.Instance.CreateLogger<BlockingCollectionAudioBuffer>();
            var buffer = new BlockingCollectionAudioBuffer(logger);
            buffer.Add(new byte[] { 6 });
            buffer.CompleteAdding();
            buffer.Dispose();

            // Access underlying BlockingCollection via reflection
            var field = typeof(BlockingCollectionAudioBuffer)
                .GetField("_buffer", BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.NotNull(field);
            var inner = (BlockingCollection<byte[]>)(field.GetValue(buffer)!);

            // Enumeration should throw or yield no items
            Assert.Throws<ObjectDisposedException>(() =>
            {
                foreach (var _ in inner.GetConsumingEnumerable())
                {
                }
            });
        }
    }
}