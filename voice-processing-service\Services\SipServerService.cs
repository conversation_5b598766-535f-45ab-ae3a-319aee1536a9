using System;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Net;
using SIPSorcery.Media;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;
using SIPSorceryMedia.Abstractions;
using System.Security.Cryptography;
using SIPSorcery.Sys;
// Add caching for retransmitted INVITEs
using System.Collections.Concurrent;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace SIP serveru jako IHostedService.
    /// </summary>
    public class SipServerService : IHostedService
    {
        private readonly ILogger<SipServerService> _logger;
        private readonly ICallSessionManager _sessionManager;
        private readonly SipServerOptions _options;
        private readonly ILoggerFactory _loggerFactory;
        private readonly ISipRegistrationManager _registrationManager;
        private readonly ICallSynchronizationService _synchronizationService;
        // Tato továrna by neměla být používána, protože porty v SDP a náhodně vytvořené porty by byly jiné
        // Ponecháváme ji zde pouze pro kompatibilitu s existujícím kódem
         //private readonly Func<IAudioInputReceiver> _audioInputReceiverFactory;
         private readonly Func<string, IAudioProcessor> _audioProcessorFactory;
         private readonly Func<string, string, string, IAudioProcessor> _enhancedAudioProcessorFactory;
         private readonly IPortAllocator _portAllocator;
         private readonly IRtpAudioReceiverFactory _rtpAudioReceiverFactory;
        private SIPTransport? _sipTransport;
        private readonly ConcurrentDictionary<string, SIPResponse> _inviteResponseCache = new ConcurrentDictionary<string, SIPResponse>();
        private readonly ConcurrentDictionary<string, string> _callIdToTransactionKey = new ConcurrentDictionary<string, string>();
        // Proměnné pro aktuální hovor
        private SIPServerUserAgent? _userAgent;
        private VoIPMediaSession? _rtpSession;
        private readonly object _userAgentLock = new object();

        /// <summary>
        /// Creates a unique transaction key for SIP INVITE retransmission detection.
        /// Uses Call-ID + From tag + CSeq + Branch ID to distinguish between:
        /// - Retransmitted INVITE (same transaction) vs New concurrent call (different transaction)
        /// </summary>
        private string CreateTransactionKey(SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            var fromTag = sipRequest.Header.From.FromTag ?? "notag";
            var cseq = sipRequest.Header.CSeq;
            var branch = sipRequest.Header.Vias?.TopViaHeader?.Branch ?? "nobranch";

            return $"{callId}:{fromTag}:{cseq}:{branch}";
        }

        /// <summary>
        /// Detects whether an incoming INVITE is a re-INVITE (in-dialog request) or initial INVITE.
        /// According to SIP RFC 3261, a re-INVITE can be identified by:
        /// - Presence of a To tag in the request (indicates established dialog)
        /// - Same Call-ID as an existing dialog
        /// - Used for modifying session parameters (like changing media ports/codecs)
        /// </summary>
        /// <param name="sipRequest">The SIP INVITE request to analyze</param>
        /// <returns>True if this is a re-INVITE, false if it's an initial INVITE</returns>
        private bool IsReInvite(SIPRequest sipRequest)
        {
            try
            {
                // Check if To header has a tag - this is the primary indicator of a re-INVITE
                var toTag = sipRequest.Header.To?.ToTag;
                var hasToTag = !string.IsNullOrWhiteSpace(toTag);
                
                var callId = sipRequest.Header.CallId;
                var fromTag = sipRequest.Header.From?.FromTag ?? "notag";
                
                if (hasToTag)
                {
                    _logger.LogInformation($"[{callId}] Detected re-INVITE: To tag present ({toTag}), From tag ({fromTag})");
                    
                    // Additional validation: check if we have an existing session for this Call-ID
                    var existingSession = _sessionManager.GetSession(callId);
                    if (existingSession != null)
                    {
                        _logger.LogInformation($"[{callId}] Confirmed re-INVITE: existing session found");
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning($"[{callId}] To tag present but no existing session found - treating as re-INVITE anyway");
                        return true;
                    }
                }
                else
                {
                    _logger.LogDebug($"[{callId}] Detected initial INVITE: no To tag present, From tag ({fromTag})");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{sipRequest.Header.CallId}] Error detecting re-INVITE, treating as initial INVITE");
                return false;
            }
        }

        /// <summary>
        /// Sanitizes SDP string to fix Linux/Windows line ending and whitespace issues.
        /// Specifically addresses the c=IN IP4 line formatting problems seen in RHEL9 environments.
        /// Prevents IP address duplication and handles corrupted addresses with leading newlines.
        /// </summary>
        private string SanitizeSdpString(string sdpString, string expectedConnectionAddress, string callId)
        {
            try
            {
                // First sanitize the expected connection address itself
                var cleanConnectionAddress = expectedConnectionAddress?.Trim();
                if (string.IsNullOrWhiteSpace(cleanConnectionAddress))
                {
                    _logger.LogWarning($"[{callId}] Expected connection address is null/empty, using fallback");
                    cleanConnectionAddress = "127.0.0.1"; // Fallback
                }

                // Split into lines and process each line
                var lines = sdpString.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);
                var sanitizedLines = new List<string>();
                bool connectionLineProcessed = false;

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();

                    // Special handling for connection line (c=IN IP4)
                    if (trimmedLine.StartsWith("c=IN IP4"))
                    {
                        if (!connectionLineProcessed)
                        {
                            // Remove any extra whitespace and ensure proper format
                            var connectionLine = $"c=IN IP4 {cleanConnectionAddress}";
                            sanitizedLines.Add(connectionLine);
                            _logger.LogDebug($"[{callId}] Sanitized connection line: '{trimmedLine}' -> '{connectionLine}'");
                            connectionLineProcessed = true;
                        }
                        else
                        {
                            _logger.LogWarning($"[{callId}] Duplicate connection line detected and skipped: '{trimmedLine}'");
                        }
                    }
                    else if (!string.IsNullOrWhiteSpace(trimmedLine))
                    {
                        // For other lines, just trim whitespace
                        sanitizedLines.Add(trimmedLine);
                    }
                }

                // Join with proper CRLF line endings
                var result = string.Join("\r\n", sanitizedLines);

                // Ensure SDP ends with CRLF
                if (!result.EndsWith("\r\n"))
                {
                    result += "\r\n";
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error sanitizing SDP string, returning original");
                return sdpString;
            }
        }

        /// <summary>
        /// Extrahuje caller a called party informace z SIP INVITE požadavku pro session sharing.
        /// </summary>
        /// <param name="sipRequest">SIP INVITE požadavek.</param>
        /// <returns>Tuple obsahující (callerParty, calledParty).</returns>
        private (string callerParty, string calledParty) ExtractCallerCalledParty(SIPRequest sipRequest)
        {
            try
            {
                // Extract caller party from From header (remove SIP URI prefix and parameters)
                var callerParty = sipRequest.Header.From?.FromURI?.User ??
                                 sipRequest.Header.From?.FromURI?.ToString()?.Replace("sip:", "")?.Split('@')[0] ??
                                 "unknown";

                // Extract called party from To header (remove SIP URI prefix and parameters)
                var calledParty = sipRequest.Header.To?.ToURI?.User ??
                                 sipRequest.Header.To?.ToURI?.ToString()?.Replace("sip:", "")?.Split('@')[0] ??
                                 "unknown";

                _logger.LogInformation($"[{sipRequest.Header.CallId}] Extracted caller/called party: {callerParty} -> {calledParty}");
                return (callerParty, calledParty);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"[{sipRequest.Header.CallId}] Failed to extract caller/called party, using defaults");
                return ("unknown", "unknown");
            }
        }

        /// <summary>
        /// Vytvoří novou instanci SipServerService.
        /// </summary>
        /// <param name="logger">Logger.</param>
        /// <param name="sessionManager">Správce sessions hovorů.</param>
        /// <param name="options">Konfigurace SIP serveru.</param>
        /// <param name="registrationManager">Správce SIP registrací.</param>
        public SipServerService(
            ILogger<SipServerService> logger,
            ICallSessionManager sessionManager,
            IOptions<SipServerOptions> options,
            ILoggerFactory loggerFactory,
            IPortAllocator portAllocator,
            IRtpAudioReceiverFactory rtpAudioReceiverFactory,
            Func<string, IAudioProcessor> audioProcessorFactory,
            Func<string, string, string, IAudioProcessor> enhancedAudioProcessorFactory,
            ISipRegistrationManager registrationManager,
            ICallSynchronizationService synchronizationService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _sessionManager = sessionManager ?? throw new ArgumentNullException(nameof(sessionManager));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _portAllocator = portAllocator ?? throw new ArgumentNullException(nameof(portAllocator));
            _rtpAudioReceiverFactory = rtpAudioReceiverFactory ?? throw new ArgumentNullException(nameof(rtpAudioReceiverFactory));
            _audioProcessorFactory = audioProcessorFactory ?? throw new ArgumentNullException(nameof(audioProcessorFactory));
            _enhancedAudioProcessorFactory = enhancedAudioProcessorFactory ?? throw new ArgumentNullException(nameof(enhancedAudioProcessorFactory));
            _registrationManager = registrationManager ?? throw new ArgumentNullException(nameof(registrationManager));
            _synchronizationService = synchronizationService ?? throw new ArgumentNullException(nameof(synchronizationService));
        }

        /// <summary>
        /// Spustí SIP server.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting SIP server...");

            try
            {
                // Vytvoření SIP transportu
                _sipTransport = new SIPTransport();

                // Nastavení loggeru pro SIPSorcery
                SIPSorcery.LogFactory.Set(_loggerFactory);

                // Nastavení IP adresy pro naslouchání
                //IPAddress listenAddress = _options.ListenIpAddress == "Any" ? IPAddress.Any : IPAddress.Parse(_options.ListenIpAddress);

                // Přidání SIP kanálu pro naslouchání (pouze UDP, TLS bude implementován později)
                var sipChannel = new SIPUDPChannel(IPAddress.Any, _options.ListenPort);
                _sipTransport.AddSIPChannel(sipChannel);

                _logger.LogInformation($"SIP server listening on {System.Net.IPAddress.Any}:{_options.ListenPort}");

                // Registrace handleru pro SIP požadavky
                _sipTransport.SIPTransportRequestReceived += OnSipRequestReceived;

                // Poznámka: SIPServerUserAgent bude vytvořen pro každý příchozí hovor

                _logger.LogInformation("SIP server started successfully.");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting SIP server.");
                throw;
            }
        }

        /// <summary>
        /// Zastaví SIP server.
        /// </summary>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping SIP server...");

            try
            {
                // Ukončení všech aktivních hovorů
                foreach (var session in _sessionManager.GetAllSessions())
                {
                    try
                    {
                        _logger.LogInformation($"Terminating call {session.CallId}...");
                        await _sessionManager.TerminateSessionAsync(session.CallId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"Error terminating call {session.CallId}.");
                    }
                }

                // Odregistrace handleru pro SIP požadavky
                if (_sipTransport != null)
                {
                    // Handler je anonymní funkce, nelze odregistrovat
                    // Ale _sipTransport bude uvolněn, takže handler nebude volán
                }

                // Ukončení aktivního hovoru
                _userAgent?.Hangup(false);
                _rtpSession?.Close("Server shutdown");

                // Uvolnění SIP transportu
                _sipTransport?.Dispose();
                _sipTransport = null;

                _logger.LogInformation("SIP server stopped successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping SIP server.");
                throw;
            }
        }

        /// <summary>
        /// Handler pro příchozí SIP požadavky.
        /// </summary>
        private Task OnSipRequestReceived(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogDebug($"[{callId}] Received {sipRequest.Method} request from {remoteEndPoint}.");

            try
            {
                switch (sipRequest.Method)
                {
                    case SIPMethodsEnum.INVITE:
                        // Zpracování INVITE požadavku
                        _ = ProcessInviteAsync(localSIPEndPoint, remoteEndPoint, sipRequest);
                        break;

                    case SIPMethodsEnum.BYE:
                        // Zpracování BYE požadavku
                        _ = ProcessByeAsync(sipRequest);
                        break;

                    case SIPMethodsEnum.CANCEL:
                        // Zpracování CANCEL požadavku
                        _ = ProcessCancelAsync(sipRequest);
                        break;

                    case SIPMethodsEnum.OPTIONS:
                        // Odpověď 200 OK na OPTIONS požadavky
                        _ = Task.Run(async () => {
                            if (_sipTransport != null)
                            {
                                SIPResponse optionsResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                                await _sipTransport.SendResponseAsync(optionsResponse);
                                _logger.LogDebug($"[{callId}] Sent 200 OK response to OPTIONS request.");
                            }
                        });
                        break;

                    case SIPMethodsEnum.REGISTER:
                        // Zpracování REGISTER požadavku pomocí registration manageru
                        _ = ProcessRegisterAsync(sipRequest);
                        break;

                    case SIPMethodsEnum.SUBSCRIBE:
                        // Odpověď 405 Method Not Allowed na SUBSCRIBE požadavky
                        _ = Task.Run(async () => {
                            SIPResponse notAllowedResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.MethodNotAllowed, null);
                            await _sipTransport.SendResponseAsync(notAllowedResponse);
                            _logger.LogDebug($"[{callId}] Sent 405 Method Not Allowed response to SUBSCRIBE request.");
                        });
                        break;

                    default:
                        // Ostatní požadavky necháme zpracovat SIPSorcery
                        _logger.LogDebug($"[{callId}] Letting SIPSorcery handle {sipRequest.Method} request.");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing {sipRequest.Method} request.");
            }

            return Task.CompletedTask;
        }
        /// <summary>
        /// Zpracuje příchozí INVITE požadavek.
        /// </summary>
        private async Task ProcessInviteAsync(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;

            // Use synchronization service to prevent race conditions for the same Call-ID
            await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
            {
                _logger.LogDebug($"[{callId}] Acquired synchronization lock for INVITE processing");

                // Log complete SIP INVITE request for session sharing analysis
                _logger.LogInformation($"[{callId}] === SIP INVITE REQUEST ===");
                _logger.LogInformation($"[{callId}] From: {sipRequest.Header.From}");
                _logger.LogInformation($"[{callId}] To: {sipRequest.Header.To}");
                _logger.LogInformation($"[{callId}] Call-ID: {sipRequest.Header.CallId}");
                _logger.LogInformation($"[{callId}] Remote Endpoint: {remoteEndPoint}");
                _logger.LogInformation($"[{callId}] Local Endpoint: {localSIPEndPoint}");
                _logger.LogInformation($"[{callId}] User-Agent: {sipRequest.Header.UserAgent}");
                _logger.LogInformation($"[{callId}] Contact: {sipRequest.Header.Contact}");
                _logger.LogInformation($"[{callId}] Content-Type: {sipRequest.Header.ContentType}");
                _logger.LogInformation($"[{callId}] Content-Length: {sipRequest.Header.ContentLength}");
                if (!string.IsNullOrEmpty(sipRequest.Body))
                {
                    _logger.LogInformation($"[{callId}] SDP Body:\n{sipRequest.Body}");
                }
                _logger.LogInformation($"[{callId}] === END SIP INVITE REQUEST ===");

                // Detect if this is a re-INVITE or initial INVITE
                bool isReInvite = IsReInvite(sipRequest);
                
                if (isReInvite)
                {
                    _logger.LogInformation($"[{callId}] Processing re-INVITE for session modification");
                    await ProcessReInviteAsync(localSIPEndPoint, remoteEndPoint, sipRequest);
                    return;
                }

                _logger.LogInformation($"[{callId}] Processing initial INVITE for new session");

                // Create UASInviteTransaction and SIPServerUserAgent early so ACK matches.
                UASInviteTransaction uasTransaction = new UASInviteTransaction(_sipTransport, sipRequest, null);

                // Synchronize access to _userAgent to prevent concurrent call conflicts
                lock (_userAgentLock)
                {
                    _userAgent = new SIPServerUserAgent(_sipTransport, null, uasTransaction, null);
                    // Send immediate 100 Trying provisional response to prevent client retransmits
                    _userAgent.Progress(SIPResponseStatusCodesEnum.Trying, null, null, null, null);
                }
                // Placeholder session creation deferred until after SDP negotiation.

                var offerSdp = SDP.ParseSDPDescription(sipRequest.Body);
                var hasZeroPortOffer = offerSdp.Media.Any(m => m.Port == 0) || !offerSdp.Media.Any();
                if (hasZeroPortOffer)
                {
                    int allocatedZeroRtp = _options.RtpPortMin;
                    var okZero = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                    okZero.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                    okZero.Body = sipRequest.Body.Replace("m=audio 0", $"m=audio {allocatedZeroRtp}");
                    var transactionKey1 = CreateTransactionKey(sipRequest);
                    _inviteResponseCache[transactionKey1] = okZero;
                    _callIdToTransactionKey[callId] = transactionKey1;
                    await _sipTransport.SendResponseAsync(okZero);
                    return;
                }

                // Handle retransmitted INVITE by re-sending cached 200 OK
                // Use proper transaction key to distinguish retransmissions from concurrent calls
                var transactionKey = CreateTransactionKey(sipRequest);
                if (_inviteResponseCache.TryGetValue(transactionKey, out var cachedResponse))
                {
                    _logger.LogInformation($"[{callId}] Retransmitted INVITE detected (transaction: {transactionKey}), resending cached 200 OK.");
                    await _sipTransport.SendResponseAsync(cachedResponse);
                    return;
                }
                
                // Continue with existing INVITE processing (SDP negotiation, receiver binding, final session startup)
                await ProcessInviteNegotiationAsync(localSIPEndPoint, remoteEndPoint, sipRequest);
            });
        }

        /// <summary>
        /// Processes a re-INVITE request for session modification.
        /// Re-INVITEs are used to modify existing sessions (e.g., change codec, hold/unhold, etc.)
        /// without creating a new session.
        /// NOTE: This method is called from within ProcessInviteAsync which already holds the synchronization lock.
        /// </summary>
        /// <param name="localSIPEndPoint">Local SIP endpoint</param>
        /// <param name="remoteEndPoint">Remote SIP endpoint</param>
        /// <param name="sipRequest">The re-INVITE SIP request</param>
        private async Task ProcessReInviteAsync(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing re-INVITE for session modification (already synchronized)");

            try
            {
                // Check if we have an existing session
                var existingSession = _sessionManager.GetSession(callId);
                if (existingSession == null)
                {
                    _logger.LogWarning($"[{callId}] re-INVITE received but no existing session found - rejecting");
                    var notFoundResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.CallLegTransactionDoesNotExist, "No existing dialog");
                    await _sipTransport.SendResponseAsync(notFoundResponse);
                    return;
                }

                _logger.LogInformation($"[{callId}] Found existing session for re-INVITE, updating session parameters");

                // Handle retransmitted re-INVITE by re-sending cached response
                var transactionKey = CreateTransactionKey(sipRequest);
                if (_inviteResponseCache.TryGetValue(transactionKey, out var cachedResponse))
                {
                    _logger.LogInformation($"[{callId}] Retransmitted re-INVITE detected, resending cached response");
                    await _sipTransport.SendResponseAsync(cachedResponse);
                    return;
                }

                // Parse the SDP offer from re-INVITE
                var offerSdp = SDP.ParseSDPDescription(sipRequest.Body);
                
                _logger.LogInformation($"[{callId}] Updating existing session parameters based on re-INVITE offer");
                
                // Send 100 Trying
                var tryingResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Trying, null);
                await _sipTransport.SendResponseAsync(tryingResponse);

                // Update the existing session with new parameters
                await _sessionManager.UpdateSessionAsync(callId, sipRequest, offerSdp);

                // Get current session to access RTP ports for SDP answer
                var currentSession = existingSession;
                var currentRtpPort = currentSession.CurrentRtpPort;
                var currentRtcpPort = currentSession.CurrentRtcpPort;

                _logger.LogInformation($"[{callId}] Reusing existing ports: RTP={currentRtpPort}, RTCP={currentRtcpPort}");

                // Create SDP answer using existing RTP session but with updated parameters
                var answerSdp = _rtpSession?.CreateAnswer(null);
                if (answerSdp == null)
                {
                    _logger.LogError($"[{callId}] Cannot create SDP answer for re-INVITE - no active RTP session");
                    var errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "No active media session");
                    await _sipTransport.SendResponseAsync(errorResponse);
                    return;
                }

                // Update SDP answer to use the existing allocated ports
                var audioMedia = answerSdp.Media?.FirstOrDefault(m =>
                    m.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase));
                if (audioMedia != null && currentRtpPort > 0)
                {
                    audioMedia.Port = currentRtpPort;
                    _logger.LogInformation($"[{callId}] Updated SDP answer to use existing RTP port {currentRtpPort}");
                }

                // Override connection address if configured
                var overrideAddress = _options.OverrideSdpConnectionAddress?.Trim();
                var connectionAddress = !string.IsNullOrWhiteSpace(overrideAddress)
                    ? overrideAddress
                    : localSIPEndPoint.Address.ToString();
                
                answerSdp.Connection.ConnectionAddress = connectionAddress;

                // Convert to string and sanitize
                var sdpString = answerSdp.ToString();
                sdpString = SanitizeSdpString(sdpString, connectionAddress, callId);

                _logger.LogInformation($"[{callId}] Sending 200 OK response to re-INVITE with updated session parameters");
                _logger.LogDebug($"[{callId}] re-INVITE SDP answer: {sdpString}");

                // Send 200 OK response
                var okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                okResponse.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                okResponse.Body = sdpString;

                // Cache the response for potential retransmissions
                _inviteResponseCache[transactionKey] = okResponse;
                _callIdToTransactionKey[callId] = transactionKey;

                await _sipTransport.SendResponseAsync(okResponse);

                _logger.LogInformation($"[{callId}] re-INVITE processed successfully - session updated without creating new resources");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing re-INVITE");
                var errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                await _sipTransport.SendResponseAsync(errorResponse);
            }
        }

        /// <summary>
        /// Handles SDP negotiation, RTP binding, and session startup for an INVITE request.
        /// </summary>
        private async Task ProcessInviteNegotiationOldAsync(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing INVITE request from {remoteEndPoint}.");

            try
            {
                // Kontrola, zda je v INVITE nabídce kodek, který podporujeme
                var offerSdp = SDP.ParseSDPDescription(sipRequest.Body);
                var hasZeroPortOffer = offerSdp.Media.Any(m => m.Port == 0) || !offerSdp.Media.Any();
                IPEndPoint dstRtpEndPoint = SDP.GetSDPRTPEndPoint(sipRequest.Body);
                if (hasZeroPortOffer)
                {
                    var (rtpClientZero, rtcpClientZero) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                    int allocatedZeroRtp = ((IPEndPoint)rtpClientZero.Client.LocalEndPoint).Port;
                    var okZero = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                    okZero.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                    okZero.Body = sipRequest.Body.Replace("m=audio 0", $"m=audio {allocatedZeroRtp}");
                    var transactionKey = CreateTransactionKey(sipRequest);
                    _inviteResponseCache[transactionKey] = okZero;
                    _callIdToTransactionKey[callId] = transactionKey;
                    await _sipTransport.SendResponseAsync(okZero);
                    await _portAllocator.ReleasePortsAsync(callId, rtpClientZero, rtcpClientZero);
                    return;
                }

                // Vytvoření VoIPMediaSession pro zpracování RTP
                // Použijeme konstruktor s explicitním nastavením lokální IP adresy
                List<AudioCodecsEnum> codecs = new List<AudioCodecsEnum> { AudioCodecsEnum.PCMU, AudioCodecsEnum.PCMA, AudioCodecsEnum.G722 };

                var audioSource = AudioSourcesEnum.SineWave;

                AudioExtrasSource audioExtrasSource = new AudioExtrasSource(new AudioEncoder(), new AudioSourceOptions { AudioSource = audioSource });
                audioExtrasSource.RestrictFormats(formats => codecs.Contains(formats.Codec));
                var _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
                {
                    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
                    RtpPortRange = new PortRange(
                        (_options.RtpPortMin % 2 == 0 ? _options.RtpPortMin : _options.RtpPortMin + 1),
                        _options.RtpPortMax),
                    
                });

                // Nastavíme, aby VoIPMediaSession přijímala data z jakéhokoliv zdroje
                _rtpSession.AcceptRtpFromAny = true;
                _logger.LogInformation($"[{callId}] Created VoIPMediaSession with RTP starting at port {_options.RtpPortMin}");

                // Nastavení vzdáleného popisu z nabídky
                var setResult = _rtpSession.SetRemoteDescription(SdpType.offer, offerSdp);

                if (setResult != SetDescriptionResultEnum.OK)
                {
                    // Není podporovaný kodek nebo jiný problém s SDP
                    _logger.LogWarning($"[{callId}] Failed to set remote description: {setResult}");
                    SIPResponse noMatchingCodecResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.NotAcceptableHere, setResult.ToString());
                    await _sipTransport.SendResponseAsync(noMatchingCodecResponse);
                }
                else
                {
                    _logger.LogDebug($"[{callId}] Client offer contained supported audio codec.");

                    try
                    {
                        // Vytvoření SDP odpovědi pomocí VoIPMediaSession
                        // Reserve RTP/RTCP ports before building SDP answer
                        var (tempRtp, tempRtcp) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                        int reservedRtp = ((IPEndPoint)tempRtp.Client.LocalEndPoint).Port;
                        int reservedRtcp = ((IPEndPoint)tempRtcp.Client.LocalEndPoint).Port;
                        _logger.LogInformation($"[{callId}] Reserved RTP={reservedRtp}, RTCP={reservedRtcp}");
                        // Build SDP answer and override audio media port
                        var answerSdp = _rtpSession.CreateAnswer(null);
                        foreach (var media in answerSdp.Media)
                        {
                            if (media.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase))
                            {
                                media.Port = reservedRtp;
                                break;
                            }
                        }
                        // Override session-level connection address using SERVER's local SIP endpoint (CORRECTED)
                        var clientAddress = localSIPEndPoint.Address.ToString(); // FIXED: Should be server's address, not client's
                        answerSdp.Connection.ConnectionAddress = clientAddress;

                        _logger.LogInformation($"[{callId}] Created SDP answer");

                        // Log SDP details for debugging
                        _logger.LogDebug($"[{callId}] SDP answer: {answerSdp}");
                        
                        // Allocate RTP/RTCP ports for this call
                        (UdpClient rtpClient, UdpClient rtcpClient) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                        int allocatedRtp = ((IPEndPoint)rtpClient.Client.LocalEndPoint).Port;
                        int allocatedRtcp = ((IPEndPoint)rtcpClient.Client.LocalEndPoint).Port;

                        // Získáme port z SDP odpovědi

                        // Vytvoření SDP odpovědi pomocí VoIPMediaSession

                        _logger.LogInformation($"[{callId}] Created SDP answer");

                        // Log SDP details for debugging
                        _logger.LogDebug($"[{callId}] SDP answer: {answerSdp}");

                        try {
                            // Get RTP endpoints from the session - important for debugging
                            var destinationEndPoint = _rtpSession.AudioDestinationEndPoint;
                            if (destinationEndPoint != null) {
                                _logger.LogInformation($"[{callId}] RTP endpoint: {destinationEndPoint}, expecting data from {dstRtpEndPoint}");

                                // Logování více informací o SDP pro diagnostiku
                                _logger.LogInformation($"[{callId}] SDP connection info: {answerSdp.Connection}");
                                foreach (var media in answerSdp.Media)
                                {
                                    _logger.LogInformation($"[{callId}] SDP media: {media.Media} port {media.Port}");
                                }

                                // Explicitní logování rozdílu mezi endpointy
                                if (destinationEndPoint.ToString() != dstRtpEndPoint.ToString())
                                {
                                    _logger.LogWarning($"[{callId}] RTP endpoint mismatch: SIPSorcery expects {destinationEndPoint}, SDP indicates {dstRtpEndPoint}");
                                    _logger.LogInformation($"[{callId}] This is normal with NAT/port forwarding and should not cause issues as long as AcceptRtpFromAny is true");
                                }
                            }
                        } catch (Exception ex) {
                            _logger.LogWarning($"[{callId}] Failed to get RTP endpoints: {ex.Message}");
                        }

                         // Create RTP receiver with allocated ports and update SDP media port
                         _logger.LogInformation($"[{callId}] Creating RTP receiver with allocated ports: RTP={allocatedRtp}, RTCP={allocatedRtcp}");
                         var receiver = _rtpAudioReceiverFactory.CreateReceiver(callId, allocatedRtp, allocatedRtcp);
                         // Override SDP media port to allocated RTP port
                         foreach (var media in answerSdp.Media)
                         {
                             if (media.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase))
                             {
                                 media.Port = allocatedRtp;
                                 _logger.LogInformation($"[{callId}] Updated SDP media port to allocated port {allocatedRtp}");
                                 break;
                             }
                         }
                         // Extract caller/called party for session sharing
                         var (callerParty, calledParty) = ExtractCallerCalledParty(sipRequest);

                         Func<IAudioInputReceiver> inputReceiverFactory = () => receiver;
                         Func<IAudioProcessor> audioProcessorFactory = () => _enhancedAudioProcessorFactory(callId, callerParty, calledParty);

                        // Pokud je již aktivní hovor, ukončíme ho
                        if (_userAgent?.IsHungup == false)
                        {
                            _logger.LogWarning($"[{callId}] Hanging up existing call to accept new one.");
                            _userAgent.Hangup(false);
                            _rtpSession?.Close("New call");
                            await Task.Delay(100); // Dáme čas na ukončení
                        }

                        // Vytvoření UAS transakce a SIPServerUserAgent
                        UASInviteTransaction uasTransaction = new UASInviteTransaction(_sipTransport, sipRequest, null);
                        _userAgent = new SIPServerUserAgent(_sipTransport, null, uasTransaction, null);

                        // Registrace callbacku pro zrušení hovoru
                        _userAgent.CallCancelled += async (uasAgent, cancelReq) =>
                        {
                            _logger.LogInformation($"[{callId}] Call cancelled by client.");
                            _rtpSession?.Close("Call cancelled");
                            await _sessionManager.TerminateSessionAsync(callId);
                        };

                        // Registrace callbacku pro ukončení RTP
                        _rtpSession.OnRtpClosed += (reason) => _userAgent?.Hangup(false);

                        // Vytvoření session
                        var session = await _sessionManager.CreateSessionAsync(_userAgent, sipRequest, inputReceiverFactory, audioProcessorFactory);

                        // Odeslání odpovědi 100 Trying a 180 Ringing
                            _userAgent.Progress(SIPResponseStatusCodesEnum.Trying, null, null, null, null);
                        await Task.Delay(100);
                            _userAgent.Progress(SIPResponseStatusCodesEnum.Ringing, null, null, null, null);
                        await Task.Delay(100);

                        try {
                            // Get RTP endpoints from the session - important for debugging
                            var destinationEndPoint = _rtpSession.AudioDestinationEndPoint;
                            if (destinationEndPoint != null) {
                                _logger.LogInformation($"[{callId}] RTP endpoint: {destinationEndPoint}, expecting data from {dstRtpEndPoint}");

                                // Logování více informací o SDP pro diagnostiku
                                _logger.LogInformation($"[{callId}] SDP connection info: {answerSdp.Connection}");
                                foreach (var media in answerSdp.Media)
                                {
                                    _logger.LogInformation($"[{callId}] SDP media: {media.Media} port {media.Port}");
                                }

                                // Explicitní logování rozdílu mezi endpointy
                                if (destinationEndPoint.ToString() != dstRtpEndPoint.ToString())
                                {
                                    _logger.LogWarning($"[{callId}] RTP endpoint mismatch: SIPSorcery expects {destinationEndPoint}, SDP indicates {dstRtpEndPoint}");
                                    _logger.LogInformation($"[{callId}] This is normal with NAT/port forwarding and should not cause issues as long as AcceptRtpFromAny is true");
                                }
                            }
                        } catch (Exception ex) {
                            _logger.LogWarning($"[{callId}] Failed to get RTP endpoints: {ex.Message}");
                        }

                        // Odeslání odpovědi 200 OK s SDP
                        var sdpString = answerSdp.ToString().Replace("c=IN IP4 0.0.0.0", $"c=IN IP4 {clientAddress}");
                        _userAgent.Answer(SDP.SDP_MIME_CONTENTTYPE, sdpString, null, SIPDialogueTransferModesEnum.NotAllowed);
                        var okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                        okResponse.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                        okResponse.Body = sdpString;
                        var transactionKey = CreateTransactionKey(sipRequest);
                        _inviteResponseCache[transactionKey] = okResponse;
                        _callIdToTransactionKey[callId] = transactionKey;
                        await _sipTransport.SendResponseAsync(okResponse);
                        await _portAllocator.ReleasePortsAsync(callId, rtpClient, rtcpClient);

                        // Spuštění session
                        await session.StartAsync(CancellationToken.None);
                        await _rtpSession.Start();

                        // Log connection details after starting
                        _logger.LogInformation($"[{callId}] SIP and RTP sessions started successfully");
                        _logger.LogInformation($"[{callId}] Call from {sipRequest.Header.From.FromURI.User} established");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[{callId}] Error processing incoming call.");

                        // Odeslání chybové odpovědi
                        SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                        await _sipTransport.SendResponseAsync(errorResponse);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing INVITE request.");

                // Odeslání chybové odpovědi
                SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                await _sipTransport.SendResponseAsync(errorResponse);
            }
        }
/// <summary>
        /// Handles SDP negotiation, RTP binding, and session startup for an INVITE request.
        /// </summary>
        private async Task ProcessInviteNegotiationAsync(SIPEndPoint localSIPEndPoint, SIPEndPoint remoteEndPoint, SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing INVITE request from {remoteEndPoint}.");

            try
            {
                // Parse incoming SDP offer
                var offerSdp = SDP.ParseSDPDescription(sipRequest.Body);
                var hasZeroPortOffer = offerSdp.Media.Any(m => m.Port == 0) || !offerSdp.Media.Any();
                if (hasZeroPortOffer)
                {
                    var (rtp0, rtcp0) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                    int zeroPort = ((IPEndPoint)rtp0.Client.LocalEndPoint).Port;
                    var okZero = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                    okZero.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                    okZero.Body = sipRequest.Body.Replace("m=audio 0", $"m=audio {zeroPort}");
                    var transactionKey2 = CreateTransactionKey(sipRequest);
                    _inviteResponseCache[transactionKey2] = okZero;
                    _callIdToTransactionKey[callId] = transactionKey2;
                    await _sipTransport.SendResponseAsync(okZero);
                    await _portAllocator.ReleasePortsAsync(callId, rtp0, rtcp0);
                    return;
                }

                // Configure media session
                var codecs = new List<AudioCodecsEnum> { AudioCodecsEnum.PCMU, AudioCodecsEnum.PCMA, AudioCodecsEnum.G722 };
                var audioSource = new AudioExtrasSource(
                    new AudioEncoder(),
                    new AudioSourceOptions { AudioSource = AudioSourcesEnum.SineWave });
                audioSource.RestrictFormats(f => codecs.Contains(f.Codec));

                UdpClient rtpClient;
                UdpClient rtcpClient;
                int rtpPort, rtcpPort;
                try
                {
                    (rtpClient, rtcpClient) = await _portAllocator.AllocateRtpPairAsync(callId, localSIPEndPoint.Address);
                    rtpPort = ((IPEndPoint)rtpClient.Client.LocalEndPoint).Port;
                    rtcpPort = ((IPEndPoint)rtcpClient.Client.LocalEndPoint).Port;
                    _logger.LogInformation($"[{callId}] Reserved RTP={rtpPort}, RTCP={rtcpPort}");
                }
                catch (Exception ex) when (ex is InvalidOperationException || ex is SocketException)
                {
                    _logger.LogError(ex, $"[{callId}] Port allocation failed: {ex.Message}");
                    var errorResponse = SIPResponse.GetResponse(
                        sipRequest,
                        SIPResponseStatusCodesEnum.NotAcceptableHere,
                        "Failed to allocate RTP ports");
                    await _sipTransport.SendResponseAsync(errorResponse);
                    return;
                }
                _rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
                {
                    MediaEndPoint = new MediaEndPoints { AudioSource = audioSource }
                });
                _rtpSession.AcceptRtpFromAny = true;
                _logger.LogInformation($"[{callId}] Created VoIPMediaSession with RTP starting at port {_options.RtpPortMin}");

                var setResult = _rtpSession.SetRemoteDescription(SdpType.offer, offerSdp);
                if (setResult != SetDescriptionResultEnum.OK)
                {
                    _logger.LogWarning($"[{callId}] Failed to set remote description: {setResult}");
                    var reject = SIPResponse.GetResponse(
                        sipRequest,
                        SIPResponseStatusCodesEnum.NotAcceptableHere,
                        setResult.ToString());
                    await _sipTransport.SendResponseAsync(reject);
                    return;
                }

                _logger.LogDebug($"[{callId}] Client offer contained supported audio codec.");


                // Build and override SDP answer
                var answerSdp = _rtpSession.CreateAnswer(null);
                var audioMedia = answerSdp.Media.First(m =>
                    m.Media.ToString().Equals("audio", StringComparison.OrdinalIgnoreCase));
                audioMedia.Port = rtpPort;

                // Set connection address (SIPSorcery-specific: uses remote endpoint for proper RTP flow)
                var originalAddress = remoteEndPoint.Address.ToString();

                // Sanitize override address to remove leading/trailing whitespace and newlines
                var overrideAddress = _options.OverrideSdpConnectionAddress?.Trim();
                var connectionAddress = !string.IsNullOrWhiteSpace(overrideAddress)
                    ? overrideAddress
                    : originalAddress;

                _logger.LogDebug($"[{callId}] SDP connection address: {connectionAddress} (original: {originalAddress}, override: {_options.OverrideSdpConnectionAddress ?? "none"})");
                answerSdp.Connection.ConnectionAddress = connectionAddress;

                // Debug: Log SDP before modification
                var rawSdp = answerSdp.ToString();
                _logger.LogDebug($"[{callId}] Raw SDP before modification: {rawSdp.Replace("\r\n", "\\r\\n").Replace("\n", "\\n")}");

                // Modify SDP answer: set receive-only mode and remove SSRC line
                var lines = answerSdp.ToString().Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);
                var sdpString = string.Join("\r\n", lines
                    .Where(line => !line.StartsWith("a=ssrc:"))
                    .Select(line => line == "a=sendrecv" ? "a=recvonly" : line));

                // Sanitize SDP to fix Linux/Windows line ending and whitespace issues
                sdpString = SanitizeSdpString(sdpString, connectionAddress, callId);

                // Debug: Log SDP after modification and sanitization
                _logger.LogDebug($"[{callId}] Modified SDP: {sdpString.Replace("\r\n", "\\r\\n").Replace("\n", "\\n")}");

                _logger.LogInformation($"[{callId}] Created SDP answer");
                _logger.LogInformation($"[{callId}] SDP answer: {sdpString}");

                // Configure UDP buffers
                rtpClient.Client.ReceiveBufferSize = rtcpClient.Client.ReceiveBufferSize = 1048576;
                rtpClient.Client.SendBufferSize    = rtcpClient.Client.SendBufferSize    = 1048576;

                // Create receiver
                _logger.LogInformation($"[{callId}] Creating RTP receiver with allocated ports: RTP={rtpPort}, RTCP={rtcpPort}");
                var receiver = new RtpAudioReceiver(
                    callId, rtpClient, rtcpClient, _loggerFactory.CreateLogger<RtpAudioReceiver>());

                if (receiver.RtpLocalEndPoint.Port != rtpPort)
                {
                    audioMedia.Port = receiver.RtpLocalEndPoint.Port;
                    _logger.LogInformation($"[{callId}] Updated SDP media port to allocated port {receiver.RtpLocalEndPoint.Port}");
                }

                // Tear down existing
                if (_userAgent?.IsHungup == false)
                {
                    _userAgent.Hangup(false);
                    _rtpSession.Close("New call");
                    await Task.Delay(100);
                }

                // Setup UAS and callbacks
                // Reuse original _userAgent created earlier; no new transaction needed.
                _userAgent.CallCancelled += async (_, __) =>
                {
                    _rtpSession.Close("Cancelled");
                    await _sessionManager.TerminateSessionAsync(callId);
                };
                _rtpSession.OnRtpClosed += _ => _userAgent?.Hangup(false);

                // Extract caller/called party for session sharing
                var (callerParty, calledParty) = ExtractCallerCalledParty(sipRequest);

                // Create session
                var session = await _sessionManager.CreateSessionAsync(
                    _userAgent, sipRequest,
                    () => receiver,
                    () => _enhancedAudioProcessorFactory(callId, callerParty, calledParty));

                // 100 Trying: already sent immediately after server transaction creation in ProcessInviteAsync
                // 180 Ringing: informs caller that call is being processed
                lock (_userAgentLock)
                {
                    _userAgent.Progress(SIPResponseStatusCodesEnum.Ringing, null, null, null, null);
                }
                // Delay removed to speed setup

                // Send final 200 OK
                _userAgent.Answer(SDP.SDP_MIME_CONTENTTYPE, sdpString, null, SIPDialogueTransferModesEnum.NotAllowed);
                var okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, null);
                okResponse.Header.ContentType = SDP.SDP_MIME_CONTENTTYPE;
                okResponse.Body = sdpString;
                var transactionKey = CreateTransactionKey(sipRequest);
                _inviteResponseCache[transactionKey] = okResponse;
                _callIdToTransactionKey[callId] = transactionKey;
                await _sipTransport.SendResponseAsync(okResponse);

                await session.StartAsync(CancellationToken.None);
                await _rtpSession.Start();

                _logger.LogInformation($"[{callId}] SIP and RTP sessions started successfully");
                _logger.LogInformation($"[{callId}] Call from {sipRequest.Header.From.FromURI.User} established");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error in negotiation.");
                var error = SIPResponse.GetResponse(
                    sipRequest,
                    SIPResponseStatusCodesEnum.InternalServerError,
                    "Internal Server Error");
                await _sipTransport.SendResponseAsync(error);
            }
        }





        /// <summary>
        /// Zpracuje příchozí BYE požadavek.
        /// </summary>
        private async Task ProcessByeAsync(SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing BYE request.");

            // Use synchronization service to prevent race conditions during session termination
            await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
            {
                _logger.LogDebug($"[{callId}] Acquired synchronization lock for BYE processing");

                try
                {
                    // Odeslání odpovědi 200 OK
                    SIPResponse okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, "OK");
                    await _sipTransport.SendResponseAsync(okResponse);

                    // Ukončení hovoru
                    _userAgent?.Hangup(true);
                    _rtpSession?.Close("BYE received");

                    // Získání session
                    var session = _sessionManager.GetSession(callId);
                    if (session != null)
                    {
                        // Terminate session: cancels loops and disposes receiver
                        await _sessionManager.TerminateSessionAsync(callId);
                        _logger.LogInformation($"[{callId}] Session terminated.");
                        // Release RTP and RTCP ports
                        var rtpReceiver = session.AudioReceiver as RtpAudioReceiver;
                        if (rtpReceiver != null)
                        {
                            try
                            {
                                await _portAllocator.ReleasePortsAsync(callId, rtpReceiver.RtpClient, rtpReceiver.RtcpClient);
                                _logger.LogInformation($"[{callId}] RTP/RTCP ports released.");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, $"[{callId}] Failed to release RTP/RTCP ports.");
                            }
                        }
                        // Remove cached OK response for this call
                        if (_callIdToTransactionKey.TryRemove(callId, out var transactionKey))
                        {
                            _inviteResponseCache.TryRemove(transactionKey, out _);
                        }
                        // Clear user agent and RTP session for next call
                        _userAgent = null;
                        _rtpSession = null;
                    }
                    else
                    {
                        _logger.LogWarning($"[{callId}] Session not found for BYE request.");
                    }

                    // Clean up synchronization locks for this Call-ID
                    await _synchronizationService.CleanupLocksAsync(callId);
                    _logger.LogDebug($"[{callId}] Synchronization locks cleaned up");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{callId}] Error processing BYE request.");

                    // Odeslání chybové odpovědi
                    SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                    await _sipTransport.SendResponseAsync(errorResponse);
                }
            });
        }

        /// <summary>
        /// Zpracuje příchozí CANCEL požadavek.
        /// </summary>
        private async Task ProcessCancelAsync(SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;
            _logger.LogInformation($"[{callId}] Processing CANCEL request.");

            // Use synchronization service to prevent race conditions during session cancellation
            await _synchronizationService.ExecuteWithLockAsync(callId, async () =>
            {
                _logger.LogDebug($"[{callId}] Acquired synchronization lock for CANCEL processing");

                try
                {
                    // Odeslání odpovědi 200 OK
                    SIPResponse okResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.Ok, "OK");
                    await _sipTransport.SendResponseAsync(okResponse);

                    // Ukončení hovoru
                    _userAgent?.Hangup(true);
                    _rtpSession?.Close("CANCEL received");

                    // Získání session
                    var session = _sessionManager.GetSession(callId);
                    if (session != null)
                    {
                        // Terminate session: cancels loops and disposes receiver
                        await _sessionManager.TerminateSessionAsync(callId);
                        _logger.LogInformation($"[{callId}] Session terminated.");
                        // Release RTP and RTCP ports
                        var rtpReceiver = session.AudioReceiver as RtpAudioReceiver;
                        if (rtpReceiver != null)
                        {
                            try
                            {
                                await _portAllocator.ReleasePortsAsync(callId, rtpReceiver.RtpClient, rtpReceiver.RtcpClient);
                                _logger.LogInformation($"[{callId}] RTP/RTCP ports released.");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, $"[{callId}] Failed to release RTP/RTCP ports.");
                            }
                        }
                        // Remove cached OK response for this call
                        if (_callIdToTransactionKey.TryRemove(callId, out var transactionKey))
                        {
                            _inviteResponseCache.TryRemove(transactionKey, out _);
                        }
                        // Clear user agent and RTP session for next call
                        _userAgent = null;
                        _rtpSession = null;
                    }
                    else
                    {
                        _logger.LogWarning($"[{callId}] Session not found for CANCEL request.");
                    }

                    // Clean up synchronization locks for this Call-ID
                    await _synchronizationService.CleanupLocksAsync(callId);
                    _logger.LogDebug($"[{callId}] Synchronization locks cleaned up");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{callId}] Error processing CANCEL request.");

                    // Odeslání chybové odpovědi
                    SIPResponse errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                    if (_sipTransport != null)
                    {
                        await _sipTransport.SendResponseAsync(errorResponse);
                    }
                }
            });
        }

        /// <summary>
        /// Zpracuje SIP REGISTER požadavek.
        /// </summary>
        /// <param name="sipRequest">SIP REGISTER požadavek.</param>
        private async Task ProcessRegisterAsync(SIPRequest sipRequest)
        {
            var callId = sipRequest.Header.CallId;

            try
            {
                _logger.LogDebug($"[{callId}] Processing REGISTER request from {sipRequest.RemoteSIPEndPoint}");

                // Použití registration manageru pro zpracování registrace
                var response = await _registrationManager.ProcessRegistrationAsync(sipRequest);

                // Odeslání odpovědi
                if (_sipTransport != null)
                {
                    await _sipTransport.SendResponseAsync(response);
                    _logger.LogDebug($"[{callId}] Sent {response.StatusCode} response to REGISTER request.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{callId}] Error processing REGISTER request.");

                // Odeslání chybové odpovědi
                var errorResponse = SIPResponse.GetResponse(sipRequest, SIPResponseStatusCodesEnum.InternalServerError, "Internal Server Error");
                if (_sipTransport != null)
                {
                    await _sipTransport.SendResponseAsync(errorResponse);
                }
            }
        }
    }
}
