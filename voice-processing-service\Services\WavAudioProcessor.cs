using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// WAV writer with time-based and format-change-based segment rotation.
    /// Default input expected: G.711 μ-law frames at 8kHz mono (RTP payload type 0).
    /// </summary>
    public class WavAudioProcessor : IAudioProcessor
    {
        private readonly ILogger<WavAudioProcessor>? _logger;

        // Identity / naming
        private readonly string _callId;
        private readonly string? _baseDirectory; // when using templating
        private readonly string? _filenameTemplate; // relative path template when using templating
        private readonly bool _usingTemplate; // true if constructed with directory+template
        private readonly string _initialResolvedPath; // when constructed with explicit path (legacy)

        // Rotation and template state
        private readonly DateTime _utcStart;
        private int _segmentIndex = 1;
        private readonly TimeSpan _maxSegmentDuration;

        // Format signature (kept simple; extend when real format metadata is available)
        private struct FormatSignature : IEquatable<FormatSignature>
        {
            public ushort FormatTag;      // 7 = μ-law, 6 = A-law, 1 = PCM, etc.
            public ushort Channels;       // 1 mono, 2 stereo
            public int SampleRate;        // e.g., 8000
            public ushort BitsPerSample;  // e.g., 8

            public bool Equals(FormatSignature other) =>
                FormatTag == other.FormatTag
                && Channels == other.Channels
                && SampleRate == other.SampleRate
                && BitsPerSample == other.BitsPerSample;

            public override bool Equals(object obj) => obj is FormatSignature fs && Equals(fs);
            public override int GetHashCode() => HashCode.Combine(FormatTag, Channels, SampleRate, BitsPerSample);
        }

        private FormatSignature _currentSignature; // signature used for the currently open segment
        private FormatSignature _desiredSignature;  // desired/observed signature to align to (may differ after re-INVITE)

        // Options
        private readonly bool _splitOnReinvite;
        private readonly bool _safeFinalize;

        // Active segment state
        private BinaryWriter _wavWriter;
        private long _dataBytesWritten = 0;
        private DateTime _segmentOpenUtc;
        private string? _currentFilePath;

        // Backpressure/drops (lightweight counters)
        private long _droppedFrames = 0;
        private DateTime _lastDropLogUtc = DateTime.MinValue;

        // Processor identity
        private readonly string _processorIdBase;

        /// <inheritdoc/>
        public string ProcessorId => $"WAV_{_processorIdBase}";

        /// <summary>
        /// Legacy constructor (single resolved filepath). Rotation will append _seg-XXX for subsequent segments if needed.
        /// </summary>
        public WavAudioProcessor(string callId, string wavFilePath, ILogger<WavAudioProcessor>? logger)
        {
            if (string.IsNullOrWhiteSpace(callId)) throw new ArgumentNullException(nameof(callId));
            if (string.IsNullOrWhiteSpace(wavFilePath)) throw new ArgumentNullException(nameof(wavFilePath));
            _logger = logger;

            _callId = callId;
            _usingTemplate = false;
            _initialResolvedPath = wavFilePath;
            _baseDirectory = Path.GetDirectoryName(wavFilePath);
            _filenameTemplate = null;

            _utcStart = DateTime.UtcNow;
            // Defaults from options (Step 1 compat)
            _maxSegmentDuration = TimeSpan.FromSeconds(3600); // 1 hour
            _splitOnReinvite = true;
            _safeFinalize = true;

            // Default signature: G.711 μ-law, 8kHz, mono, 8 bits
            _currentSignature = new FormatSignature { FormatTag = 7, Channels = 1, SampleRate = 8000, BitsPerSample = 8 };
            _desiredSignature = _currentSignature;

            _processorIdBase = Path.GetFileNameWithoutExtension(wavFilePath);
            _logger?.LogInformation($"[{_callId}] WavAudioProcessor created for file: {wavFilePath}");
        }

        /// <summary>
        /// New constructor supporting templating and rotation.
        /// {callId}, {utcStart[:format]}, {date}, {segmentIndex[:format]} tokens are supported.
        /// </summary>
        public WavAudioProcessor(
            string callId,
            string baseDirectory,
            string filenameTemplate,
            DateTime utcStart,
            WavRecordingOptions options,
            ILogger<WavAudioProcessor>? logger)
        {
            if (string.IsNullOrWhiteSpace(callId)) throw new ArgumentNullException(nameof(callId));
            if (string.IsNullOrWhiteSpace(baseDirectory)) throw new ArgumentNullException(nameof(baseDirectory));
            if (string.IsNullOrWhiteSpace(filenameTemplate)) throw new ArgumentNullException(nameof(filenameTemplate));
            _logger = logger;

            _callId = callId;
            _baseDirectory = baseDirectory;
            _filenameTemplate = filenameTemplate;
            _usingTemplate = true;

            _utcStart = utcStart == default ? DateTime.UtcNow : utcStart;
            _segmentIndex = 1;

            // Options
            _maxSegmentDuration = TimeSpan.FromSeconds(Math.Max(1, options?.MaxFileDurationSeconds ?? 3600));
            _splitOnReinvite = options?.SplitOnReinvite ?? true;
            _safeFinalize = options?.SafeFinalize ?? true;

            // Default signature: G.711 μ-law, 8kHz, mono, 8 bits
            _currentSignature = new FormatSignature { FormatTag = 7, Channels = 1, SampleRate = 8000, BitsPerSample = 8 };
            _desiredSignature = _currentSignature;

            // Pre-compute ProcessorId base name from first segment filename
            var seg1Name = ResolveSegmentFullPath(1);
            _processorIdBase = Path.GetFileNameWithoutExtension(seg1Name);
            _initialResolvedPath = seg1Name; // only used for logging context
            _logger?.LogInformation($"[{_callId}] WavAudioProcessor created for template: baseDir='{_baseDirectory}', template='{_filenameTemplate}', utcStart={_utcStart}");
        }

        /// <inheritdoc/>
        public Task InitializeAsync(CancellationToken cancellationToken) => Task.CompletedTask;

        /// <inheritdoc/>
        public async Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            _logger?.LogInformation($"[{_callId}] Starting WAV processing for {ProcessorId}.");

            try
            {
                // Drain until buffer signals completion and no more data remains
                int consecutiveEmptyPolls = 0;
                while (true)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        _logger?.LogInformation($"[{_callId}] WAV processing cancellation requested for {ProcessorId}.");
                        break;
                    }

                    if (buffer.TryTake(out var audioData, 150, cancellationToken))
                    {
                        consecutiveEmptyPolls = 0;

                        // First frame lazily opens the segment
                        if (_wavWriter == null)
                        {
                            OpenSegment();
                        }

                        // Rotation conditions
                        RotateIfNeeded(_desiredSignature);

                        // Write frame
                        AppendAudioData(audioData);
                        continue;
                    }

                    // No data fetched within the timeout; exit when producer completed and queue is empty
                    if (buffer.IsCompleted || (buffer.IsAddingCompleted && ++consecutiveEmptyPolls >= 5))
                        break;
                }
            }
            catch (OperationCanceledException)
            {
                _logger?.LogInformation($"[{_callId}] WAV processing cancelled for {ProcessorId} while waiting for data.");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[{_callId}] Error during WAV processing for {ProcessorId}.");
            }
            finally
            {
                _logger?.LogInformation($"[{_callId}] Finishing WAV processing for {ProcessorId}. Finalizing segment.");
                // Ensure header-only WAV is emitted when no frames were written
                if (_wavWriter == null)
                {
                    OpenSegment();
                }
                FinalizeAndCloseSegment();
            }

            _logger?.LogInformation($"[{_callId}] WAV processing stopped for {ProcessorId}.");
            await Task.CompletedTask;
        }

        private void OpenSegment()
        {
            string path;
            if (_usingTemplate)
            {
                path = ResolveSegmentFullPath(_segmentIndex);
            }
            else
            {
                // Legacy: for first segment use the provided path, subsequent segments append _seg-XXX
                if (_segmentIndex == 1)
                {
                    path = _initialResolvedPath;
                }
                else
                {
                    var dir = Path.GetDirectoryName(_initialResolvedPath) ?? "";
                    var name = Path.GetFileNameWithoutExtension(_initialResolvedPath);
                    var ext = Path.GetExtension(_initialResolvedPath);
                    path = Path.Combine(dir, $"{name}_seg-{_segmentIndex:000}{ext}");
                }
            }

            try
            {
                var dirPath = Path.GetDirectoryName(path);
                if (!string.IsNullOrWhiteSpace(dirPath))
                    Directory.CreateDirectory(dirPath);

                var fs = new FileStream(
                    path,
                    FileMode.Create,
                    FileAccess.Write,
                    FileShare.Read,
                    bufferSize: 64 * 1024,
                    options: FileOptions.Asynchronous | FileOptions.SequentialScan);

                _wavWriter = new BinaryWriter(fs);
                WriteWavHeader(_wavWriter, 0, _currentSignature);
                _segmentOpenUtc = DateTime.UtcNow;
                _dataBytesWritten = 0;
                _currentFilePath = path;

                _logger?.LogInformation($"[{_callId}] WAV segment opened: {path} seg={_segmentIndex} fmt={_currentSignature.SampleRate}Hz/{_currentSignature.BitsPerSample}b/{_currentSignature.Channels}ch utcStart={_utcStart}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[{_callId}] Failed to open WAV segment file for segment {_segmentIndex}.");
                // Ensure no half-open writer remains
                try { _wavWriter?.Dispose(); } catch { /* ignore */ }
                _wavWriter = null;
            }
        }

        private void FinalizeAndCloseSegment()
        {
            if (_wavWriter == null) return;

            // Empty WAV files are intentionally retained for troubleshooting and diagnostics.
            if (_dataBytesWritten == 0 && _currentFilePath != null)
            {
                // Warning: Empty WAV file retained for analysis.
                _logger?.LogWarning($"[{_callId}] Empty WAV segment retained for diagnostics: {_currentFilePath} seg={_segmentIndex} bytes={_dataBytesWritten}. No audio data written. File kept for analysis.");
                try
                {
                    _wavWriter.Close();
                }
                catch { /* ignore */ }
                _wavWriter = null;
                return;
            }

            try
            {
                UpdateWavHeader(_wavWriter, _dataBytesWritten);
                _wavWriter.Flush();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, $"[{_callId}] WAV finalize failed: seg={_segmentIndex}");
                if (_safeFinalize)
                {
                    // swallow to keep pipeline resilient
                }
            }
            finally
            {
                try
                {
                    _wavWriter.Close(); // closes underlying stream
                }
                catch { /* ignore */ }
                _wavWriter = null;
            }

            var duration = DateTime.UtcNow - _segmentOpenUtc;
            _logger?.LogInformation($"[{_callId}] WAV segment closed: {_currentFilePath ?? "<unknown>"} seg={_segmentIndex} bytes={_dataBytesWritten} durationSec={duration.TotalSeconds}");
        }

        private void RotateIfNeeded(FormatSignature observedSignature)
        {
            // Format-change rotation: always rotate when signature differs and a segment is open
            if (_splitOnReinvite && _wavWriter != null && !observedSignature.Equals(_currentSignature))
            {
                var prev = _segmentIndex;
                var next = _segmentIndex + 1;
                var oldSig = _currentSignature;
                var newSig = observedSignature;

                _logger?.LogInformation(
                    $"[{_callId}] WAV rotate(format): seg={prev}->{next} oldFmt={oldSig.SampleRate}/{oldSig.BitsPerSample}/{oldSig.Channels} newFmt={newSig.SampleRate}/{newSig.BitsPerSample}/{newSig.Channels}");

                FinalizeAndCloseSegment();
                _currentSignature = observedSignature;
                _segmentIndex++;
                OpenSegment();
                return;
            }

            // Time-based rotation
            if (_wavWriter != null && (DateTime.UtcNow - _segmentOpenUtc) >= _maxSegmentDuration)
            {
                var prev = _segmentIndex;
                var next = _segmentIndex + 1;
                var duration = DateTime.UtcNow - _segmentOpenUtc;

                _logger?.LogInformation(
                    $"[{_callId}] WAV rotate(time): seg={prev}->{next} durationSec={duration.TotalSeconds}");

                FinalizeAndCloseSegment();
                _segmentIndex++;
                OpenSegment();
            }
        }

        private void AppendAudioData(byte[] audioData)
        {
            if (_wavWriter == null) return;

            try
            {
                _wavWriter.Write(audioData);
                _dataBytesWritten += audioData.Length;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[{_callId}] Failed to write audio data to WAV file on segment #{_segmentIndex}.");
                // Fail fast on IO errors for this segment
                FinalizeAndCloseSegment();
            }
        }

        // WAV header helpers respecting the active format signature
        private static void WriteWavHeader(BinaryWriter writer, long audioDataSize, FormatSignature sig)
        {
            writer.Write(Encoding.ASCII.GetBytes("RIFF"));
            writer.Write((uint)(36 + audioDataSize)); // ChunkSize
            writer.Write(Encoding.ASCII.GetBytes("WAVE"));

            writer.Write(Encoding.ASCII.GetBytes("fmt "));
            writer.Write(16); // Subchunk1Size for PCM/G711
            writer.Write((ushort)sig.FormatTag); // AudioFormat (7 = μ-law, 6 = A-law, 1 = PCM)
            writer.Write((ushort)sig.Channels);  // NumChannels
            writer.Write(sig.SampleRate);        // SampleRate

            // ByteRate and BlockAlign: for G711 8-bit mono at 8k: ByteRate=8000, BlockAlign=1
            int bytesPerSamplePerChan = sig.BitsPerSample / 8;
            int blockAlign = bytesPerSamplePerChan * sig.Channels;
            int byteRate = sig.SampleRate * blockAlign;

            writer.Write(byteRate);              // ByteRate
            writer.Write((ushort)blockAlign);    // BlockAlign
            writer.Write((ushort)sig.BitsPerSample); // BitsPerSample

            writer.Write(Encoding.ASCII.GetBytes("data"));
            writer.Write((uint)audioDataSize);
        }

        private static void UpdateWavHeader(BinaryWriter writer, long audioDataSize)
        {
            if (writer?.BaseStream == null || !writer.BaseStream.CanSeek) return;

            writer.BaseStream.Seek(4, SeekOrigin.Begin);
            writer.Write((uint)(36 + audioDataSize));
            writer.BaseStream.Seek(40, SeekOrigin.Begin);
            writer.Write((uint)audioDataSize);
            writer.Flush();
        }

        // Template rendering used when constructed with templating-enabled constructor
        private string ResolveSegmentFullPath(int segmentIndex)
        {
            var relative = RenderTemplate(_filenameTemplate!, _callId, _utcStart, segmentIndex);
            // Normalize to be relative under base dir
            relative = relative.TrimStart(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
            return Path.Combine(_baseDirectory!, relative);
        }

        private static string RenderTemplate(string template, string callId, DateTime utcStart, int segmentIndex)
        {
            // Very small templater (regex avoided here to keep this class independent)
            string rendered = template;

            // {utcStart[:format]}
            rendered = System.Text.RegularExpressions.Regex.Replace(rendered, @"\{utcStart(?::([^}]+))?\}", m =>
            {
                var fmt = m.Groups.Count > 1 ? m.Groups[1].Value : null;
                return string.IsNullOrEmpty(fmt) ? utcStart.ToString("yyyyMMdd'T'HHmmss'Z'") : utcStart.ToString(fmt);
            });

            // {date}
            rendered = rendered.Replace("{date}", utcStart.ToString("yyyyMMdd"));

            // {callId}
            rendered = rendered.Replace("{callId}", callId);

            // {segmentIndex[:format]}
            rendered = System.Text.RegularExpressions.Regex.Replace(rendered, @"\{segmentIndex(?::([^}]+))?\}", m =>
            {
                var fmt = m.Groups.Count > 1 ? m.Groups[1].Value : null;
                return string.IsNullOrEmpty(fmt) ? segmentIndex.ToString() : segmentIndex.ToString(fmt);
            });

            return rendered;
        }

        /// <summary>
        /// Notifies the processor that the input audio format has changed.
        /// When SplitOnReinvite is enabled and a segment is open, this will rotate to a new segment
        /// and use the new format signature for the WAV header.
        /// </summary>
        /// <param name="formatTag">WAVE format tag (e.g., 7 = μ-law, 6 = A-law, 1 = PCM).</param>
        /// <param name="channels">Number of channels.</param>
        /// <param name="sampleRate">Samples per second.</param>
        /// <param name="bitsPerSample">Bits per sample.</param>
        public void NotifyFormatChange(ushort formatTag, ushort channels, int sampleRate, ushort bitsPerSample)
        {
            var observed = new FormatSignature
            {
                FormatTag = formatTag,
                Channels = channels,
                SampleRate = sampleRate,
                BitsPerSample = bitsPerSample
            };

            _desiredSignature = observed;
            RotateIfNeeded(observed);
        }

        // Backpressure hook: currently unused. Increments dropped frames counter and periodically logs a warning.
        private void RegisterFrameDrop(int count = 1)
        {
            if (count <= 0) return;
            Interlocked.Add(ref _droppedFrames, count);

            var now = DateTime.UtcNow;
            if ((now - _lastDropLogUtc) >= TimeSpan.FromSeconds(10))
            {
                _lastDropLogUtc = now;
                var total = Interlocked.Read(ref _droppedFrames);
                _logger?.LogWarning($"[{_callId}] WAV frames dropped due to backpressure: total={total}");
            }
        }

        /// <inheritdoc/>
        public void Dispose()
        {
            _logger?.LogInformation($"[{_callId}] Disposing WavAudioProcessor for {ProcessorId}.");
            FinalizeAndCloseSegment();
            GC.SuppressFinalize(this);
        }
    }
}
