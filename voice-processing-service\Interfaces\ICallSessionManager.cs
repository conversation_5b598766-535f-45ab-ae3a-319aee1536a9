using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SIPSorcery.SIP;
using SIPSorcery.SIP.App;
using SIPSorcery.Net;

namespace voice_processing_service.Interfaces
{
    /// <summary>
    /// Rozhraní pro správce sessions hovorů.
    /// </summary>
    public interface ICallSessionManager
    {
        /// <summary>
        /// Vytvoří novou session pro hovor.
        /// </summary>
        /// <param name="userAgent">SIP User Agent pro tento hovor.</param>
        /// <param name="inviteRequest">INVITE požadavek, který zahájil hovor.</param>
        /// <param name="inputReceiverFactory">Tovární metoda pro vytvoření přijímače audio dat.</param>
        /// <param name="audioProcessorFactory">Tovární metoda pro vytvoření procesoru audio dat.</param>
        /// <returns>Task reprezentující asynchronní operaci, která vrací vytvořenou session.</returns>
        Task<ICallSession> CreateSessionAsync(
            SIPServerUserAgent userAgent,
            SIPRequest inviteRequest,
            Func<IAudioInputReceiver> inputReceiverFactory,
            Func<IAudioProcessor> audioProcessorFactory);

        /// <summary>
        /// Získá session podle identifikátoru hovoru.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <returns>Session hovoru, nebo null, pokud session neexistuje.</returns>
        ICallSession GetSession(string callId);

        /// <summary>
        /// Ukončí session hovoru.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task TerminateSessionAsync(string callId);

        /// <summary>
        /// Aktualizuje existující session na základě re-INVITE požadavku.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru (Call-ID).</param>
        /// <param name="reInviteRequest">Re-INVITE SIP požadavek.</param>
        /// <param name="newSdpOffer">Nová SDP nabídka.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        Task UpdateSessionAsync(string callId, SIPRequest reInviteRequest, SDP newSdpOffer);

        /// <summary>
        /// Získá všechny aktivní session hovorů.
        /// </summary>
        /// <returns>Kolekce aktivních session hovorů.</returns>
        IEnumerable<ICallSession> GetAllSessions();
    }
}
