# Aktualizovaný plán implementace

Tento dokument popisuje celkový plán implementace projektu s využitím existující struktury v adres<PERSON>ři `voice-processing-service`. Ka<PERSON>d<PERSON> úkol je navr<PERSON>en tak, aby mohl být realizován izolovaně, ale zá<PERSON>ň navazoval na předchozí úkoly a přidával postupně business funkcionality.

## Fáze implementace

1. **Rozšíření existující struktury projektu** - [01_project_structure_extension.md](01_project_structure_extension.md)
   - Vytvoření adres<PERSON>řov<PERSON> struktury (Interfaces, Services, Configuration)
   - Definice rozhraní a základních tříd
   - Rozšíření DI kontejneru

2. **Implementace audio bufferu** - [02_audio_buffer_implementation.md](02_audio_buffer_implementation.md)
   - Implementace thread-safe bufferu pro audio data
   - Testování bufferu

3. **Implementace RTP přijímače** - [03_rtp_receiver_implementation.md](03_rtp_receiver_implementation.md)
   - Implementace přijímače RTP paketů
   - Parsování RTP dat a extrakce audio dat
   - Testování RTP přijímače

4. **Implementace WAV procesoru** - [04_wav_processor_implementation.md](04_wav_processor_implementation.md)
   - Implementace procesoru pro ukládání audio dat do WAV souboru
   - Testování WAV procesoru

5. **Implementace správy hovorů** - [05_call_session_management.md](05_call_session_management.md)
   - Implementace CallSession a CallSessionManager
   - Propojení komponent (RTP přijímač, buffer, WAV procesor)
   - Testování správy hovorů

6. **Implementace SIP serveru** - [06_sip_server_implementation.md](06_sip_server_implementation.md)
   - Implementace SIP serveru jako IHostedService
   - Zpracování SIP požadavků (INVITE, BYE, CANCEL)
   - Testování SIP serveru

7. **Implementace simulátoru pro testování** - [07_testing_simulator.md](07_testing_simulator.md)
   - Implementace nástroje pro simulaci SIP klienta
   - Možnost streamování WAV souboru přes RTP
   - E2E testování celého řešení

8. **Rozšíření o STT procesor** - [08_stt_processor_implementation.md](08_stt_processor_implementation.md)
   - Implementace procesoru pro převod řeči na text
   - Integrace s existujícím řešením
   - Testování STT procesoru

## Testovací strategie

Každý úkol obsahuje vlastní testovací scénáře, které jsou zaměřeny na testování konkrétní funkcionality. Testování bude probíhat na několika úrovních:

1. **Unit testy** - testování jednotlivých komponent izolovaně
2. **Integrační testy** - testování spolupráce mezi komponentami
3. **E2E testy** - testování celého řešení pomocí simulátoru

## Závislosti mezi úkoly

Úkoly jsou navrženy tak, aby mohly být implementovány postupně, ale některé úkoly mají závislosti na předchozích úkolech:

- Úkol 3 (RTP přijímač) závisí na úkolu 2 (Audio buffer)
- Úkol 4 (WAV procesor) závisí na úkolu 2 (Audio buffer)
- Úkol 5 (Správa hovorů) závisí na úkolech 2, 3 a 4
- Úkol 6 (SIP server) závisí na úkolu 5
- Úkol 7 (Simulátor) může být implementován nezávisle, ale pro E2E testování je potřeba mít implementované úkoly 1-6
- Úkol 8 (STT procesor) závisí na úkolech 2 a 5

## Změny oproti původnímu plánu

Tento plán je aktualizovanou verzí původního plánu, která bere v úvahu existující strukturu projektu v adresáři `voice-processing-service`. Hlavní změny jsou:

1. Využití existující .NET Core struktury boilerplate
2. Přizpůsobení implementace existujícímu projektu
3. Zachování stejných logických celků a postupného přidávání business funkcionalit
