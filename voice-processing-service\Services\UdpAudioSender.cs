using System;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Třída pro odesílání audio dat přes UDP.
    /// </summary>
    public class UdpAudioSender : IDisposable
    {
        private readonly UdpClient _udpClient;
        private readonly IPEndPoint _remoteEndPoint;
        private readonly ILogger _logger;
        private bool _isDisposed = false;

        /// <summary>
        /// Vytvoří novou instanci UdpAudioSender.
        /// </summary>
        /// <param name="host">Cílová adresa.</param>
        /// <param name="port">Cílový port.</param>
        /// <param name="logger">Logger.</param>
        public UdpAudioSender(string host, int port, ILogger logger)
        {
            _udpClient = new UdpClient();
            _remoteEndPoint = new IPEndPoint(IPAddress.Parse(host), port);
            _logger = logger;
            _logger.LogInformation($"UdpAudioSender created for {host}:{port}");
        }

        /// <summary>
        /// Odešle audio data přes UDP.
        /// </summary>
        /// <param name="audioData">Audio data k odeslání.</param>
        /// <param name="cancellationToken">Token pro zrušení operace.</param>
        /// <returns>Task reprezentující asynchronní operaci.</returns>
        public async Task SendAudioAsync(byte[] audioData, CancellationToken cancellationToken)
        {
            if (audioData == null || audioData.Length == 0 || _isDisposed)
            {
                return;
            }

            try
            {
                await _udpClient.SendAsync(audioData, audioData.Length, _remoteEndPoint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending audio data over UDP.");
            }
        }

        /// <summary>
        /// Uvolní prostředky.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
            {
                return;
            }

            _logger.LogInformation("Disposing UdpAudioSender.");
            _udpClient.Close();
            _udpClient.Dispose();
            _isDisposed = true;
            GC.SuppressFinalize(this);
        }
    }
}
