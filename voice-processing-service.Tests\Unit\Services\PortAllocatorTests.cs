using System.Net;
using System.Net.Sockets;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Services;
using Xunit;

namespace voice_processing_service.Tests.Unit.Services
{
    public class PortAllocatorTests
    {
        [Fact]
        public async void AllocateRtpPairAsync_BindsSequentialPorts()
        {
            var opts = new SipServerOptions { RtpPortMin = 20000, RtpPortMax = 20004 };
            var options = Options.Create(opts);
            var allocator = new PortAllocator(options, NullLogger<PortAllocator>.Instance);

            var (rtp, rtcp) = await allocator.AllocateRtpPairAsync("test", IPAddress.Loopback);
            Assert.NotNull(rtp);
            Assert.NotNull(rtcp);

            var rtpEP = (IPEndPoint)rtp.Client.LocalEndPoint;
            var rtcpEP = (IPEndPoint)rtcp.Client.LocalEndPoint;

            Assert.Equal(opts.RtpPortMin, rtpEP.Port);
            Assert.Equal(opts.RtpPortMin + 1, rtcpEP.Port);
        }

        [Fact]
        public async void AllocateRtpPairAsync_FallsBackToEphemeralWhenRangeExhausted()
        {
            var opts = new SipServerOptions { RtpPortMin = 21000, RtpPortMax = 21002 };
            var options = Options.Create(opts);
            var allocator = new PortAllocator(options, NullLogger<PortAllocator>.Instance);

            // Occupy all ports in range
            using var b1 = new UdpClient(new IPEndPoint(IPAddress.Loopback, 21000));
            using var b2 = new UdpClient(new IPEndPoint(IPAddress.Loopback, 21001));
            using var b3 = new UdpClient(new IPEndPoint(IPAddress.Loopback, 21002));

            var (rtp, rtcp) = await allocator.AllocateRtpPairAsync("test", IPAddress.Loopback);
            IPEndPoint rtpEp = (IPEndPoint)rtp.Client.LocalEndPoint;
            IPEndPoint rtcpEp = (IPEndPoint)rtcp.Client.LocalEndPoint;
            Assert.NotNull(rtp);
            Assert.NotNull(rtcp);

            var rtpPort = ((IPEndPoint)rtp.Client.LocalEndPoint).Port;
            var rtcpPort = ((IPEndPoint)rtcp.Client.LocalEndPoint).Port;

            Assert.NotInRange(rtpPort, opts.RtpPortMin, opts.RtpPortMax);
            Assert.NotInRange(rtcpPort, opts.RtpPortMin, opts.RtpPortMax);
            Assert.NotEqual(rtpPort, opts.RtpPortMin);
            Assert.Equal(rtpPort + 1, rtcpPort);
        }

        [Fact]
        public async void ReleasePortsAsync_ClosesClients()
        {
            var opts = new SipServerOptions { RtpPortMin = 22000, RtpPortMax = 22002 };
            var options = Options.Create(opts);
            var allocator = new PortAllocator(options, NullLogger<PortAllocator>.Instance);

            var (rtp, rtcp) = await allocator.AllocateRtpPairAsync("test", IPAddress.Loopback);
            // Capture the bound endpoints before releasing
            IPEndPoint rtpEp = (IPEndPoint)rtp.Client.LocalEndPoint;
            IPEndPoint rtcpEp = (IPEndPoint)rtcp.Client.LocalEndPoint;
            await allocator.ReleasePortsAsync("test", rtp, rtcp);

            // After release, binding on same ports should succeed
            using (new UdpClient(new IPEndPoint(IPAddress.Loopback, rtpEp.Port)))
            using (new UdpClient(new IPEndPoint(IPAddress.Loopback, rtcpEp.Port)))
{
            }
        }

        [Fact]
        public async void AllocateSpecificPairAsync_BindsRequestedPorts()
        {
            var opts = new SipServerOptions { RtpPortMin = 50000, RtpPortMax = 50001 };
            var options = Options.Create(opts);
            var allocator = new PortAllocator(options, NullLogger<PortAllocator>.Instance);

            var (rtp, rtcp) = await allocator.AllocateSpecificPairAsync("call1", IPAddress.Loopback, 50000, 50001);

            var rtpEP = (IPEndPoint)rtp.Client.LocalEndPoint;
            var rtcpEP = (IPEndPoint)rtcp.Client.LocalEndPoint;

            Assert.Equal(50000, rtpEP.Port);
            Assert.Equal(50001, rtcpEP.Port);

            rtp.Dispose();
            rtcp.Dispose();
        }
    }
}