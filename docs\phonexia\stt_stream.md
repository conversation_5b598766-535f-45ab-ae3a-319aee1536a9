
# Stream API Commands

## `/input_stream/rtp` – Input RTP stream

### `POST /input_stream/rtp` Open input RTP stream

Opens input RTP stream. Stream is opened on port, which is returned in result.

#### Parameters

| Name | Parameter Type | Data Type | Required | Description                                                                                                                            |
| :--- | :------------- | :-------- | :------- | :------------------------------------------------------------------------------------------------------------------------------------- |
| path | query          | string    | no       | Path to file where streamed data will be saved. If path is not set file will not be saved. File will be overwritten if already exists. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X POST "SERVER_ADDRESS:PORT/input_stream/rtp"
```


#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 | [JSON](#) [XML](#)                                                                          |
|                        | ```json                                                                                     |
|                        | {                                                                                           |
|                        | "result": {                                                                                 |
|                        | "version": 1,                                                                               |
|                        | "name": "RtpInputStreamResult",                                                             |
|                        | "port": "5000",                                                                             |
|                        | "input_stream": "ec563083-3d9b-457d-a0ac-24b197bc222f"                                      |
|                        | }                                                                                           |
|                        | }                                                                                           |
|                        | ```                                                                                         |
|                        | ```xml                                                                                      |
|                        | <?xml version="1.0" encoding="UTF-8"?>                                                      |
|                        | <result>                                                                                    |
|                        | <version>1</version>                                                                        |
|                        | <name>RtpInputStreamResult</name>                                                           |
|                        | <port>5000</port>                                                                           |
|                        | <input_stream>a869ea70-980a-4497-99b4-3ef181f0b87d</input_stream>                           |
|                        | </result>                                                                                   |
|                        | ```                                                                                         |
| 400 Bad Request        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 403 Forbidden          | \* `1026` RTP sessions limit exceeded                                                       |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

### `DELETE /input_stream/rtp` Close opened input RTP stream

Closes opened input RTP stream.

#### Parameters

| Name         | Parameter Type | Data Type | Required | Description                    |
| :----------- | :------------- | :-------- | :------- | :----------------------------- |
| input_stream | query          | string    | yes      | ID of opened input RTP stream. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X DELETE "SERVER_ADDRESS:PORT/input_stream/rtp?input_stream=OPENED_RTP_STREAM"
```

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 | [JSON](#) [XML](#)                                                                          |
|                        | ```json                                                                                     |
|                        | {                                                                                           |
|                        | "result": {                                                                                 |
|                        | "version": 1,                                                                               |
|                        | "name": "RtpInputStreamDeleteResult",                                                       |
|                        | "number_of_received_packets": "100",                                                        |
|                        | "number_of_lost_packets": "1",                                                              |
|                        | "packet_loss": "1.0",                                                                       |
|                        | "jitter": "10.24"                                                                           |
|                        | }                                                                                           |
|                        | }                                                                                           |
|                        | ```                                                                                         |
|                        | ```xml                                                                                      |
|                        | <?xml version="1.0" encoding="UTF-8"?>                                                      |
|                        | <result>                                                                                    |
|                        | <version>1</version>                                                                        |
|                        | <name>RtpInputStreamDeleteResult</name>                                                     |
|                        | <number_of_received_packets>100</number_of_received_packets>                                |
|                        | <number_of_lost_packets>1</number_of_lost_packets>                                          |
|                        | <packet_loss>1.0</packet_loss>                                                              |
|                        | <jitter>10.24</jitter>                                                                      |
|                        | </result>                                                                                   |
|                        | ```                                                                                         |
|                        | **Result description**                                                                      |
|                        | **number_of_received_packets** number of received packets.                                  |
|                        | **number_of_lost_packets** number of lost packes.                                           |
|                        | **packet_loss** packet loss as percentage.                                                  |
|                        | **jitter** jitter in milliseconds.                                                          |
| 400 Bad Request        | \* `1003` A required query parameter was not specified for this request                     |
|                        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 404 Not Found          | \* `1030` Stream not found                                                                  |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

## `/input_stream/rtp/info` – Information about input RTP stream

### `GET /input_stream/rtp/info` Information about input RTP stream

Return information about the input RTP stream such as source address, target address, and transfer statistics.

#### Parameters

| Name         | Parameter Type | Data Type | Required | Description                    |
| :----------- | :------------- | :-------- | :------- | :----------------------------- |
| input_stream | query          | string    | yes      | ID of opened input RTP stream. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X GET "SERVER_ADDRESS:PORT/input_stream/rtp/info?input_stream=OPENED_RTP_STREAM"
```

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 | [JSON](#) [XML](#)                                                                          |
|                        | ```json                                                                                     |
|                        | {                                                                                           |
|                        | "result": {                                                                                 |
|                        | "version": 1,                                                                               |
|                        | "name": "RtpInputStreamInfoResult",                                                         |
|                        | "source_address": {                                                                         |
|                        | "ip": "127.0.0.1",                                                                          |
|                        | "port": 54271                                                                               |
|                        | },                                                                                          |
|                        | "target_address": {                                                                         |
|                        | "ip": "0.0.0.0",                                                                            |
|                        | "port": 10001                                                                               |
|                        | },                                                                                          |
|                        | "statistics": {                                                                             |
|                        | "number_of_received_packets": 631,                                                          |
|                        | "number_of_lost_packets": 0,                                                                |
|                        | "packet_loss": 0,                                                                           |
|                        | "jitter": 3.0381560121013047                                                                |
|                        | }                                                                                           |
|                        | }                                                                                           |
|                        | }                                                                                           |
|                        | ```                                                                                         |
|                        | ```xml                                                                                      |
|                        | <?xml version="1.0" encoding="UTF-8"?>                                                      |
|                        | <result>                                                                                    |
|                        | <version>1</version>                                                                        |
|                        | <name>RtpInputStreamInfoResult</name>                                                       |
|                        | <source_address>                                                                            |
|                        | <ip>127.0.0.1</ip>                                                                          |
|                        | <port>58281</port>                                                                          |
|                        | </source_address>                                                                           |
|                        | <target_address>                                                                            |
|                        | <ip>0.0.0.0</ip>                                                                            |
|                        | <port>10001</port>                                                                          |
|                        | </target_address>                                                                           |
|                        | <statistics>                                                                                |
|                        | <number_of_received_packets>353</number_of_received_packets>                                |
|                        | <number_of_lost_packets>0</number_of_lost_packets>                                          |
|                        | <packet_loss>0</packet_loss>                                                                |
|                        | <jitter>3.0308383114718813</jitter>                                                         |
|                        | </statistics>                                                                               |
|                        | </result>                                                                                   |
|                        | ```                                                                                         |
|                        | **Statistics description**                                                                  |
|                        | **number_of_received_packets** number of received packets.                                  |
|                        | **number_of_lost_packets** number of lost packes.                                           |
|                        | **packet_loss** packet loss as percentage.                                                  |
|                        | **jitter** jitter in milliseconds.                                                          |
| 400 Bad Request        | \* `1003` A required query parameter was not specified for this request                     |
|                        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 404 Not Found          | \* `1030` Stream not found                                                                  |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

## `/input_stream/http` – Input HTTP stream

### `POST /input_stream/http` Opens input HTTP stream

Opens input HTTP stream.

See also [Usage examples - Speech To Text Stream](#examples_stt_stream) for more information.

#### Parameters

| Name       | Parameter Type | Data Type | Required | Description                                                                                                                            |
| :--------- | :------------- | :-------- | :------- | :------------------------------------------------------------------------------------------------------------------------------------- |
| frequency  | query          | string    | no       | Frequency of streamed data. Default value is 8000.                                                                                     |
| n_channels | query          | string    | no       | Number of channels. Default value is 1.                                                                                                |
| path       | query          | string    | no       | Path to file where streamed data will be saved. If path is not set file will not be saved. File will be overwritten if already exists. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X POST "SERVER_ADDRESS:PORT/input_stream/http?frequency=8000"
```

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 | [JSON](#) [XML](#)                                                                          |
|                        | ```json                                                                                     |
|                        | {                                                                                           |
|                        | "result": {                                                                                 |
|                        | "version": 1,                                                                               |
|                        | "name": "HttpInputStreamResult",                                                            |
|                        | "input_stream": "ec563083-3d9b-457d-a0ac-24b197bc222f"                                      |
|                        | }                                                                                           |
|                        | }                                                                                           |
|                        | ```                                                                                         |
|                        | ```xml                                                                                      |
|                        | <?xml version="1.0" encoding="UTF-8"?>                                                      |
|                        | <result>                                                                                    |
|                        | <version>1</version>                                                                        |
|                        | <name>HttpInputStreamResult</name>                                                          |
|                        | <input_stream>a869ea70-980a-4497-99b4-3ef181f0b87d</input_stream>                           |
|                        | </result>                                                                                   |
|                        | ```                                                                                         |
| 400 Bad Request        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

### `PUT /input_stream/http` Send data to input stream via HTTP

Sends data to input stream via HTTP request. Chunked HTTP stream is supported as well.

See also [Usage examples - Speech To Text Stream](#examples_stt_stream) for more information.

#### Parameters

| Name         | Parameter Type | Data Type | Required | Description                     |
| :----------- | :------------- | :-------- | :------- | :------------------------------ |
| input_stream | query          | string    | yes      | ID of opened input HTTP stream. |

#### Body

RAW AUDIO DATA (s16le, frequency and number of channels depends on request
POST /input_stream/http?frequency={FREQ}&n_channels={N})

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 |                                                                                             |
| 400 Bad Request        | \* `1003` A required query parameter was not specified for this request                     |
|                        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 404 Not Found          | \* `1030` Stream not found                                                                  |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

### `DELETE /input_stream/http` Close opened input HTTP stream

Closes opened input HTTP stream.

#### Parameters

| Name         | Parameter Type | Data Type | Required | Description               |
| :----------- | :------------- | :-------- | :------- | :------------------------ |
| input_stream | query          | string    | yes      | ID of opened HTTP stream. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X DELETE "SERVER_ADDRESS:PORT/input_stream/http?input_stream=OPENED_HTTP_STREAM"
```

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 |                                                                                             |
| 400 Bad Request        | \* `1003` A required query parameter was not specified for this request                     |
|                        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 404 Not Found          | \* `1030` Stream not found                                                                  |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

## `/input_stream/websocket` – Input WebSocket stream

### `GET /input_stream/websocket` Opens input WebSocket stream

Opens input WebSocket stream.

WebSocket handshake request must contains the following parameters in the header: Upgrade, Connection, Sec-WebSocket-Version, Sec-WebSocket-Key and session parameter (X-SessionID) or basic authorization (Authorization).

```
 GET /input_stream/websocket?frequency=8000&n_channels=1 HTTP/1.1
 Host: server.example.com
 Upgrade: websocket
 Connection: Upgrade
 Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==
 Sec-WebSocket-Version: 13
 X-SessionID: 258f505c-a6fa-4c3f-8a87-b048874ac6aa
 Accept: application/json
```

Ther result of this request is delivered via WebSocket as a first frame. Data must be sent in a binary format in s16le format. If an error occurs during the upgrading connection, the error is delivered as an HTTP response. If an error occurs after a WebSocket connection is established, the WebSocket is closed with the corresponding WebSocket's status code. The maximum frame size is 4 MiB by default. When the WebSocket connection is closed, the input stream stops automatically. This can be changed in the server configuration file using the input_stream.websocket.max_payload_size option.

See also [Usage examples - Speech To Text Stream](#examples_stt_stream) for more information.

#### Parameters

| Name       | Parameter Type | Data Type | Required | Description                                                                                                                            |
| :--------- | :------------- | :-------- | :------- | :------------------------------------------------------------------------------------------------------------------------------------- |
| frequency  | query          | string    | no       | Frequency of streamed data. Default value is 8000.                                                                                     |
| n_channels | query          | string    | no       | Number of channels. Default value is 1.                                                                                                |
| path       | query          | string    | no       | Path to file where streamed data will be saved. If path is not set file will not be saved. File will be overwritten if already exists. |

#### Body

_empty content_

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| WebSocket              | [JSON](#) [XML](#)                                                                          |
|                        | ```json                                                                                     |
|                        | {                                                                                           |
|                        | "result": {                                                                                 |
|                        | "version": 1,                                                                               |
|                        | "name": "WebSocketInputStreamResult",                                                       |
|                        | "input_stream": "ec563083-3d9b-457d-a0ac-24b197bc222f"                                      |
|                        | }                                                                                           |
|                        | }                                                                                           |
|                        | ```                                                                                         |
|                        | ```xml                                                                                      |
|                        | <?xml version="1.0" encoding="UTF-8"?>                                                      |
|                        | <result>                                                                                    |
|                        | <version>1</version>                                                                        |
|                        | <name>WebSocketInputStreamResult</name>                                                     |
|                        | <input_stream>a869ea70-980a-4497-99b4-3ef181f0b87d</input_stream>                           |
|                        | </result>                                                                                   |
|                        | ```                                                                                         |
| 400 Bad Request        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
|                        | \* `1043` Can't create WebSocket                                                            |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

## `/output_stream/rtp` – Output RTP stream

### `GET /output_stream/rtp` List status of RTP stream

Lists status of opened output RTP streams for a user. If the user has admin role, all of the opened output RTP streams are listed.

Note that output RTP streams are automatically deleted after the timeout specified in SPE property file. This timeout fires when no task is attached to the output stream, or the attached task is already finished for a given amount of time.

See also [Usage examples - External Text To Speech stream](#examples_external_tts_stream) for more information.

#### Parameters

| Name          | Parameter Type | Data Type | Required | Description                     |
| :------------ | :------------- | :-------- | :------- | :------------------------------ |
| output_stream | query          | string    | yes      | ID of opened output RTP stream. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X GET "SERVER_ADDRESS:PORT/output_stream/rtp?output_stream=OPENED_RTP_STREAM"
```

#### Available responses

| HTTP Status            | Output                                                                                             |
| :--------------------- | :------------------------------------------------------------------------------------------------- |
| 200 OK                 | [JSON](#) [XML](#)                                                                                 |
|                        | ```json                                                                                            |
|                        | {                                                                                                  |
|                        | "result": {                                                                                        |
|                        | "version": 1,                                                                                      |
|                        | "name": "RtpOutputStreamsListResult",                                                              |
|                        | "user_output_streams": [                                                                           |
|                        | {                                                                                                  |
|                        | "user": "admin",                                                                                   |
|                        | "output_streams": ["tc548623-3d9b-445d-a8ac-24b17bc222fa", "35cb19b9-9a1e-430f-89a4-2eb33c8a1337"] |
|                        | },                                                                                                 |
|                        | {                                                                                                  |
|                        | "user": "john",                                                                                    |
|                        | "output_streams": ["32ee0cf9-c7a4-4999-a321-241e668db831"]                                         |
|                        | }                                                                                                  |
|                        | ]                                                                                                  |
|                        | }                                                                                                  |
|                        | }                                                                                                  |
|                        | ```                                                                                                |
|                        | ```xml                                                                                             |
|                        | <?xml version="1.0" encoding="UTF-8"?>                                                             |
|                        | <result>                                                                                           |
|                        | <version>1</version>                                                                               |
|                        | <name>RtpOutputStreamsListResult</name>                                                            |
|                        | <user_output_streams>                                                                              |
|                        | <item>                                                                                             |
|                        | <user>admin</user>                                                                                 |
|                        | <output_streams>                                                                                   |
|                        | <item>tc548623-3d9b-445d-a8ac-24b17bc222fa</item>                                                  |
|                        | <item>35cb19b9-9a1e-430f-89a4-2eb33c8a1337</item>                                                  |
|                        | </output_streams>                                                                                  |
|                        | </item>                                                                                            |
|                        | <item>                                                                                             |
|                        | <user>john</user>                                                                                  |
|                        | <output_streams>                                                                                   |
|                        | <item>32ee0cf9-c7a4-4999-a321-241e668db831</item>                                                  |
|                        | </output_streams>                                                                                  |
|                        | </item>                                                                                            |
|                        | </user_output_streams>                                                                             |
|                        | </result>                                                                                          |
|                        | ```                                                                                                |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                                    |

### `POST /output_stream/rtp` Open output RTP stream

Opens output RTP stream. Opened stream sends data to host address and port specified. In specified RTP payloadType.

Note that output RTP streams are automatically deleted after the timeout specified in SPE property file. This timeout fires when no task is attached to the output stream, or the attached task is already finished for a given amount of time.

See also [Usage examples - External Text To Speech stream](#examples_external_tts_stream) for more information.

#### Parameters

| Name         | Parameter Type | Data Type | Required | Description                                                                                                                 |
| :----------- | :------------- | :-------- | :------- | :-------------------------------------------------------------------------------------------------------------------------- |
| host         | query          | string    | yes      | Host in format address:port. E.g. myserver.com:6789                                                                         |
| payload_type | query          | number    | yes      | Payload type. Supported payloads are 0 (PCMU) and 8 (PCMA). See [RTP/HTTP streams](#RTP_HTTP_streams) for more information. |
| source_port  | query          | number    | no       | Source port. When omitted, the source port is automatically assigned.                                                       |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X POST "SERVER_ADDRESS:PORT/output_stream/rtp?host=127.0.0.1:6789&payload_type=0"
```

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 | [JSON](#) [XML](#)                                                                          |
|                        | ```json                                                                                     |
|                        | {                                                                                           |
|                        | "result": {                                                                                 |
|                        | "version": 1,                                                                               |
|                        | "name": "RtpOutputStreamResult",                                                            |
|                        | "output_stream": "tc548623-3d9b-445d-a8ac-24b17bc222f"                                      |
|                        | }                                                                                           |
|                        | }                                                                                           |
|                        | ```                                                                                         |
|                        | ```xml                                                                                      |
|                        | <?xml version="1.0" encoding="UTF-8"?>                                                      |
|                        | <result>                                                                                    |
|                        | <version>1</version>                                                                        |
|                        | <name>RtpOutputStreamResult</name>                                                          |
|                        | <output_stream>tc548623-3d9b-445d-a8ac-24b17bc222f</output_stream>                          |
|                        | </result>                                                                                   |
|                        | ```                                                                                         |
| 400 Bad Request        | \* `1003` A required query parameter was not specified for this request                     |
|                        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

### `DELETE /output_stream/rtp` Close opened output RTP stream

Closes opened output RTP stream.

#### Parameters

| Name          | Parameter Type | Data Type | Required | Description                     |
| :------------ | :------------- | :-------- | :------- | :------------------------------ |
| output_stream | query          | string    | yes      | ID of opened output RTP stream. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X DELETE "SERVER_ADDRESS:PORT/output_stream/rtp?output_stream=OPENED_RTP_STREAM"
```

#### Available responses

| HTTP Status            | Output                                                                  |
| :--------------------- | :---------------------------------------------------------------------- |
| 200 OK                 |                                                                         |
| 400 Bad Request        | \* `1003` A required query parameter was not specified for this request |
| 404 Not Found          | \* `1030` Stream not found                                              |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                         |

## `/stream/rtp` – RTP stream **Deprecated**

### `POST /stream/rtp` **Deprecated** Open RTP stream for listening

This endpoint is deprecated since SPE 3.23.x. Use `POST /input_stream/rtp` instead.

Opens RTP stream for listening. Stream is opened on port, which is returned in result.

#### Parameters

| Name | Parameter Type | Data Type | Required | Description                                                                                                                            |
| :--- | :------------- | :-------- | :------- | :------------------------------------------------------------------------------------------------------------------------------------- |
| path | query          | string    | no       | Path to file where streamed data will be saved. If path is not set file will not be saved. File will be overwritten if already exists. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X POST "SERVER_ADDRESS:PORT/stream/rtp"
```

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 | [JSON](#) [XML](#)                                                                          |
|                        | ```json                                                                                     |
|                        | {                                                                                           |
|                        | "result": {                                                                                 |
|                        | "version": 1,                                                                               |
|                        | "name": "RtpStreamResult",                                                                  |
|                        | "port": "5000",                                                                             |
|                        | "stream": "ec563083-3d9b-457d-a0ac-24b197bc222f"                                            |
|                        | }                                                                                           |
|                        | }                                                                                           |
|                        | ```                                                                                         |
|                        | ```xml                                                                                      |
|                        | <?xml version="1.0" encoding="UTF-8"?>                                                      |
|                        | <result>                                                                                    |
|                        | <version>1</version>                                                                        |
|                        | <name>RtpStreamResult</name>                                                                |
|                        | <port>5000</port>                                                                           |
|                        | <stream>a869ea70-980a-4497-99b4-3ef181f0b87d</stream>                                       |
|                        | </result>                                                                                   |
|                        | ```                                                                                         |
| 400 Bad Request        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 403 Forbidden          | \* `1026` RTP sessions limit exceeded                                                       |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

### `DELETE /stream/rtp` **Deprecated** Close opened RTP stream

This endpoint is deprecated since SPE 3.23.x. Use `DELETE /input_stream/rtp` instead.

Closes opened RTP stream.

#### Parameters

| Name   | Parameter Type | Data Type | Required | Description              |
| :----- | :------------- | :-------- | :------- | :----------------------- |
| stream | query          | string    | yes      | ID of opened RTP stream. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X DELETE "SERVER_ADDRESS:PORT/stream/rtp?stream=OPENED_RTP_STREAM"
```

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 |                                                                                             |
| 400 Bad Request        | \* `1003` A required query parameter was not specified for this request                     |
|                        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 404 Not Found          | \* `1030` Stream not found                                                                  |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

## `/stream/http` – HTTP stream **Deprecated**

### `POST /stream/http` **Deprecated** Open HTTP stream for listening

This endpoint is deprecated since SPE 3.23.x. Use `POST /input_stream/http` instead.

Opens HTTP stream for listening.

#### Parameters

| Name       | Parameter Type | Data Type | Required | Description                                                                                                                            |
| :--------- | :------------- | :-------- | :------- | :------------------------------------------------------------------------------------------------------------------------------------- |
| frequency  | query          | string    | no       | Frequency of streamed data. Default value is 8000.                                                                                     |
| n_channels | query          | string    | no       | Number of channels. Default value is 1.                                                                                                |
| path       | query          | string    | no       | Path to file where streamed data will be saved. If path is not set file will not be saved. File will be overwritten if already exists. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X POST "SERVER_ADDRESS:PORT/stream/http?frequency=8000"
```

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 | [JSON](#) [XML](#)                                                                          |
|                        | ```json                                                                                     |
|                        | {                                                                                           |
|                        | "result": {                                                                                 |
|                        | "version": 1,                                                                               |
|                        | "name": "HttpStreamResult",                                                                 |
|                        | "stream": "ec563083-3d9b-457d-a0ac-24b197bc222f"                                            |
|                        | }                                                                                           |
|                        | }                                                                                           |
|                        | ```                                                                                         |
|                        | ```xml                                                                                      |
|                        | <?xml version="1.0" encoding="UTF-8"?>                                                      |
|                        | <result>                                                                                    |
|                        | <version>1</version>                                                                        |
|                        | <name>HttpStreamResult</name>                                                               |
|                        | <stream>a869ea70-980a-4497-99b4-3ef181f0b87d</stream>                                       |
|                        | </result>                                                                                   |
|                        | ```                                                                                         |
| 400 Bad Request        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

### `PUT /stream/http` **Deprecated** Send data to stream via HTTP

This endpoint is deprecated since SPE 3.23.x. Use `PUT /input_stream/http` instead.

Sends data to stream via HTTP request. Chunked HTTP stream is supported as well.

#### Parameters

| Name   | Parameter Type | Data Type | Required | Description               |
| :----- | :------------- | :-------- | :------- | :------------------------ |
| stream | query          | string    | yes      | ID of opened HTTP stream. |

#### Body

RAW AUDIO DATA (s16le, frequency and number of channels depends on request
POST /stream/http?frequency={FREQ}&n_channels={N})

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 |                                                                                             |
| 400 Bad Request        | \* `1003` A required query parameter was not specified for this request                     |
|                        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 404 Not Found          | \* `1030` Stream not found                                                                  |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

### `DELETE /stream/http` **Deprecated** Close opened HTTP stream

This endpoint is deprecated since SPE 3.23.x. Use `DELETE /input_stream/http` instead.

Closes opened HTTP stream.

#### Parameters

| Name   | Parameter Type | Data Type | Required | Description               |
| :----- | :------------- | :-------- | :------- | :------------------------ |
| stream | query          | string    | yes      | ID of opened HTTP stream. |

#### Body

_empty content_

#### Example

```bash
curl -H "X-SessionID:YOUR_SESSION_ID" -X DELETE "SERVER_ADDRESS:PORT/stream/http?stream=OPENED_HTTP_STREAM"
```

#### Available responses

| HTTP Status            | Output                                                                                      |
| :--------------------- | :------------------------------------------------------------------------------------------ |
| 200 OK                 |                                                                                             |
| 400 Bad Request        | \* `1003` A required query parameter was not specified for this request                     |
|                        | \* `1004` An invalid value was specified for one of the query parameters in the request URI |
| 404 Not Found          | \* `1030` Stream not found                                                                  |
| 405 Method Not Allowed | \* `1009` Technology is not supported by server                                             |

```

```
