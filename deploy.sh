#!/bin/bash

# Voice Processing Service Deployment Script
# This script helps deploy the voice processing service in different environments

set -e

# Default values
ENVIRONMENT="development"
PHONEXIA_URL="http://localhost:8600"
RTP_PORT_RANGE="10000-10100"
DATA_DIR="./data"
IMAGE_TAG="voice-processing-service:latest"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] COMMAND"
    echo ""
    echo "Commands:"
    echo "  build       Build the Docker image"
    echo "  start       Start the services"
    echo "  stop        Stop the services"
    echo "  restart     Restart the services"
    echo "  logs        Show service logs"
    echo "  status      Show service status"
    echo "  clean       Clean up containers and volumes"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV    Set environment (development|production) [default: development]"
    echo "  -p, --phonexia-url URL   Set Phonexia API URL [default: http://localhost:8600]"
    echo "  -r, --rtp-range RANGE    Set RTP port range [default: 10000-10100]"
    echo "  -d, --data-dir DIR       Set data directory [default: ./data]"
    echo "  -t, --tag TAG            Set Docker image tag [default: voice-processing-service:latest]"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 start -e production -p http://phonexia.company.com:8600"
    echo "  $0 logs"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    print_info "Prerequisites check passed"
}

# Function to create data directories
create_data_dirs() {
    print_info "Creating data directories..."
    mkdir -p "${DATA_DIR}/logs"
    mkdir -p "${DATA_DIR}/recordings"
    chmod 755 "${DATA_DIR}/logs"
    chmod 755 "${DATA_DIR}/recordings"
    print_info "Data directories created at ${DATA_DIR}"
}

# Function to build Docker image
build_image() {
    print_info "Building Docker image: ${IMAGE_TAG}"
    docker build -t "${IMAGE_TAG}" ./voice-processing-service
    print_info "Docker image built successfully"
}

# Function to start services
start_services() {
    print_info "Starting services in ${ENVIRONMENT} environment..."
    
    create_data_dirs
    
    # Set environment variables
    export PHONEXIA_API_URL="${PHONEXIA_URL}"
    export DATA_DIR="${DATA_DIR}"
    
    if [ "${ENVIRONMENT}" = "production" ]; then
        docker-compose -f docker-compose.yml up -d
    else
        docker-compose up -d
    fi
    
    print_info "Services started successfully"
    print_info "Health check: curl http://localhost:8081/health"
}

# Function to stop services
stop_services() {
    print_info "Stopping services..."
    docker-compose down
    print_info "Services stopped"
}

# Function to restart services
restart_services() {
    print_info "Restarting services..."
    stop_services
    start_services
}

# Function to show logs
show_logs() {
    print_info "Showing service logs..."
    docker-compose logs -f voice-processing-service
}

# Function to show status
show_status() {
    print_info "Service status:"
    docker-compose ps
    
    print_info "Health check:"
    if curl -s http://localhost:8081/health > /dev/null 2>&1; then
        print_info "Service is healthy"
        curl -s http://localhost:8081/health | jq . 2>/dev/null || curl -s http://localhost:8081/health
    else
        print_warning "Service health check failed"
    fi
}

# Function to clean up
clean_up() {
    print_warning "This will remove all containers and volumes. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_info "Cleaning up..."
        docker-compose down -v --remove-orphans
        docker system prune -f
        print_info "Cleanup completed"
    else
        print_info "Cleanup cancelled"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--phonexia-url)
            PHONEXIA_URL="$2"
            shift 2
            ;;
        -r|--rtp-range)
            RTP_PORT_RANGE="$2"
            shift 2
            ;;
        -d|--data-dir)
            DATA_DIR="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        build|start|stop|restart|logs|status|clean)
            COMMAND="$1"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if command is provided
if [ -z "${COMMAND:-}" ]; then
    print_error "No command provided"
    show_usage
    exit 1
fi

# Validate environment
if [[ ! "${ENVIRONMENT}" =~ ^(development|production)$ ]]; then
    print_error "Invalid environment: ${ENVIRONMENT}. Must be 'development' or 'production'"
    exit 1
fi

# Execute command
case "${COMMAND}" in
    build)
        check_prerequisites
        build_image
        ;;
    start)
        check_prerequisites
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        check_prerequisites
        restart_services
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    clean)
        clean_up
        ;;
    *)
        print_error "Unknown command: ${COMMAND}"
        show_usage
        exit 1
        ;;
esac
