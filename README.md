# CCCZ Buddy Realtime Phonexia Server

## Overview

This project implements a modular SIP server for receiving calls, processing audio streams in real-time, and integrating with Phonexia speech-to-text services. The server acts as a bridge between telephony systems and speech analysis capabilities, enabling real-time transcription and processing of audio from phone calls.

## Architecture

The solution follows a modular, clean architecture approach with clear separation of concerns:

- **ASP.NET Core Application** (.NET 9) serves as the hosting platform
- **Dependency Injection** for all components, enabling easy testing and component replacement
- **Buffer-based Audio Processing** to manage different speeds of input and processing
- **Session Management** to isolate each call and handle resources properly

### Key Components

1. **SipServerService** (IHostedService): Manages SIP transport and delegates incoming calls
2. **CallSessionManager**: Creates and manages ICallSession instances
3. **CallSession**: Represents one active call and manages its lifecycle
4. **AudioInputReceiver**: Receives network data (e.g., RTP packets) and extracts audio
5. **AudioBuffer**: Thread-safe queue for audio data
6. **AudioProcessor**: Processes audio data (initially saving to WAV, later for STT)

### Architecture Diagram

```mermaid
graph TD
    subgraph External
        SIP_Client[SIP Klient]
    end

    subgraph SipApplication ["ASP.NET Core Aplikace (.NET 9)"]
        A_Kestrel[Kestrel Web Server] --> B_ApiEndpoints(API Endpoints /api/calls)
        A_Kestrel --> C_SipServerService(SipServerService : IHostedService)

        subgraph DependencyInjection ["DI Kontejner"]
            D_SIPTransport(SIPTransport - Singleton)
            E_CallSessionManager(CallSessionManager : ICallSessionManager - Singleton)
            F_LoggerFactory(ILoggerFactory - Singleton)
            G_AudioReceiverFactory(Factory pro IAudioInputReceiver - Transient)
            H_AudioBufferFactory(Factory pro IAudioBuffer - Transient)
            I_AudioProcessorFactory(Factory pro IAudioProcessor - Transient)
            J_CallSessionFactory(Factory pro ICallSession - Transient)
        end

        C_SipServerService -- Příchozí INVITE --> E_CallSessionManager
        B_ApiEndpoints -- Správa hovorů --> E_CallSessionManager

        E_CallSessionManager -- Vytváří / Spravuje --> K_CallSession(ICallSession - Instance per Call)

        subgraph CallSessionInstance [Instance pro 1 hovor]
            direction LR
            K_CallSession --> L_SIPUserAgent(SIPUserAgent)
            K_CallSession --> M_AudioReceiver(IAudioInputReceiver)
            K_CallSession --> N_AudioBuffer(IAudioBuffer)
            K_CallSession --> O_AudioProcessor(IAudioProcessor)

            M_AudioReceiver -- byte[] audio --> N_AudioBuffer
            O_AudioProcessor -- čte byte[] --> N_AudioBuffer
        end

        M_AudioReceiver -- síťová data --> P_UdpListeners(UDP Listeners RTP/RTCP)
        O_AudioProcessor -- výsledek (WAV/Text) --> Q_Output[Uložiště / STT Výsledek]
    end

    SIP_Client -- SIP (UDP/TCP) --> C_SipServerService
    SIP_Client -- RTP/RTCP (UDP) --> P_UdpListeners
```

## Technology Stack

- **ASP.NET Core** (.NET 9)
- **SIPSorcery** for SIP protocol handling
- **Phonexia** for speech-to-text processing (planned integration)
- **Minimal API** for management endpoints

## CI/CD Pipeline

The project uses GitLab CI/CD pipeline for automated building and deployment of Docker images.

### Building New Docker Images

To trigger a new Docker image build:

1. **Update the version file**: Edit the `version` file in the project root with your desired version number (e.g., `1.2.2`)
2. **Push to main branch**: Commit and push the changes to the `main` branch
3. **Pipeline execution**: The CI/CD pipeline will automatically:
   - Build the .NET application
   - Create a Docker image tagged with the version from the `version` file
   - Push the image to the GitLab Container Registry

### Pipeline Configuration

The pipeline is configured in `.gitlab-ci.yml` and includes:

- **Build stage**: Compiles the .NET application using `dotnet publish`
- **Deploy stage**: Builds Docker image and pushes to registry (triggered only when `version` file changes)

**Example workflow:**

```bash
# Update version
echo "1.2.2" > version

# Commit and push
git add version
git commit -m "Bump version to 1.2.2"
git push origin main
```

The resulting Docker image will be available as: `${CI_REGISTRY_IMAGE}:1.2.2`

## Development Status

This project is currently in the development phase:

- [x] Architecture design
- [x] Project structure setup
- [x] Interface definitions
- [x] Configuration classes
- [x] STT processor implementation
- [x] Basic SIP server implementation
- [x] Audio buffering and WAV recording
- [x] Call session management
- [x] Integration with Phonexia STT
- [ ] API endpoints for call management
- [ ] Extended audio processing capabilities

### Implemented Components

1. **Project Structure**

   - Created directory structure (Interfaces, Services, Configuration, Models)
   - Added required NuGet packages
   - Set up configuration in appsettings.json

2. **Interface Definitions**

   - IAudioBuffer - Interface for audio data buffer
   - IAudioInputReceiver - Interface for audio input receiver (e.g., RTP)
   - IAudioProcessor - Interface for audio data processor (e.g., WAV, STT)
   - ICallSession - Interface for call session
   - ICallSessionManager - Interface for call session manager

3. **Configuration Classes**

   - SipServerOptions - Configuration for SIP server
   - PhonexiaOptions - Configuration for Phonexia API

4. **Models**

   - PhonexiaModels - Models for Phonexia API responses
   - PhonexiaStreamModels - Models for Phonexia streaming API
   - TranscriptionMessage - Model for Kafka transcription messages
   - SttResult - Model for STT results

5. **Core Services**

   - BlockingCollectionAudioBuffer - Thread-safe implementation of IAudioBuffer
   - RtpAudioReceiver - Implementation of IAudioInputReceiver for RTP
   - SttAudioProcessor - Implementation of IAudioProcessor for STT processing
   - PhonexiaSttProcessor - Implementation of IAudioProcessor for Phonexia STT
   - CallSession - Implementation of ICallSession
   - CallSessionManager - Implementation of ICallSessionManager
   - SipServerService - SIP server implementation

6. **Testing Tools**
   - voice-processing-simulator - Tool for generating test WAV files

## Getting Started

### Prerequisites

- .NET 9 SDK
- Visual Studio 2022 or later (optional)
- SIP client for testing (e.g., Linphone, Jitsi)
- Phonexia API key (for STT functionality)

### Setup

1. Clone the repository
2. Build the solution using `dotnet build`
3. Configure settings in `appsettings.json`
4. Run the application using `dotnet run --project voice-processing-service`

### Testing

1. Generate test WAV files using the simulator:
   ```
   dotnet run --project voice-processing-simulator generate test.wav 10 440
   ```
2. Configure a SIP client to connect to the server
3. Make a test call to verify functionality
4. Check the logs and recorded files in the configured directory

### Configuration

Basic settings in `appsettings.json`:

```json
{
  "SipServer": {
    "ListenIpAddress": "Any",
    "ListenPort": 5060,
    "RtpPortMin": 10000,
    "RtpPortMax": 19998,
    "WavRecordingDirectory": "RecordedCalls"
  },
  "Phonexia": {
    "ApiUrl": "https://api.phonexia.com/v1",
    "ApiKey": "your-api-key-here",
    "Language": "cs-CZ",
    "Model": "default",
    "ChunkSizeBytes": 8000
  },
  "RtpReceiver": {
    "InactivityTimeoutMs": 30000
  }
}
```

#### Advanced Option: RTP Inactivity Timeout

**RtpReceiver.InactivityTimeoutMs** (default: `30000` ms)
Controls how long RTP media can be inactive before the session is considered ended.
- Prevents premature media shutdown during SIP re-INVITE pauses.
- Override via `appsettings.json` or environment variable:
  - `RtpReceiver__InactivityTimeoutMs`
- Example environment variable usage:
  - Linux/Windows: `export RtpReceiver__InactivityTimeoutMs=45000`
  - Docker: `-e RtpReceiver__InactivityTimeoutMs=45000`
- Recommended values:
  - 30000 ms (30s) for typical SIP/RTP deployments with re-INVITE support.
  - Lower values for aggressive cleanup (e.g., 5000 ms).

**Troubleshooting:**
If media stops before BYE, increase `RtpReceiver.InactivityTimeoutMs`.

## API Endpoints

- `GET /api/calls` - List active calls
- `DELETE /api/calls/{callId}` - Terminate a specific call

## WAV Recording

WAV recording can be enabled to persist raw call audio to .wav files without changing the STT pipeline. Recording runs in parallel with STT via the composite fan-out in [C#.CompositeAudioProcessor](voice-processing-service/Services/CompositeAudioProcessor.cs:1).

- Configuration keys (defaults from [C#.WavRecordingOptions](voice-processing-service/Configuration/WavRecordingOptions.cs:1)):
  - WavRecording:Enabled — bool (default: false)
  - WavRecording:Directory — string (default: /app/recordings)
  - WavRecording:FilenameTemplate — string (default: "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav")
  - WavRecording:MaxFileDurationSeconds — int (default: 3600)
  - WavRecording:SplitOnReinvite — bool (default: true)

- Environment variable overrides (ASP.NET Core convention):
  - WavRecording__Enabled=true
  - WavRecording__Directory=/app/recordings
  - WavRecording__FilenameTemplate={date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav

- Filename template tokens (rendered by [C#.WavAudioProcessorFactory](voice-processing-service/Services/WavAudioProcessorFactory.cs:1)):
  - {callId}, {utcStart[:format]}, {date}, {segmentIndex[:format]}
  - Example for callId "testcall", utcStart 2025-08-09T12:00:00Z, segmentIndex 1:
    /app/recordings/20250809/20250809T120000Z_call-testcall_seg-001.wav

- Behavior:
  - Rotation: new segment is created when duration exceeds MaxFileDurationSeconds
  - Split on format change when enabled (e.g., re-INVITE/codec change)
  - STT is unaffected; the STT path is unchanged due to fan-out in [C#.CompositeAudioProcessor](voice-processing-service/Services/CompositeAudioProcessor.cs:1)

- Logging and output location:
  - Files are written under WavRecording:Directory
  - Logs indicate segment open/close/rotation in [C#.WavAudioProcessor](voice-processing-service/Services/WavAudioProcessor.cs:1)

## License

Internal project - All Rights Reserved
