# Grok parser inbuild
[PARSER]
    Name          my_grok_parser
    Format        grok
    Grok_Pattern  <%{NUMBER:pri}>%{MONTHDAY:day} %{MONTH:month} %{TIME:time} %{HOSTNAME:hostname} %{WORD:appname}: %{GREEDYDATA:message}

# Grok parser file
[PARSER]
    Name          my_grok_parser
    Format        grok
    Grok_Pattern  <%{NUMBER:pri}>%{MONTHDAY:day} %{MONTH:month} %{TIME:time} %{HOSTNAME:hostname} %{MY_PATTERN}
    Grok_Match    "patterns/my_grok_patterns.conf"
