# Implementace WAV procesoru

## Popis úkolu

Tento úkol zahrnuje implementaci procesoru pro ukládání audio dat do WAV souboru. Procesor bude odebírat audio data z bufferu a ukládat je do WAV souboru ve formátu G.711 µ-law.

## Technické detaily

### Implementace WavAudioProcessor

Implementace bude využívat `BinaryWriter` z .NET pro zápis audio dat do WAV souboru.

```csharp
using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace procesoru pro ukládání audio dat do WAV souboru.
    /// </summary>
    public class WavAudioProcessor : IAudioProcessor
    {
        private readonly ILogger<WavAudioProcessor> _logger;
        private readonly string _wavFilePath;
        private readonly string _callId;
        private BinaryWriter _wavWriter;
        private long _audioDataSize = 0;

        /// <inheritdoc/>
        public string ProcessorId => $"WAV_{Path.GetFileNameWithoutExtension(_wavFilePath)}";

        /// <summary>
        /// Inicializuje novou instanci třídy <see cref="WavAudioProcessor"/>.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru pro logování.</param>
        /// <param name="wavFilePath">Cesta k WAV souboru pro ukládání audio dat.</param>
        /// <param name="logger">Logger pro logování událostí.</param>
        public WavAudioProcessor(string callId, string wavFilePath, ILogger<WavAudioProcessor> logger)
        {
            _callId = callId ?? throw new ArgumentNullException(nameof(callId));
            _wavFilePath = wavFilePath ?? throw new ArgumentNullException(nameof(wavFilePath));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _logger.LogInformation($"[{_callId}] WavAudioProcessor created for file: {_wavFilePath}");
        }

        /// <inheritdoc/>
        public async Task StartProcessingAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"[{_callId}] Starting WAV processing from buffer for {ProcessorId}.");

            if (!InitializeWavFile())
            {
                _logger.LogError($"[{_callId}] Failed to initialize WAV file, processing cannot start.");
                return; // Nemůžeme zpracovávat bez souboru
            }

            try
            {
                // Odebíráme data z bufferu, dokud není prázdný A není dokončeno přidávání
                while (!buffer.IsCompleted || buffer.TryTake(out _, 0, CancellationToken.None)) // Zkontrolujeme i IsCompleted pro jistotu
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        _logger.LogInformation($"[{_callId}] WAV processing cancellation requested for {ProcessorId}.");
                        break;
                    }

                    // Blokující čekání s timeoutem a cancellation tokenem
                    if (buffer.TryTake(out byte[] audioData, 500, cancellationToken)) // Timeout 500ms
                    {
                        // _logger.LogTrace($"[{_callId}] Processing {audioData.Length} audio bytes for {ProcessorId}.");
                        AppendAudioData(audioData);
                    }
                    // Pokud TryTake vrátí false (timeout nebo cancellation), cyklus pokračuje a zkontroluje podmínky znovu
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"[{_callId}] WAV processing cancelled for {ProcessorId} while waiting for data.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Error during WAV processing for {ProcessorId}.");
            }
            finally
            {
                _logger.LogInformation($"[{_callId}] Finishing WAV processing for {ProcessorId}. Finalizing file.");
                FinalizeWavFile(); // Ukončíme soubor bez ohledu na to, jak smyčka skončila
            }
            _logger.LogInformation($"[{_callId}] WAV processing stopped for {ProcessorId}.");
        }

        private bool InitializeWavFile()
        {
            try
            {
                // Zajistit, že adresář existuje
                Directory.CreateDirectory(Path.GetDirectoryName(_wavFilePath));

                var fileStream = new FileStream(_wavFilePath, FileMode.Create, FileAccess.Write, FileShare.Read);
                _wavWriter = new BinaryWriter(fileStream);
                WriteWavHeader(_wavWriter, 0); // Zapíše hlavičku s nulovou délkou dat
                _logger.LogInformation($"[{_callId}] Initialized WAV file: {_wavFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to initialize WAV file {_wavFilePath}");
                _wavWriter?.Dispose();
                _wavWriter = null;
                return false;
            }
        }

        private void AppendAudioData(byte[] audioData)
        {
            if (_wavWriter == null) return;
            try
            {
                _wavWriter.Write(audioData);
                _audioDataSize += audioData.Length;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{_callId}] Failed to write audio data to WAV file {_wavFilePath}");
                FinalizeWavFile(); // Pokud selže zápis, rovnou soubor uzavřeme
            }
        }

        private void FinalizeWavFile()
        {
            if (_wavWriter != null)
            {
                _logger.LogInformation($"[{_callId}] Finalizing WAV file: {_wavFilePath}, Audio Size: {_audioDataSize} bytes");
                try
                {
                    UpdateWavHeader(_wavWriter, _audioDataSize);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[{_callId}] Failed to update WAV header for {_wavFilePath}");
                }
                finally
                {
                    _wavWriter.Close(); // Zavře i podkladový FileStream
                    _wavWriter = null; // Označit jako uzavřený
                }
            }
        }

        // Statické metody pro WAV hlavičku
        private static void WriteWavHeader(BinaryWriter writer, long audioDataSize)
        {
            writer.Write(Encoding.ASCII.GetBytes("RIFF"));
            writer.Write((uint)(36 + audioDataSize));
            writer.Write(Encoding.ASCII.GetBytes("WAVE"));
            writer.Write(Encoding.ASCII.GetBytes("fmt "));
            writer.Write(16);
            writer.Write((ushort)7); // G.711 µ-law
            writer.Write((ushort)1); // Mono
            writer.Write(8000); // Sample rate
            writer.Write(8000); // Byte rate
            writer.Write((ushort)1); // Block align
            writer.Write((ushort)8); // Bits per sample
            writer.Write(Encoding.ASCII.GetBytes("data"));
            writer.Write((uint)audioDataSize);
        }

        private static void UpdateWavHeader(BinaryWriter writer, long audioDataSize)
        {
            if (writer?.BaseStream == null || !writer.BaseStream.CanSeek) return;
            writer.BaseStream.Seek(4, SeekOrigin.Begin);
            writer.Write((uint)(36 + audioDataSize));
            writer.BaseStream.Seek(40, SeekOrigin.Begin);
            writer.Write((uint)audioDataSize);
            writer.Flush(); // Zajistit zápis
        }

        /// <inheritdoc/>
        public void Dispose()
        {
            _logger.LogInformation($"[{_callId}] Disposing WavAudioProcessor for {ProcessorId}.");
            FinalizeWavFile(); // Zajistíme uzavření souboru při dispose
            GC.SuppressFinalize(this);
        }
    }
}
```

### Továrna pro vytváření WavAudioProcessor

Pro vytváření instancí WavAudioProcessor bude implementována tovární metoda, která bude zodpovědná za vytvoření a inicializaci procesoru.

```csharp
using System;
using System.IO;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using voice_processing_service.Configuration;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Továrna pro vytváření instancí WavAudioProcessor.
    /// </summary>
    public class WavAudioProcessorFactory
    {
        private readonly ILoggerFactory _loggerFactory;
        private readonly SipServerOptions _options;

        /// <summary>
        /// Inicializuje novou instanci třídy <see cref="WavAudioProcessorFactory"/>.
        /// </summary>
        /// <param name="loggerFactory">Továrna pro vytváření loggerů.</param>
        /// <param name="options">Konfigurace SIP serveru.</param>
        public WavAudioProcessorFactory(ILoggerFactory loggerFactory, IOptions<SipServerOptions> options)
        {
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }

        /// <summary>
        /// Vytvoří novou instanci WavAudioProcessor.
        /// </summary>
        /// <param name="callId">Identifikátor hovoru pro logování.</param>
        /// <returns>Instance IAudioProcessor.</returns>
        public IAudioProcessor CreateProcessor(string callId)
        {
            var wavFileName = $"call_{callId}_{DateTime.Now:yyyyMMddHHmmss}.wav";
            var wavFilePath = Path.Combine(_options.WavRecordingDirectory, wavFileName);
            return new WavAudioProcessor(callId, wavFilePath, _loggerFactory.CreateLogger<WavAudioProcessor>());
        }
    }
}
```

### Registrace v DI kontejneru

WavAudioProcessorFactory bude registrována v DI kontejneru jako singleton služba:

```csharp
// V Program.cs
services.AddSingleton<WavAudioProcessorFactory>();
```

## Testovací scénáře

### Unit testy pro WavAudioProcessor

1. **Test konstruktoru**
   - Ověřit, že instance WavAudioProcessor je vytvořena bez chyb
   - Ověřit, že ProcessorId je správně inicializován

2. **Test metody StartProcessingAsync**
   - Ověřit, že metoda StartProcessingAsync spustí zpracování dat z bufferu
   - Ověřit, že metoda StartProcessingAsync respektuje cancellation token

3. **Test inicializace WAV souboru**
   - Ověřit, že metoda InitializeWavFile vytvoří WAV soubor s platnou hlavičkou
   - Ověřit, že metoda InitializeWavFile vytvoří adresář, pokud neexistuje

4. **Test přidávání audio dat**
   - Ověřit, že metoda AppendAudioData správně zapisuje audio data do souboru
   - Ověřit, že metoda AppendAudioData správně aktualizuje _audioDataSize

5. **Test finalizace WAV souboru**
   - Ověřit, že metoda FinalizeWavFile správně aktualizuje WAV hlavičku
   - Ověřit, že metoda FinalizeWavFile správně uzavře soubor

6. **Test metody Dispose**
   - Ověřit, že po zavolání Dispose je soubor správně uzavřen
   - Ověřit, že po zavolání Dispose nelze přidat další data

### Unit testy pro WavAudioProcessorFactory

1. **Test konstruktoru**
   - Ověřit, že instance WavAudioProcessorFactory je vytvořena bez chyb

2. **Test metody CreateProcessor**
   - Ověřit, že metoda CreateProcessor vytvoří instanci WavAudioProcessor
   - Ověřit, že metoda CreateProcessor vytvoří správnou cestu k WAV souboru

### Integrační testy pro WavAudioProcessor a AudioBuffer

1. **Test zpracování audio dat z bufferu**
   - Vytvořit instanci WavAudioProcessor a AudioBuffer
   - Přidat audio data do bufferu
   - Spustit zpracování a ověřit, že data jsou správně zapsána do WAV souboru

2. **Test ukončení zpracování**
   - Vytvořit instanci WavAudioProcessor a AudioBuffer
   - Přidat audio data do bufferu a zavolat CompleteAdding
   - Spustit zpracování a ověřit, že zpracování je ukončeno po vyprázdnění bufferu

## Implementační kroky

1. Implementovat třídu WavAudioProcessor
2. Implementovat třídu WavAudioProcessorFactory
3. Implementovat unit testy pro WavAudioProcessor
4. Implementovat unit testy pro WavAudioProcessorFactory
5. Implementovat integrační testy pro WavAudioProcessor a AudioBuffer
6. Registrovat WavAudioProcessorFactory v DI kontejneru

## Simulace pro testování

Pro testování WavAudioProcessor bude vytvořena jednoduchá konzolová aplikace, která simuluje přidávání audio dat do bufferu a jejich zpracování:

```csharp
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;

namespace voice_processing_service.Tests
{
    class WavProcessorSimulator
    {
        static async Task Main(string[] args)
        {
            if (args.Length < 1)
            {
                Console.WriteLine("Usage: WavProcessorSimulator <output_wav_file>");
                return;
            }

            string outputWavFile = args[0];

            // Vytvoření loggeru
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Trace);
            });
            var bufferLogger = loggerFactory.CreateLogger<BlockingCollectionAudioBuffer>();
            var processorLogger = loggerFactory.CreateLogger<WavAudioProcessor>();

            // Vytvoření bufferu a procesoru
            using var buffer = new BlockingCollectionAudioBuffer(bufferLogger);
            using var processor = new WavAudioProcessor("test_call", outputWavFile, processorLogger);

            // Vytvoření CancellationTokenSource pro ukončení simulace
            using var cts = new CancellationTokenSource();
            cts.CancelAfter(TimeSpan.FromSeconds(10)); // Simulace bude běžet 10 sekund

            // Spuštění producenta a procesoru
            var producerTask = ProducerAsync(buffer, cts.Token);
            var processorTask = processor.StartProcessingAsync(buffer, cts.Token);

            // Čekání na ukončení simulace
            try
            {
                await Task.WhenAll(producerTask, processorTask);
                Console.WriteLine("Simulace úspěšně dokončena.");
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Simulace byla zrušena.");
            }
        }

        static async Task ProducerAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            try
            {
                var random = new Random();
                for (int i = 0; i < 500; i++) // Simulace 500 paketů (10 sekund při 20ms)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    // Vytvoření náhodných audio dat (G.711 µ-law, 20ms při 8kHz = 160 bytů)
                    var audioData = new byte[160];
                    random.NextBytes(audioData);

                    // Přidání dat do bufferu
                    buffer.Add(audioData);
                    Console.WriteLine($"Producent: Přidáno {audioData.Length} bytů do bufferu (paket {i+1}/500).");

                    // Simulace prodlevy mezi pakety (20ms)
                    await Task.Delay(20, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Producent: Zrušen.");
            }
            finally
            {
                buffer.CompleteAdding();
                Console.WriteLine("Producent: Ukončeno přidávání do bufferu.");
            }
        }
    }
}
```

Tato simulace umožní otestovat zpracování audio dat a jejich ukládání do WAV souboru.

## Overview (Runtime)

WAV recording runs in parallel to STT using composite fan-out; enabling recording does not impact the STT path. See [C#.CompositeAudioProcessor](voice-processing-service/Services/CompositeAudioProcessor.cs:1). STT behavior remains unchanged when WAV recording is enabled.

### RTP Media Lifecycle & Inactivity Timeout

Media lifecycle is affected by RTP inactivity.
**RtpReceiver.InactivityTimeoutMs** (default: 30000 ms) controls how long RTP media can be paused (e.g., during SIP re-INVITE) before the session is considered ended.
Override via `appsettings.json` or environment variable (`RtpReceiver__InactivityTimeoutMs`).
If media stops before BYE, increase `RtpReceiver.InactivityTimeoutMs`.

## Configuration

Defaults are defined in [C#.WavRecordingOptions](voice-processing-service/Configuration/WavRecordingOptions.cs:1). Relevant keys:
- WavRecording:Enabled — bool (default: false)
- WavRecording:Directory — string (default: /app/recordings)
- WavRecording:FilenameTemplate — string (default: "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav")
- WavRecording:MaxFileDurationSeconds — int (default: 3600)
- WavRecording:SplitOnReinvite — bool (default: true)

Environment variable overrides (ASP.NET Core convention):
- WavRecording__Enabled=true
- WavRecording__Directory=/app/recordings
- WavRecording__FilenameTemplate={date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav

## Filename templating

Filenames are rendered by [C#.WavAudioProcessorFactory](voice-processing-service/Services/WavAudioProcessorFactory.cs:1). Supported tokens:
- {callId}
- {utcStart[:format]}
- {date}
- {segmentIndex[:format]}

Example resolution for callId "testcall", utcStart 2025-08-09T12:00:00Z, segmentIndex 1:
- /app/recordings/20250809/20250809T120000Z_call-testcall_seg-001.wav

## Rotation & Splitting

- Time-based rotation: a new segment is opened when duration exceeds MaxFileDurationSeconds.
- Format-based split: when enabled via SplitOnReinvite, a new segment is started if input format changes (e.g., re-INVITE/codec change).
- On normal shutdown, WAV headers are finalized before close to ensure valid files.

## Logging

Open/rotate/close events and finalize warnings are logged by [C#.WavAudioProcessor](voice-processing-service/Services/WavAudioProcessor.cs:1). Files are written under WavRecording:Directory. STT processing remains unaffected due to fan-out via [C#.CompositeAudioProcessor](voice-processing-service/Services/CompositeAudioProcessor.cs:1).
