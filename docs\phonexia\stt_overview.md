# Phonexia Speech Engine API - Speech-to-Text Summary (v3.63.1)

This document summarizes the relevant API endpoints and concepts for using Phonexia Speech Engine's Speech-to-Text (STT) functionality, based on the documentation for version 3.63.1. It includes core STT endpoints as well as essential common and basic operations.

## Core Concepts

### Request Structure & Response Format

- **Protocol:** REST-like interface using standard HTTP methods (GET, POST, PUT, DELETE) over HTTP/HTTPS.
- **Data Format:** Supports JSON and XML for request bodies and responses.
  - Specify response format via `Accept` header (`application/json`, `application/xml`) or `format` query parameter (`json`, `xml`). Query parameter has higher priority. JSON is the default.
- **Synchronous vs. Asynchronous:**
  - **Synchronous:** Response contains the result directly.
  - **Asynchronous:** Used for long-running tasks (like STT on large files). Returns `202 Accepted` with a task ID (`Location` header for polling/WebSocket, `X-TaskID` for webhook). Results must be retrieved later.
- **Request ID:** SP<PERSON> generates a unique `RID` for logging. Clients can provide their own ID (up to 50 chars) via the `X-Request-ID` header. The server response includes the `X-Request-ID`.

### Asynchronous Request Handling

Three methods to get results for asynchronous tasks:

1.  **Polling:**
    - Client receives `202 Accepted` with a `Location` header pointing to `/pending/{id}`.
    - Client repeatedly polls `GET /pending/{id}`.
    - Response is `200 OK` with status (`waiting`, `running`) until finished.
    - When finished, server responds with `303 See Other` and a `Location` header pointing to `/done/{id}`.
    - Client sends `GET /done/{id}` to retrieve the final result.
    - Results expire after a configurable timeout (default 60s) or after being fetched via `GET /done/{id}`.
2.  **WebSocket:**
    - Client receives `202 Accepted` with `Location: /pending/{id}`.
    - Client initiates a WebSocket handshake on the `/pending/{id}` URI.
    - Server sends the final result message through the WebSocket and closes it upon task completion.
    - Cannot be used if Webhook is registered for the task.
3.  **Webhook:**
    - Client sends the asynchronous request with an `X-WebhookTarget` header specifying the callback URL.
    - Server returns `202 Accepted` with an `X-TaskID` header.
    - When the task finishes, the server sends an HTTP POST request to the `X-WebhookTarget` URL with the result in the body and the `X-TaskID` in the header.
    - The callback target must respond with an HTTP `2xx` status.
    - Cannot be used if WebSocket is registered for the task.

### Task Prioritization

- Asynchronous tasks (except streams) can be prioritized using the `X-Priority` header (0=highest, 99=lowest, default=50).
- Requires the `prioritize` user role.
- Tasks are processed based on priority, but lower priority tasks might run if higher priority tasks require unavailable resources.
- Can be disabled server-side.

### Authentication

Two methods:

1.  **Token Authentication (Recommended):**
    - Client sends `POST /login` with Basic Authentication credentials.
    - Server responds with `200 OK` containing an `X-SessionID` token in the header and body.
    - Client includes the `X-SessionID` header in subsequent requests.
    - One active token per user. Persists across server restarts.
2.  **Basic Authentication:**
    - Disabled by default. If enabled, include the `Authorization: Basic <credentials>` header in _every_ request.
    - `POST /login` is disabled (returns 405).

### Audio Requirements

- **Containers/Codecs:**
  - WAVE (`.wav`): PCM s8, s16le, f32le, f64le; A-law, µ-law; ADPCM
  - FLAC (`.flac`)
  - OPUS in OGG (`.opus`)
  - Other formats possible if the optional audio converter is enabled and supports them.
- **Upload:** Use `POST /audiofile`.

### Streaming Overview (RTP/HTTP/WebSocket)

General workflow for stream-based processing (like STT Stream):

1.  **Open Input Stream:**
    - `POST /input_stream/rtp`: Returns port and stream ID.
    - `POST /input_stream/http`: Returns stream ID. Requires `frequency`, `n_channels`.
    - `GET /input_stream/websocket`: Requires WebSocket handshake. Returns stream ID via WebSocket message. Requires `frequency`, `n_channels`.
2.  **Bind Technology:**
    - `POST /technologies/{tech}/input_stream` (e.g., `POST /technologies/stt/input_stream`) with the `input_stream` ID and technology parameters.
    - Returns a `task_id`.
    - **Result Modes (STT, KWS, VAD):**
      - `complete` (default): Each result contains all data processed so far.
      - `incremental`: Each result contains only new data since the last result retrieval.
3.  **Send Data:**
    - **RTP:** Send RTP packets to the assigned port. Supported payload types include PCMU, PCMA, L16 (various rates/channels).
    - **HTTP:** Send raw s16le audio chunks via `PUT /input_stream/http` with the stream ID.
    - **WebSocket:** Send raw s16le audio binary messages over the established WebSocket.
4.  **Get Results:**
    - **Polling:** Periodically call `GET /technologies/{tech}/input_stream?task={task_id}`. Check `is_last: true` flag for the final result.
    - **WebSocket:** Establish WebSocket connection via `GET /technologies/{tech}/input_stream?task={task_id}` (with handshake headers). Server pushes results periodically (interval settable via `interval` param). Last message has `is_last: true`.
    - **Webhook:** Register callback URL via `GET /technologies/{tech}/input_stream?task={task_id}` with `X-WebhookTarget` header. Server POSTs results periodically (interval settable via `interval` param). Last message has `is_last: true`.
5.  **Close Input Stream:**
    - `DELETE /input_stream/{type}` (e.g., `DELETE /input_stream/http`) with the stream ID.
    - Streams auto-close after inactivity timeout (default 10s). WebSocket streams also close on client disconnect.

### Error Responses

- HTTP 4xx/5xx errors (except 501) return JSON/XML body with `code` and `message`.
- HTTP 500 errors may include an `X-ExceptionType` header.
- Common Error Codes:
  - `1003`: Missing parameter
  - `1004`: Invalid parameter value
  - `1005`: Unauthorized
  - `1007`: Unsupported audio format
  - `1008`: File not found
  - `1009`: Technology/Method not supported/allowed
  - `1010`: Task not found
  - `1015`, `1072`: Model not found
  - `1024`: Result not found (e.g., expired async result)
  - `1030`: Stream not found
  - `1038`: Resource locked
  - `1060`: Technology capacity exceeded
  - `1063`: Access to file forbidden
  - `1064`: Max task limit exceeded
  - `1071`: Technology is running (e.g., cannot delete running model)

### Resource Locker

- Locks resources (Files, Speaker Models, etc.) during asynchronous operations to prevent modification/deletion.
- Read access is still allowed.
- Can be disabled server-side.

## Basic Operations API

- **`POST /login`**: Authenticate using Basic Auth, receive `X-SessionID` token.
- **`GET /doc`**: Retrieve API documentation in HTML (no auth needed).
- **`GET /status`**: Check server component status (no auth needed).
- **`GET /server/info`**: Get server version, capabilities (e.g., audio converter, stream support).
- **`GET /technologies`**: List available/running technologies and models (no auth needed). `?show_all=true` includes disabled models.
- **`POST /technologies`**: Configure running technologies/models (admin only, requires restart).
- **`GET /directory?path={dir_path}`**: List directory contents.
- **`POST /directory?path={dir_path}`**: Create directory.
- **`DELETE /directory?path={dir_path}`**: Delete directory.
- **`GET /audiofile?path={file_path}`**: Download audio file.
- **`POST /audiofile?path={file_path}`**: Upload audio file (overwrites if exists).
- **`DELETE /audiofile?path={file_path}`**: Delete audio file.
- **`PUT /audiofile?path={src}&new_path={dest}©={bool}`**: Move or copy audio file.
- **`GET /audiofile/info?path={file_path}`**: Get audio file metadata (duration, freq, channels).
- **`GET /pending/{id}`**: Check status of an asynchronous task (polling/WebSocket).
- **`DELETE /pending/{id}`**: Cancel a waiting asynchronous task.
- **`GET /done/{id}`**: Retrieve result of a finished asynchronous task.
- **`GET /tasks`**: List all current asynchronous tasks and their states (no auth needed).

## Streaming API

- **`POST /input_stream/rtp`**: Open RTP input stream.
  - Params: `path` (optional, save stream to file).
  - Response: `port`, `input_stream` ID.
- **`DELETE /input_stream/rtp?input_stream={id}`**: Close RTP input stream.
- **`GET /input_stream/rtp/info?input_stream={id}`**: Get info about an active RTP input stream.
- **`POST /input_stream/http`**: Open HTTP input stream.
  - Params: `frequency` (default 8000), `n_channels` (default 1), `path` (optional).
  - Response: `input_stream` ID.
- **`PUT /input_stream/http?input_stream={id}`**: Send audio data chunk to HTTP stream.
- **`DELETE /input_stream/http?input_stream={id}`**: Close HTTP input stream.
- **`GET /input_stream/websocket`**: Open WebSocket input stream (requires handshake headers).
  - Params: `frequency` (default 8000), `n_channels` (default 1), `path` (optional).
  - Response: WebSocket connection established, first message contains `input_stream` ID.
- **(Deprecated)** `/stream/*`: Use `/input_stream/*` instead.

## Speech-to-Text API Endpoints

### File-based STT

- **`GET /technologies/stt` (asynchronous)**

  - Performs STT on a specified audio file.
  - Params:
    - `path` (required): Path to the audio file.
    - `model` (required): STT model name (e.g., `EN_US_6`).
    - `result_type` (optional): `one_best` (default), `n_best`, `confusion_network`. Multiple types comma-separated.
    - `cache_only`, `cache_disable` (optional): Control caching.
    - `from_time`, `to_time` (optional): Process specific time range (disables caching).
  - Response (via `/done/{id}`): `SpeechRecognitionResult` (if multiple `result_type`) or specific result type name (if single `result_type`), containing transcription(s), timings, confidence scores.

- **`POST /technologies/stt` (asynchronous)**
  - Performs STT on a specified audio file, allowing preferred phrases and dictionary additions.
  - **Does not use cache.**
  - Params: Same as `GET /technologies/stt` (path, model, result_type, from_time, to_time).
  - Body (JSON/XML): Contains `preferred_phrases` object with optional `phrases` array and `dictionary` array.
    - `phrases`: Array of `{"phrase": "text..."}`. Can include `$class_name`.
    - `dictionary`: Array of `{"word": "word_text", "pronunciations": [{"phonemes": "p h o n e m e s"}, ...]}`. Pronunciations optional (auto-generated if omitted).
  - Response (via `/done/{id}`): Similar to GET, but includes the input `phrases` and the processed `dictionary` (with auto-generated pronunciations, OOV status, warnings).

### Stream-based STT

- **`POST /technologies/stt/input_stream`**

  - Binds STT processing to an existing input stream.
  - Params:
    - `input_stream` (required): ID of the stream opened via `/input_stream/*`.
    - `model` (required): STT model name.
    - `result_mode` (optional): `complete` (default) or `incremental`.
  - Body (Optional, JSON/XML): Same structure as `POST /technologies/stt` for preferred phrases/dictionary.
  - Response: `UserStreamTaskInfoResult` containing the `task_id` for this STT stream task.

- **`GET /technologies/stt/input_stream`**

  - Retrieves partial/final results for an STT stream task (polling).
  - Also used for WebSocket handshake or Webhook registration.
  - Params:
    - `task` (required): The `task_id` from the POST request.
    - `interval` (optional): Interval (seconds) for WebSocket/Webhook updates (default 0 = immediate).
    - `trigger_events` (optional, for WebSocket/Webhook): Comma-separated list (`start_segment`, `end_segment`, `transcription`) defining when to push results (default `transcription`).
  - Response: `SpeechRecognitionOnlineResult` containing partial/final transcription, `is_last` flag, `delete_n_words` (for incremental), `sentence_info` (sentence confidence), and potentially `n_best_result` if supported/configured. Includes `phrases` and `dictionary` if provided in POST.

- **`DELETE /technologies/stt/input_stream?task={task_id}`**
  - Stops the STT analysis on the specified stream task.

### STT Dictionary & Customization

- **`POST /technologies/stt/checkdictionary` (asynchronous)**

  - Checks a list of words/pronunciations against a model's dictionary.
  - Params: `model` (required).
  - Body (JSON/XML): `dictionary` array (same structure as in `POST /technologies/stt`).
  - Response (via `/done/{id}`): `SpeechToTextCheckDictionaryResult` containing the input words enriched with pronunciations (including auto-generated ones), `out_of_vocabulary` status, `class`, and potential `warning_message`.

- **`GET /technologies/stt/classes`**

  - Returns supported word class names (e.g., `$male_first_name_nominative`) for a given model, if supported.
  - Params: `model` (required).
  - Response: `SpeechRecognitionClassesResult` listing class names.

- **`GET /technologies/stt/graphemes`**

  - Returns the set of allowed characters (graphemes) for defining words in a specific model.
  - Params: `model` (required).
  - Response: `GraphemeResult` listing graphemes and the `word_separator`.

- **`GET /technologies/stt/phonemes`**

  - Returns the set of allowed phonemes for defining pronunciations in a specific model.
  - Params: `model` (required).
  - Response: `PhonemesListResult` listing phonemes.

- **`GET /technologies/stt/models` (Beta)**

  - Lists available built-in and custom STT models.
  - Response: `SpeechRecognitionModelsResult`.

- **`POST /technologies/stt/models/{builtin_model}` (Beta, asynchronous)**

  - Creates a new customized STT model from a built-in model and a custom dictionary.
  - Params: `builtin_model` (path), `suffix` (query, required).
  - Body (JSON/XML): `dictionary` array (same structure as in `POST /technologies/stt`).
  - Response: Async task ID. Result (via `/done/{id}`) indicates success/failure. _Note: Requires server restart and manual configuration to use the new model._

- **`GET /technologies/stt/models/{model}` (Beta)**

  - Retrieves the custom dictionary associated with a customized model. Returns empty for built-in models.
  - Params: `model` (path).
  - Response: `SpeechRecognitionModelResult`.

- **`DELETE /technologies/stt/models/{customized_model}` (Beta)**
  - Deletes a customized STT model. Cannot delete built-in models or models currently configured/running.
  - Params: `customized_model` (path).

### Deprecated STT Endpoints

- **`/technologies/dictate`**: Deprecated since v3.18. Use `/technologies/stt/input_stream`.
- **`/technologies/stt/stream`**: Deprecated since v3.23. Use `/technologies/stt/input_stream`.
