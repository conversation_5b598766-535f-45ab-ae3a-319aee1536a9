using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Broadcasts audio data from a source IAudioBuffer to multiple target IAudioBuffer instances.
    /// This component does not implement IAudioBuffer to avoid ambiguous producer/consumer semantics;
    /// it is a one-way tee that reads from a source and fans out to targets.
    /// </summary>
    public class AudioBufferBroadcaster : IDisposable
    {
        private readonly ILogger<AudioBufferBroadcaster> _logger;
        private readonly string _callId;
        private bool _disposed;

        public AudioBufferBroadcaster(string callId, ILogger<AudioBufferBroadcaster> logger)
        {
            _callId = callId ?? throw new ArgumentNullException(nameof(callId));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Starts broadcasting audio frames from source buffer to all target buffers.
        /// Lifecycle:
        /// - Reads frames until the source completes and is drained, or cancellation is requested.
        /// - On exit, calls CompleteAdding on all target buffers to signal end-of-stream.
        /// </summary>
        public async Task StartBroadcastingAsync(
            IAudioBuffer sourceBuffer,
            IReadOnlyList<IAudioBuffer> targetBuffers,
            CancellationToken cancellationToken)
        {
            if (sourceBuffer == null) throw new ArgumentNullException(nameof(sourceBuffer));
            if (targetBuffers == null) throw new ArgumentNullException(nameof(targetBuffers));

            _logger.LogInformation("[{CallId}] Broadcaster starting with {Targets} targets.", _callId, targetBuffers.Count);

            try
            {
                while (!sourceBuffer.IsCompleted || sourceBuffer.TryTake(out _, 0, CancellationToken.None))
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        _logger.LogInformation("[{CallId}] Broadcaster cancellation requested.", _callId);
                        break;
                    }

                    if (sourceBuffer.TryTake(out var audioData, 500, cancellationToken))
                    {
                        // Fan-out to all targets
                        for (int i = 0; i < targetBuffers.Count; i++)
                        {
                            try
                            {
                                targetBuffers[i].Add(audioData);
                            }
                            catch (ObjectDisposedException)
                            {
                                // Ignore disposed target
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "[{CallId}] Failed to add audio to target buffer {Index}.", _callId, i);
                            }
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("[{CallId}] Broadcaster cancelled.", _callId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{CallId}] Error during broadcasting.", _callId);
            }
            finally
            {
                // Signal completion to all targets
                foreach (var buf in targetBuffers)
                {
                    try
                    {
                        buf.CompleteAdding();
                    }
                    catch (ObjectDisposedException)
                    {
                        // Ignore
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[{CallId}] Error completing target buffer.", _callId);
                    }
                }

                _logger.LogInformation("[{CallId}] Broadcaster stopped.", _callId);
            }

            // Yield once to avoid synchronous completion surprises
            await Task.Yield();
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            GC.SuppressFinalize(this);
        }
    }
}