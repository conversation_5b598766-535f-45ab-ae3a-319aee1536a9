using System;

namespace voice_processing_service.Configuration
{
    public sealed class WavRecordingOptions
    {
        public bool Enabled { get; set; } = false;
        public string? Directory { get; set; } = "/app/recordings";
        public bool SplitPerDirection { get; set; } = true;
        public bool SplitOnReinvite { get; set; } = true;
        public int MaxFileDurationSeconds { get; set; } = 3600;
        public string? FilenameTemplate { get; set; } = "{date}/{utcStart:yyyyMMddTHHmmssZ}_call-{callId}_seg-{segmentIndex:000}.wav";
        public int QueueCapacityFrames { get; set; } = 200;
        public bool SafeFinalize { get; set; } = true;
        public bool IncludeNumbersInFilename { get; set; } = false;
    }
}