# Implementace audio bufferu

## Popis úkolu

Tento úkol zahrnuje implementaci thread-safe bufferu pro audio data, k<PERSON><PERSON> bude sloužit jako prostředník mezi přijímačem audio dat (RTP) a procesorem audio dat (WAV, STT). Buffer musí být schopen vyrovnávat rozdílné rychlosti zpracování dat mezi přijímačem a procesorem.

## Technické detaily

### Implementace BlockingCollectionAudioBuffer

Implementace bude využívat `BlockingCollection<T>` z .NET, která poskytuje thread-safe frontu s možností blokujícího čtení a zápisu.

```csharp
using System;
using System.Collections.Concurrent;
using System.Threading;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;

namespace voice_processing_service.Services
{
    /// <summary>
    /// Implementace audio bufferu pomocí BlockingCollection.
    /// </summary>
    public class BlockingCollectionAudioBuffer : IAudioBuffer
    {
        private readonly BlockingCollection<byte[]> _buffer = new BlockingCollection<byte[]>(new ConcurrentQueue<byte[]>());
        private readonly ILogger<BlockingCollectionAudioBuffer> _logger;
        private readonly string _instanceId = Guid.NewGuid().ToString("N").Substring(0, 6); // Pro logování

        /// <summary>
        /// Inicializuje novou instanci třídy <see cref="BlockingCollectionAudioBuffer"/>.
        /// </summary>
        /// <param name="logger">Logger pro logování událostí.</param>
        public BlockingCollectionAudioBuffer(ILogger<BlockingCollectionAudioBuffer> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _logger.LogTrace($"[{_instanceId}] Buffer created.");
        }

        /// <inheritdoc/>
        public void Add(byte[] audioData)
        {
            if (!_buffer.IsAddingCompleted)
            {
                try
                {
                    _buffer.Add(audioData);
                    // _logger.LogTrace($"[{_instanceId}] Added {audioData.Length} bytes to buffer. Current count: {_buffer.Count}");
                }
                catch (InvalidOperationException)
                {
                    // Může nastat, pokud se zavolá CompleteAdding a pak Add
                    _logger.LogWarning($"[{_instanceId}] Attempted to add data after CompleteAdding was called.");
                }
            }
        }

        /// <inheritdoc/>
        public bool TryTake(out byte[] audioData, int millisecondsTimeout, CancellationToken cancellationToken)
        {
            // _logger.LogTrace($"[{_instanceId}] Attempting to take data from buffer. Current count: {_buffer.Count}");
            return _buffer.TryTake(out audioData, millisecondsTimeout, cancellationToken);
        }

        /// <inheritdoc/>
        public void CompleteAdding()
        {
            if (!_buffer.IsAddingCompleted)
            {
                _logger.LogInformation($"[{_instanceId}] Completing adding to buffer.");
                _buffer.CompleteAdding();
            }
        }

        /// <inheritdoc/>
        public bool IsAddingCompleted => _buffer.IsAddingCompleted;

        /// <inheritdoc/>
        public bool IsCompleted => _buffer.IsCompleted;

        /// <inheritdoc/>
        public void Dispose()
        {
            _logger.LogTrace($"[{_instanceId}] Disposing buffer.");
            CompleteAdding(); // Zajistí, že konzumenti přestanou čekat
            _buffer.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
```

### Registrace v DI kontejneru

Buffer bude registrován v DI kontejneru jako transient služba, protože každý hovor bude mít svůj vlastní buffer.

```csharp
// V Program.cs nebo v extension metodě pro IServiceCollection
services.AddTransient<IAudioBuffer, BlockingCollectionAudioBuffer>();
```

## Testovací scénáře

### Unit testy pro BlockingCollectionAudioBuffer

1. **Test konstruktoru**
   - Ověřit, že instance BlockingCollectionAudioBuffer je vytvořena bez chyb
   - Ověřit, že logger je správně inicializován

2. **Test metody Add**
   - Ověřit, že data lze přidat do bufferu
   - Ověřit, že po zavolání CompleteAdding nelze přidat další data

3. **Test metody TryTake**
   - Ověřit, že data lze získat z bufferu
   - Ověřit, že TryTake blokuje, dokud nejsou data k dispozici
   - Ověřit, že TryTake respektuje timeout
   - Ověřit, že TryTake respektuje cancellation token

4. **Test metody CompleteAdding**
   - Ověřit, že po zavolání CompleteAdding je IsAddingCompleted true
   - Ověřit, že po zavolání CompleteAdding a vyprázdnění bufferu je IsCompleted true

5. **Test metody Dispose**
   - Ověřit, že po zavolání Dispose je buffer správně uvolněn
   - Ověřit, že po zavolání Dispose nelze přidat ani získat data

### Integrační testy pro více vláken

1. **Test producent-konzument**
   - Vytvořit jedno vlákno, které přidává data do bufferu
   - Vytvořit druhé vlákno, které odebírá data z bufferu
   - Ověřit, že všechna data jsou správně přenesena z producenta ke konzumentovi

2. **Test více producentů a konzumentů**
   - Vytvořit několik vláken, která přidávají data do bufferu
   - Vytvořit několik vláken, která odebírají data z bufferu
   - Ověřit, že všechna data jsou správně přenesena od producentů ke konzumentům

3. **Test ukončení přidávání**
   - Vytvořit vlákno, které přidává data do bufferu a pak zavolá CompleteAdding
   - Vytvořit vlákno, které odebírá data z bufferu
   - Ověřit, že konzument správně detekuje ukončení přidávání a ukončí zpracování

## Implementační kroky

1. Implementovat třídu BlockingCollectionAudioBuffer
2. Implementovat unit testy pro BlockingCollectionAudioBuffer
3. Implementovat integrační testy pro více vláken
4. Registrovat buffer v DI kontejneru

## Simulace pro testování

Pro testování bufferu bude vytvořena jednoduchá konzolová aplikace, která simuluje producenta a konzumenta:

```csharp
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using voice_processing_service.Interfaces;
using voice_processing_service.Services;

namespace voice_processing_service.Tests
{
    class BufferSimulator
    {
        static async Task Main(string[] args)
        {
            // Vytvoření loggeru
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Trace);
            });
            var logger = loggerFactory.CreateLogger<BlockingCollectionAudioBuffer>();

            // Vytvoření bufferu
            using var buffer = new BlockingCollectionAudioBuffer(logger);

            // Vytvoření CancellationTokenSource pro ukončení simulace
            using var cts = new CancellationTokenSource();
            cts.CancelAfter(TimeSpan.FromSeconds(10)); // Simulace bude běžet 10 sekund

            // Spuštění producenta a konzumenta
            var producerTask = ProducerAsync(buffer, cts.Token);
            var consumerTask = ConsumerAsync(buffer, cts.Token);

            // Čekání na ukončení simulace
            try
            {
                await Task.WhenAll(producerTask, consumerTask);
                Console.WriteLine("Simulace úspěšně dokončena.");
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Simulace byla zrušena.");
            }
        }

        static async Task ProducerAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            try
            {
                var random = new Random();
                while (!cancellationToken.IsCancellationRequested)
                {
                    // Vytvoření náhodných audio dat
                    var audioData = new byte[320]; // 20ms G.711 při 8kHz
                    random.NextBytes(audioData);

                    // Přidání dat do bufferu
                    buffer.Add(audioData);
                    Console.WriteLine($"Producent: Přidáno {audioData.Length} bytů do bufferu.");

                    // Simulace prodlevy mezi pakety (20ms)
                    await Task.Delay(20, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Producent: Zrušen.");
            }
            finally
            {
                buffer.CompleteAdding();
                Console.WriteLine("Producent: Ukončeno přidávání do bufferu.");
            }
        }

        static async Task ConsumerAsync(IAudioBuffer buffer, CancellationToken cancellationToken)
        {
            try
            {
                long totalBytesProcessed = 0;
                while (!buffer.IsCompleted)
                {
                    // Pokus o získání dat z bufferu
                    if (buffer.TryTake(out var audioData, 100, cancellationToken))
                    {
                        // Zpracování dat
                        totalBytesProcessed += audioData.Length;
                        Console.WriteLine($"Konzument: Zpracováno {audioData.Length} bytů. Celkem: {totalBytesProcessed} bytů.");

                        // Simulace zpracování dat (může být rychlejší nebo pomalejší než producent)
                        await Task.Delay(random.Next(10, 30), cancellationToken);
                    }
                }
                Console.WriteLine($"Konzument: Buffer je prázdný a přidávání ukončeno. Celkem zpracováno {totalBytesProcessed} bytů.");
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Konzument: Zrušen.");
            }
        }
    }
}
```

Tato simulace umožní otestovat chování bufferu v reálném scénáři s producentem a konzumentem běžícími v různých vláknech a s různými rychlostmi zpracování.
