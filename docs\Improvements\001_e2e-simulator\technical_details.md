# Technické detaily SIPSorcery a správného nastavení portů

Tento dokument poskytuje technické detaily o knihovně SIPSorcery a správném nastavení portů pro SIP a RTP komunikaci.

## 1. SIPSorcery - přehled

SIPSorcery je knihovna pro .NET, která poskytuje implementaci protokolů SIP (Session Initiation Protocol) a RTP (Real-time Transport Protocol). Tyto protokoly jsou základem pro VoIP (Voice over IP) komunikaci.

### 1.1 Klíčové komponenty SIPSorcery

- **SIPTransport**: Zajišťuje přenos SIP zpráv přes různé transportní protokoly (UDP, TCP, TLS, WebSocket)
- **SIPChannel**: Reprezentuje konkrétní kanál pro přenos SIP zpráv (např. SIPUDPChannel, SIPTCPChannel)
- **SIPUserAgent**: <PERSON>bstrakce pro SIP User Agent, k<PERSON><PERSON> může být klient (UAC) nebo server (UAS)
- **SIPClientUserAgent**: Implementace klientského SIP User Agenta
- **SIPServerUserAgent**: Implementace serverového SIP User Agenta
- **VoIPMediaSession**: Spravuje RTP relaci pro přenos médií (audio, video)
- **SDP**: Session Description Protocol, používá se pro vyjednávání parametrů médií

### 1.2 Verze SIPSorcery

Aktuální implementace používá SIPSorcery verze 8.0.11. Tato verze přináší několik změn oproti předchozím verzím, zejména v oblasti konfigurace RTP portů a vytváření SDP.

## 2. SIP a RTP porty

### 2.1 SIP porty

SIP (Session Initiation Protocol) používá standardně port 5060 pro nezabezpečenou komunikaci (UDP/TCP) a port 5061 pro zabezpečenou komunikaci (TLS). Tyto porty jsou používány pro signalizaci - navázání, řízení a ukončení hovoru.

V SIPSorcery se SIP port konfiguruje při vytváření SIP kanálu:

```csharp
var sipChannel = new SIPUDPChannel(IPAddress.Any, 5060);
sipTransport.AddSIPChannel(sipChannel);
```

### 2.2 RTP porty

RTP (Real-time Transport Protocol) používá dynamicky přidělované porty pro přenos médií (audio, video). Standardně se používají sudá čísla portů v rozsahu 10000-20000. Pro každý RTP stream se používají dva porty - jeden pro RTP a jeden pro RTCP (RTP Control Protocol).

V SIPSorcery se RTP porty konfigurují při vytváření VoIPMediaSession:

```csharp
var rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioSource },
    RtpPortRange = new PortRange(10000, 20000),
});
```

### 2.3 Důležitost správného nastavení portů

Správné nastavení portů je klíčové pro úspěšnou komunikaci mezi SIP klienty a servery. Pokud jsou porty nesprávně nakonfigurovány, může dojít k následujícím problémům:

1. **Konflikt portů**: Pokud více aplikací používá stejné porty, dojde ke konfliktu a aplikace se nespustí
2. **Blokování firewallem**: Pokud jsou porty blokovány firewallem, komunikace nebude fungovat
3. **Nesprávné SDP**: Pokud porty v SDP neodpovídají portům, na kterých aplikace skutečně naslouchá, komunikace nebude fungovat

## 3. SDP (Session Description Protocol)

SDP je protokol používaný pro popis multimediálních relací. V kontextu SIP se používá pro vyjednávání parametrů médií mezi klientem a serverem.

### 3.1 Vytváření SDP v SIPSorcery

V SIPSorcery se SDP vytváří pomocí metody `CreateOffer` třídy `VoIPMediaSession`:

```csharp
var offerSDP = rtpSession.CreateOffer(IPAddress.Any);
```

Tato metoda vytvoří SDP nabídku, která obsahuje informace o podporovaných kodecích, formátech médií a portech, na kterých aplikace naslouchá pro RTP komunikaci.

### 3.2 Porty v SDP

Porty v SDP jsou definovány v sekci média (m=). Například:

```
m=audio 30000 RTP/AVP 0 8 101
```

Tato řádka říká, že audio bude přenášeno na portu 30000 pomocí RTP/AVP (RTP Audio/Video Profile) a podporuje kodeky 0 (PCMU), 8 (PCMA) a 101 (telefonní události).

### 3.3 Důležitost souladu portů v SDP a skutečných portů

Je důležité, aby porty uvedené v SDP odpovídaly portům, na kterých aplikace skutečně naslouchá. Pokud tomu tak není, druhá strana bude posílat RTP pakety na porty uvedené v SDP, ale aplikace je nebude přijímat, protože naslouchá na jiných portech.

V SIPSorcery je tento soulad zajištěn správným nastavením `RtpPortRange` v `VoIPMediaSessionConfig`. Porty z tohoto rozsahu jsou pak použity při vytváření SDP.

## 4. Konfigurace portů v našem projektu

### 4.1 Server (voice-processing-service)

Server má konfiguraci portů v souboru `appsettings.json`:

```json
"SipServer": {
  "ListenIpAddress": "Any",
  "ListenPort": 5060,
  "RtpPortMin": 30002,
  "RtpPortMax": 30010,
  "WavRecordingDirectory": "RecordedCalls"
}
```

Tyto hodnoty jsou načteny do třídy `SipServerOptions` a použity při vytváření SIP kanálu a VoIPMediaSession. Konfiguraci lze přepsat pomocí proměnných prostředí:

```bash
# Windows
set SIPSERVER__LISTENPORT=5061
set SIPSERVER__RTPPORTMIN=31000
set SIPSERVER__RTPPORTMAX=31010

# Linux/macOS
export SIPSERVER__LISTENPORT=5061
export SIPSERVER__RTPPORTMIN=31000
export SIPSERVER__RTPPORTMAX=31010
```

ASP.NET Core automaticky nahradí dvojtečky v názvech sekcí konfigurace dvěma podtržítky v názvech proměnných prostředí.

### 4.2 Simulátor (voice-processing-simulator)

Simulátor má porty pevně nastavené v kódu:

```csharp
var sipChannel = new SIPSorcery.SIP.SIPUDPChannel(System.Net.IPAddress.Any, 5070);
```

```csharp
_rtpSession = new VoIPMediaSession(new VoIPMediaSessionConfig
{
    MediaEndPoint = new MediaEndPoints { AudioSource = audioExtrasSource },
    RtpPortRange = new PortRange(25000, 25010),
});
```

Naše úpravy umožní konfiguraci těchto portů pomocí parametrů příkazové řádky.

## 5. Běh více instancí na jedné stanici

### 5.1 Požadavky pro běh více instancí

Pro běh více instancí serveru nebo simulátoru na jedné stanici je potřeba zajistit:

1. **Unikátní SIP porty**: Každá instance musí mít vlastní SIP port
2. **Unikátní rozsahy RTP portů**: Rozsahy RTP portů se nesmí překrývat
3. **Správné nastavení SDP**: Porty v SDP musí odpovídat portům, na kterých instance skutečně naslouchá

### 5.2 Příklad konfigurace pro více instancí

#### Server 1:
- SIP port: 5060
- RTP porty: 30000-30100

#### Server 2:
- SIP port: 5061
- RTP porty: 31000-31100

#### Simulátor 1:
- SIP port: 5070
- RTP porty: 25000-25100
- Cíl: Server 1 (127.0.0.1:5060)

#### Simulátor 2:
- SIP port: 5071
- RTP porty: 26000-26100
- Cíl: Server 2 (127.0.0.1:5061)

## 6. Řešení problémů

### 6.1 Konflikt portů

Pokud dojde ke konfliktu portů, aplikace se nespustí s chybou podobnou:

```
System.Net.Sockets.SocketException: Only one usage of each socket address (protocol/network address/port) is normally permitted
```

Řešení:
1. Použijte jiný port
2. Ukončete aplikaci, která používá daný port
3. Počkejte, až se port uvolní (může trvat až několik minut po ukončení aplikace)

### 6.2 Problémy s firewallem

Pokud jsou porty blokovány firewallem, komunikace nebude fungovat. Řešení:

1. Povolte potřebné porty ve firewallu
2. Použijte porty, které jsou již povoleny

### 6.3 Nesprávné SDP

Pokud porty v SDP neodpovídají portům, na kterých aplikace skutečně naslouchá, komunikace nebude fungovat. Řešení:

1. Ujistěte se, že `RtpPortRange` v `VoIPMediaSessionConfig` je správně nastaven
2. Zkontrolujte SDP v logu a ověřte, že porty odpovídají očekávaným hodnotám

### 6.4 Problémy s NAT

Pokud je aplikace za NAT (Network Address Translation), může být potřeba další konfigurace. Řešení:

1. Použijte STUN (Session Traversal Utilities for NAT) pro zjištění veřejné IP adresy
2. Nakonfigurujte přesměrování portů na routeru
3. Použijte TURN (Traversal Using Relays around NAT) pro přenos médií přes NAT

## 7. Doporučení pro produkční nasazení

Pro produkční nasazení doporučujeme:

1. **Použití specifických IP adres**: Místo `IPAddress.Any` použijte konkrétní IP adresu serveru
2. **Větší rozsah RTP portů**: Pro podporu více současných hovorů použijte větší rozsah RTP portů
3. **Zabezpečení komunikace**: Použijte TLS pro SIP a SRTP pro RTP
4. **Monitorování portů**: Implementujte monitorování portů pro detekci problémů
5. **Automatické testy**: Implementujte automatické testy pro ověření funkčnosti

## 8. Závěr

Správná konfigurace portů je klíčová pro úspěšnou komunikaci mezi SIP klienty a servery. Naše úpravy umožní konfiguraci portů pro server i simulátor, což umožní běh obou komponent na jedné stanici.

Při implementaci těchto úprav je důležité dbát na:

1. Správné nastavení SIP portů
2. Správné nastavení RTP portů
3. Soulad portů v SDP a skutečných portů
4. Unikátní porty pro každou instanci
5. Kompatibilitu s aktuální verzí SIPSorcery (8.0.11)
