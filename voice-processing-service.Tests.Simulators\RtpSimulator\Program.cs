using System;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;

namespace voice_processing_service.Tests.Simulators.RtpSimulator
{
    class RtpSimulator
    {
        static async Task Main(string[] args)
        {
            if (args.Length < 3)
            {
                Console.WriteLine("Usage: RtpSimulator <destination_ip> <rtp_port> <wav_file>");
                return;
            }

            string destinationIp = args[0];
            int rtpPort = int.Parse(args[1]);
            string wavFile = args[2];

            // Vytvoření UDP klienta pro odesílání RTP paketů
            using var udpClient = new UdpClient();
            var destinationEndPoint = new IPEndPoint(IPAddress.Parse(destinationIp), rtpPort);

            // Načtení WAV souboru
            byte[] wavData = await File.ReadAllBytesAsync(wavFile);
            
            // Přeskočení WAV hlavičky (44 bytů)
            int offset = 44;
            
            // Parametry RTP
            ushort sequenceNumber = 0;
            uint timestamp = 0;
            uint ssrc = (uint)new Random().Next();
            
            // Velikost RTP paketu (20ms G.711 při 8kHz = 160 vzorků = 160 bytů)
            const int payloadSize = 160;
            
            Console.WriteLine($"Sending RTP packets to {destinationEndPoint}...");
            
            // Odesílání RTP paketů
            while (offset < wavData.Length)
            {
                // Vytvoření RTP hlavičky
                byte[] rtpHeader = new byte[12];
                rtpHeader[0] = 0x80; // Version=2, P=0, X=0, CC=0
                rtpHeader[1] = 0x00; // M=0, PT=0 (PCMU/G.711u)
                rtpHeader[2] = (byte)(sequenceNumber >> 8);
                rtpHeader[3] = (byte)(sequenceNumber & 0xFF);
                rtpHeader[4] = (byte)(timestamp >> 24);
                rtpHeader[5] = (byte)(timestamp >> 16);
                rtpHeader[6] = (byte)(timestamp >> 8);
                rtpHeader[7] = (byte)(timestamp & 0xFF);
                rtpHeader[8] = (byte)(ssrc >> 24);
                rtpHeader[9] = (byte)(ssrc >> 16);
                rtpHeader[10] = (byte)(ssrc >> 8);
                rtpHeader[11] = (byte)(ssrc & 0xFF);
                
                // Vytvoření RTP paketu (hlavička + payload)
                int bytesToSend = Math.Min(payloadSize, wavData.Length - offset);
                byte[] rtpPacket = new byte[rtpHeader.Length + bytesToSend];
                Buffer.BlockCopy(rtpHeader, 0, rtpPacket, 0, rtpHeader.Length);
                Buffer.BlockCopy(wavData, offset, rtpPacket, rtpHeader.Length, bytesToSend);
                
                // Odeslání RTP paketu
                await udpClient.SendAsync(rtpPacket, rtpPacket.Length, destinationEndPoint);
                Console.WriteLine($"Sent RTP packet: seq={sequenceNumber}, ts={timestamp}, size={rtpPacket.Length}");
                
                // Aktualizace parametrů
                sequenceNumber++;
                timestamp += (uint)bytesToSend; // Pro G.711 je timestamp = počet vzorků = počet bytů
                offset += bytesToSend;
                
                // Pauza 20ms mezi pakety
                await Task.Delay(20);
            }
            
            Console.WriteLine("All RTP packets sent.");
        }
    }
}